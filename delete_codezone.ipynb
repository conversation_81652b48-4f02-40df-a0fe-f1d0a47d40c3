{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["16:04:30 heracles:INFO:logger.py:74 Heracles is starting, current log_level is INFO\n", "/Users/<USER>/miniconda3/envs/clackyai/lib/python3.11/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n", "16:04:34 heracles:INFO:llm.py:31 langfuse is enabled, load config\n", "16:04:34 heracles:INFO:redis_cache.py:44 [cache] feature is disabled\n"]}], "source": ["from heracles.agent_workspace.paas_sdk.utils import bind_middleware_to_codezone, fork_codezone, get_playground_id, stop_playground, delete_codezone  # noqa: E501\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True, 'data': {'status': 'success'}, 'error': None}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["await delete_codezone('762102839328833536')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["ids = [\"762095901287317504\",\n", "\"762096308021559296\",\n", "\"762096502364672000\",\n", "\"762097331461095424\",\n", "\"762098061735596032\",\n", "\"762098788985929728\",\n", "\"762098991264665600\",\n", "\"762099187390283776\",]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[31m17:17:30 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762095901287317504)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:30 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762096308021559296)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:31 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762096502364672000)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:32 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762097331461095424)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:33 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762098061735596032)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:33 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762098788985929728)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:34 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762098991264665600)不存在'}]}\u001b[0m\n", "\u001b[31m17:17:35 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:762099187390283776)不存在'}]}\u001b[0m\n"]}], "source": ["for i in ids:\n", "    await delete_codezone(i)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}