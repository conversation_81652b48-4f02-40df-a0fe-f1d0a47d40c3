from heracles.agent_workspace.paas_sdk.utils import fork_new_codezone_get_id,get_playground_info,get_playground_id_from_codezone_id, delete_codezone

await get_playground_info(802692854501003264)

old_codezone_id = 763146446240661504

await fork_new_codezone_get_id(old_codezone_id)

await get_playground_id_from_codezone_id(802665929048719360)

await delete_codezone(802690152253227008)

new_playground_id = 802617428567052288

from heracles.core.schema.models import (
    TestCaseRuleModels,
    ErrorFoundModel,
    ErrorReportModel
)

d = {
  "summary": "The application started successfully with one deprecation warning and one informational message. The `punycode` module is deprecated, and the `caniuse-lite` database is outdated. These do not appear to be critical issues but should be addressed.",
  "errors": [
    {
      "title": "Punycode Module Deprecation",
      "content": "(node:873) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.",
      "severity": "low"
    },
    {
      "title": "Outdated Browserslist",
      "content": "Browserslist: caniuse-lite is outdated. Please run:\n  npx update-browserslist-db@latest",
      "severity": "info"
    }
  ]
}

ErrorReportModel(**d)

