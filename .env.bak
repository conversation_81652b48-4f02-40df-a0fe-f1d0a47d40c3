# IDEServer 连接参数设置
PAAS_DOMAIN_URL="https://develop.clackypaas.com"
PAAS_TENANT_CODE="demo"

# LLM 参数配置
# strong 版本, 最强但最贵和慢, 用于核心推理
LLM_MODEL="gpt-4o"
LLM_API_KEY="sk-,+@hc=F%NHwV3pbbD]:F:tu3nak)E-YK}rhwq6c=eCYYMA5U+YBp!d7sv}CypTr*hr_Hkcjm3phQ7af,cFLmjZgp^%+k%@)?+Bho"
LLM_BASE_URL="https://proxy.clackyai.com/"
# LLM_BASE_URL="https://api.openai.com/v1/"
# weak 版本, 最弱但最快, 用于代码补全等高频操作
LLM_WEAK_MODEL_OPTIONAL="gpt-4o-mini"
LLM_WEAK_API_KEY_OPTIONAL="sk-,+@hc=F%NHwV3pbbD]:F:tu3nak)E-YK}rhwq6c=eCYYMA5U+YBp!d7sv}CypTr*hr_Hkcjm3phQ7af,cFLmjZgp^%+k%@)?+Bho"
LLM_WEAK_BASE_URL_OPTIONAL="https://proxy.clackyai.com/"
# normal 版本, 中等, 用于编码等
LLM_NORMAL_MODEL_OPTIONAL="gpt-4o-mini"
LLM_NORMAL_API_KEY_OPTIONAL="sk-,+@hc=F%NHwV3pbbD]:F:tu3nak)E-YK}rhwq6c=eCYYMA5U+YBp!d7sv}CypTr*hr_Hkcjm3phQ7af,cFLmjZgp^%+k%@)?+Bho"
LLM_NORMAL_BASE_URL_OPTIONAL="https://proxy.clackyai.com/"
# LANGFUSE
LANGFUSE_PUBLIC_KEY_OPTIONAL="pk-lf-31fa522f-fd9d-4169-aed4-f33acde38ba1"
LANGFUSE_SECRET_KEY_OPTIONAL="******************************************"
LANGFUSE_HOST_OPTIONAL="https://us.cloud.langfuse.com"
LANGFUSE_PROJECT_OPTIONAL="cxd"

# 日志等级: 'debug' or 'info'
LOG_LEVEL_OPTIONAL='debug'

# 这个是可选变量示例, 不要删除, 以 _OPTIONAL 结局系统不会强制检查配置
XXX_OPTIONAL=''

# 向量数据库的配置
ADMIN_VECTOR_DB_HOST_OPTIONAL="http://************/"
ADMIN_VECTOR_DB_PORT_OPTIONAL="30633"
ADMIN_VECTOR_DB_SPARSE_VECTOR_NAME_OPTIONAL="bm25"
ADMIN_VECTOR_DB_DENSE_VECTOR_NAME_OPTIONAL="text-embedding-3-small"
ADMIN_VECTOR_DB_DENSE_DIM_OPTIONAL="1536"
ADMIN_SPARSE_EMBEDDING_MODEL_OPTIONAL="Qdrant/bm25"
ADMIN_DENSE_EMBEDDING_MODEL_OPTIONAL="text-embedding-3-small"
ADMIN_VECTOR_DB_API_KEY_OPTIONAL="sk-hV23KnK2T5"