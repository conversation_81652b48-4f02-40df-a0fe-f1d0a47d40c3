{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['no_proxy'] = 'localhost,127.0.0.1'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset('princeton-nlp/SWE-bench_Lite')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['repo', 'instance_id', 'base_commit', 'patch', 'test_patch', 'problem_statement', 'hints_text', 'created_at', 'version', 'FAIL_TO_PASS', 'PASS_TO_PASS', 'environment_setup_commit'],\n", "    num_rows: 300\n", "})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset['test']"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'repo': 'astropy/astropy', 'instance_id': 'astropy__astropy-12907', 'base_commit': 'd16bfe05a744909de4b27f5875fe0d4ed41ce607', 'patch': \"diff --git a/astropy/modeling/separable.py b/astropy/modeling/separable.py\\n--- a/astropy/modeling/separable.py\\n+++ b/astropy/modeling/separable.py\\n@@ -242,7 +242,7 @@ def _cstack(left, right):\\n         cright = _coord_matrix(right, 'right', noutp)\\n     else:\\n         cright = np.zeros((noutp, right.shape[1]))\\n-        cright[-right.shape[0]:, -right.shape[1]:] = 1\\n+        cright[-right.shape[0]:, -right.shape[1]:] = right\\n \\n     return np.hstack([cleft, cright])\\n \\n\", 'test_patch': \"diff --git a/astropy/modeling/tests/test_separable.py b/astropy/modeling/tests/test_separable.py\\n--- a/astropy/modeling/tests/test_separable.py\\n+++ b/astropy/modeling/tests/test_separable.py\\n@@ -28,6 +28,13 @@\\n p1 = models.Polynomial1D(1, name='p1')\\n \\n \\n+cm_4d_expected = (np.array([False, False, True, True]),\\n+                  np.array([[True,  True,  False, False],\\n+                            [True,  True,  False, False],\\n+                            [False, False, True,  False],\\n+                            [False, False, False, True]]))\\n+\\n+\\n compound_models = {\\n     'cm1': (map3 & sh1 | rot & sh1 | sh1 & sh2 & sh1,\\n             (np.array([False, False, True]),\\n@@ -52,7 +59,17 @@\\n     'cm7': (map2 | p2 & sh1,\\n             (np.array([False, True]),\\n              np.array([[True, False], [False, True]]))\\n-            )\\n+            ),\\n+    'cm8': (rot & (sh1 & sh2), cm_4d_expected),\\n+    'cm9': (rot & sh1 & sh2, cm_4d_expected),\\n+    'cm10': ((rot & sh1) & sh2, cm_4d_expected),\\n+    'cm11': (rot & sh1 & (scl1 & scl2),\\n+             (np.array([False, False, True, True, True]),\\n+              np.array([[True,  True,  False, False, False],\\n+                        [True,  True,  False, False, False],\\n+                        [False, False, True,  False, False],\\n+                        [False, False, False, True,  False],\\n+                        [False, False, False, False, True]]))),\\n }\\n \\n \\n\", 'problem_statement': \"Modeling's `separability_matrix` does not compute separability correctly for nested CompoundModels\\nConsider the following model:\\r\\n\\r\\n```python\\r\\nfrom astropy.modeling import models as m\\r\\nfrom astropy.modeling.separable import separability_matrix\\r\\n\\r\\ncm = m.Linear1D(10) & m.Linear1D(5)\\r\\n```\\r\\n\\r\\nIt's separability matrix as you might expect is a diagonal:\\r\\n\\r\\n```python\\r\\n>>> separability_matrix(cm)\\r\\narray([[ True, False],\\r\\n       [False,  True]])\\r\\n```\\r\\n\\r\\nIf I make the model more complex:\\r\\n```python\\r\\n>>> separability_matrix(m.Pix2Sky_TAN() & m.Linear1D(10) & m.Linear1D(5))\\r\\narray([[ True,  True, False, False],\\r\\n       [ True,  True, False, False],\\r\\n       [False, False,  True, False],\\r\\n       [False, False, False,  True]])\\r\\n```\\r\\n\\r\\nThe output matrix is again, as expected, the outputs and inputs to the linear models are separable and independent of each other.\\r\\n\\r\\nIf however, I nest these compound models:\\r\\n```python\\r\\n>>> separability_matrix(m.Pix2Sky_TAN() & cm)\\r\\narray([[ True,  True, False, False],\\r\\n       [ True,  True, False, False],\\r\\n       [False, False,  True,  True],\\r\\n       [False, False,  True,  True]])\\r\\n```\\r\\nSuddenly the inputs and outputs are no longer separable?\\r\\n\\r\\nThis feels like a bug to me, but I might be missing something?\\n\", 'hints_text': '', 'created_at': '2022-03-03T15:14:54Z', 'version': '4.3', 'FAIL_TO_PASS': '[\"astropy/modeling/tests/test_separable.py::test_separable[compound_model6-result6]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model9-result9]\"]', 'PASS_TO_PASS': '[\"astropy/modeling/tests/test_separable.py::test_coord_matrix\", \"astropy/modeling/tests/test_separable.py::test_cdot\", \"astropy/modeling/tests/test_separable.py::test_cstack\", \"astropy/modeling/tests/test_separable.py::test_arith_oper\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model0-result0]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model1-result1]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model2-result2]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model3-result3]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model4-result4]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model5-result5]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model7-result7]\", \"astropy/modeling/tests/test_separable.py::test_separable[compound_model8-result8]\", \"astropy/modeling/tests/test_separable.py::test_custom_model_separable\"]', 'environment_setup_commit': '298ccb478e6bf092953bca67a3d29dc6c35f6752'}\n", "{'repo': 'astropy/astropy', 'instance_id': 'astropy__astropy-14182', 'base_commit': 'a5917978be39d13cd90b517e1de4e7a539ffaa48', 'patch': 'diff --git a/astropy/io/ascii/rst.py b/astropy/io/ascii/rst.py\\n--- a/astropy/io/ascii/rst.py\\n+++ b/astropy/io/ascii/rst.py\\n@@ -27,7 +27,6 @@ def get_fixedwidth_params(self, line):\\n \\n \\n class SimpleRSTData(FixedWidthData):\\n-    start_line = 3\\n     end_line = -1\\n     splitter_class = FixedWidthTwoLineDataSplitter\\n \\n@@ -39,12 +38,29 @@ class RST(FixedWidth):\\n \\n     Example::\\n \\n-        ==== ===== ======\\n-        Col1  Col2  Col3\\n-        ==== ===== ======\\n-          1    2.3  Hello\\n-          2    4.5  Worlds\\n-        ==== ===== ======\\n+      >>> from astropy.table import QTable\\n+      >>> import astropy.units as u\\n+      >>> import sys\\n+      >>> tbl = QTable({\"wave\": [350, 950] * u.nm, \"response\": [0.7, 1.2] * u.count})\\n+      >>> tbl.write(sys.stdout,  format=\"ascii.rst\")\\n+      ===== ========\\n+       wave response\\n+      ===== ========\\n+      350.0      0.7\\n+      950.0      1.2\\n+      ===== ========\\n+\\n+    Like other fixed-width formats, when writing a table you can provide ``header_rows``\\n+    to specify a list of table rows to output as the header.  For example::\\n+\\n+      >>> tbl.write(sys.stdout,  format=\"ascii.rst\", header_rows=[\\'name\\', \\'unit\\'])\\n+      ===== ========\\n+       wave response\\n+         nm       ct\\n+      ===== ========\\n+      350.0      0.7\\n+      950.0      1.2\\n+      ===== ========\\n \\n     Currently there is no support for reading tables which utilize continuation lines,\\n     or for ones which define column spans through the use of an additional\\n@@ -57,10 +73,15 @@ class RST(FixedWidth):\\n     data_class = SimpleRSTData\\n     header_class = SimpleRSTHeader\\n \\n-    def __init__(self):\\n-        super().__init__(delimiter_pad=None, bookend=False)\\n+    def __init__(self, header_rows=None):\\n+        super().__init__(delimiter_pad=None, bookend=False, header_rows=header_rows)\\n \\n     def write(self, lines):\\n         lines = super().write(lines)\\n-        lines = [lines[1]] + lines + [lines[1]]\\n+        idx = len(self.header.header_rows)\\n+        lines = [lines[idx]] + lines + [lines[idx]]\\n         return lines\\n+\\n+    def read(self, table):\\n+        self.data.start_line = 2 + len(self.header.header_rows)\\n+        return super().read(table)\\n', 'test_patch': 'diff --git a/astropy/io/ascii/tests/test_rst.py b/astropy/io/ascii/tests/test_rst.py\\n--- a/astropy/io/ascii/tests/test_rst.py\\n+++ b/astropy/io/ascii/tests/test_rst.py\\n@@ -2,7 +2,11 @@\\n \\n from io import StringIO\\n \\n+import numpy as np\\n+\\n+import astropy.units as u\\n from astropy.io import ascii\\n+from astropy.table import QTable\\n \\n from .common import assert_almost_equal, assert_equal\\n \\n@@ -185,3 +189,27 @@ def test_write_normal():\\n ==== ========= ==== ====\\n \"\"\",\\n     )\\n+\\n+\\n+def test_rst_with_header_rows():\\n+    \"\"\"Round-trip a table with header_rows specified\"\"\"\\n+    lines = [\\n+        \"======= ======== ====\",\\n+        \"   wave response ints\",\\n+        \"     nm       ct     \",\\n+        \"float64  float32 int8\",\\n+        \"======= ======== ====\",\\n+        \"  350.0      1.0    1\",\\n+        \"  950.0      2.0    2\",\\n+        \"======= ======== ====\",\\n+    ]\\n+    tbl = QTable.read(lines, format=\"ascii.rst\", header_rows=[\"name\", \"unit\", \"dtype\"])\\n+    assert tbl[\"wave\"].unit == u.nm\\n+    assert tbl[\"response\"].unit == u.ct\\n+    assert tbl[\"wave\"].dtype == np.float64\\n+    assert tbl[\"response\"].dtype == np.float32\\n+    assert tbl[\"ints\"].dtype == np.int8\\n+\\n+    out = StringIO()\\n+    tbl.write(out, format=\"ascii.rst\", header_rows=[\"name\", \"unit\", \"dtype\"])\\n+    assert out.getvalue().splitlines() == lines\\n', 'problem_statement': 'Please support header rows in RestructuredText output\\n### Description\\r\\n\\r\\nIt would be great if the following would work:\\r\\n\\r\\n```Python\\r\\n>>> from astropy.table import QTable\\r\\n>>> import astropy.units as u\\r\\n>>> import sys\\r\\n>>> tbl = QTable({\\'wave\\': [350,950]*u.nm, \\'response\\': [0.7, 1.2]*u.count})\\r\\n>>> tbl.write(sys.stdout,  format=\"ascii.rst\")\\r\\n===== ========\\r\\n wave response\\r\\n===== ========\\r\\n350.0      0.7\\r\\n950.0      1.2\\r\\n===== ========\\r\\n>>> tbl.write(sys.stdout,  format=\"ascii.fixed_width\", header_rows=[\"name\", \"unit\"])\\r\\n|  wave | response |\\r\\n|    nm |       ct |\\r\\n| 350.0 |      0.7 |\\r\\n| 950.0 |      1.2 |\\r\\n>>> tbl.write(sys.stdout,  format=\"ascii.rst\", header_rows=[\"name\", \"unit\"])\\r\\nTraceback (most recent call last):\\r\\n  File \"<stdin>\", line 1, in <module>\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/table/connect.py\", line 129, in __call__\\r\\n    self.registry.write(instance, *args, **kwargs)\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/io/registry/core.py\", line 369, in write\\r\\n    return writer(data, *args, **kwargs)\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/io/ascii/connect.py\", line 26, in io_write\\r\\n    return write(table, filename, **kwargs)\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/io/ascii/ui.py\", line 856, in write\\r\\n    writer = get_writer(Writer=Writer, fast_writer=fast_writer, **kwargs)\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/io/ascii/ui.py\", line 800, in get_writer\\r\\n    writer = core._get_writer(Writer, fast_writer, **kwargs)\\r\\n  File \"/usr/lib/python3/dist-packages/astropy/io/ascii/core.py\", line 1719, in _get_writer\\r\\n    writer = Writer(**writer_kwargs)\\r\\nTypeError: RST.__init__() got an unexpected keyword argument \\'header_rows\\'\\r\\n```\\r\\n\\r\\n\\r\\n### Additional context\\r\\n\\r\\nRestructuredText output is a great way to fill autogenerated documentation with content, so having this flexible makes the life easier `:-)`\\r\\n\\r\\n\\n', 'hints_text': '', 'created_at': '2022-12-16T11:13:37Z', 'version': '5.1', 'FAIL_TO_PASS': '[\"astropy/io/ascii/tests/test_rst.py::test_rst_with_header_rows\"]', 'PASS_TO_PASS': '[\"astropy/io/ascii/tests/test_rst.py::test_read_normal\", \"astropy/io/ascii/tests/test_rst.py::test_read_normal_names\", \"astropy/io/ascii/tests/test_rst.py::test_read_normal_names_include\", \"astropy/io/ascii/tests/test_rst.py::test_read_normal_exclude\", \"astropy/io/ascii/tests/test_rst.py::test_read_unbounded_right_column\", \"astropy/io/ascii/tests/test_rst.py::test_read_unbounded_right_column_header\", \"astropy/io/ascii/tests/test_rst.py::test_read_right_indented_table\", \"astropy/io/ascii/tests/test_rst.py::test_trailing_spaces_in_row_definition\", \"astropy/io/ascii/tests/test_rst.py::test_write_normal\"]', 'environment_setup_commit': '5f74eacbcc7fff707a44d8eb58adaa514cb7dcb5'}\n", "{'repo': 'astropy/astropy', 'instance_id': 'astropy__astropy-14365', 'base_commit': '7269fa3e33e8d02485a647da91a5a2a60a06af61', 'patch': 'diff --git a/astropy/io/ascii/qdp.py b/astropy/io/ascii/qdp.py\\n--- a/astropy/io/ascii/qdp.py\\n+++ b/astropy/io/ascii/qdp.py\\n@@ -68,7 +68,7 @@ def _line_type(line, delimiter=None):\\n     _new_re = rf\"NO({sep}NO)+\"\\n     _data_re = rf\"({_decimal_re}|NO|[-+]?nan)({sep}({_decimal_re}|NO|[-+]?nan))*)\"\\n     _type_re = rf\"^\\\\s*((?P<command>{_command_re})|(?P<new>{_new_re})|(?P<data>{_data_re})?\\\\s*(\\\\!(?P<comment>.*))?\\\\s*$\"\\n-    _line_type_re = re.compile(_type_re)\\n+    _line_type_re = re.compile(_type_re, re.IGNORECASE)\\n     line = line.strip()\\n     if not line:\\n         return \"comment\"\\n@@ -306,7 +306,7 @@ def _get_tables_from_qdp_file(qdp_file, input_colnames=None, delimiter=None):\\n \\n             values = []\\n             for v in line.split(delimiter):\\n-                if v == \"NO\":\\n+                if v.upper() == \"NO\":\\n                     values.append(np.ma.masked)\\n                 else:\\n                     # Understand if number is int or float\\n', 'test_patch': 'diff --git a/astropy/io/ascii/tests/test_qdp.py b/astropy/io/ascii/tests/test_qdp.py\\n--- a/astropy/io/ascii/tests/test_qdp.py\\n+++ b/astropy/io/ascii/tests/test_qdp.py\\n@@ -43,7 +43,18 @@ def test_get_tables_from_qdp_file(tmp_path):\\n     assert np.isclose(table2[\"MJD_nerr\"][0], -2.37847222222222e-05)\\n \\n \\n-def test_roundtrip(tmp_path):\\n+def lowercase_header(value):\\n+    \"\"\"Make every non-comment line lower case.\"\"\"\\n+    lines = []\\n+    for line in value.splitlines():\\n+        if not line.startswith(\"!\"):\\n+            line = line.lower()\\n+        lines.append(line)\\n+    return \"\\\\n\".join(lines)\\n+\\n+\\<EMAIL>(\"lowercase\", [False, True])\\n+def test_roundtrip(tmp_path, lowercase):\\n     example_qdp = \"\"\"\\n     ! Swift/XRT hardness ratio of trigger: XXXX, name: BUBU X-2\\n     ! Columns are as labelled\\n@@ -70,6 +81,8 @@ def test_roundtrip(tmp_path):\\n     53000.123456 2.37847222222222e-05    -2.37847222222222e-05   -0.292553       -0.374935\\n     NO 1.14467592592593e-05    -1.14467592592593e-05   0.000000        NO\\n     \"\"\"\\n+    if lowercase:\\n+        example_qdp = lowercase_header(example_qdp)\\n \\n     path = str(tmp_path / \"test.qdp\")\\n     path2 = str(tmp_path / \"test2.qdp\")\\n', 'problem_statement': 'ascii.qdp Table format assumes QDP commands are upper case\\n### Description\\n\\nascii.qdp assumes that commands in a QDP file are upper case, for example, for errors they must be \"READ SERR 1 2\" whereas QDP itself is not case sensitive and case use \"read serr 1 2\". \\r\\n\\r\\nAs many QDP files are created by hand, the expectation that all commands be all-caps should be removed.\\n\\n### Expected behavior\\n\\nThe following qdp file should read into a `Table` with errors, rather than crashing.\\r\\n```\\r\\nread serr 1 2 \\r\\n1 0.5 1 0.5\\r\\n```\\n\\n### How to Reproduce\\n\\nCreate a QDP file:\\r\\n```\\r\\n> cat > test.qdp\\r\\nread serr 1 2 \\r\\n1 0.5 1 0.5\\r\\n<EOF>\\r\\n\\r\\n > python\\r\\nPython 3.10.9 (main, Dec  7 2022, 02:03:23) [Clang 13.0.0 (clang-1300.0.29.30)] on darwin\\r\\nType \"help\", \"copyright\", \"credits\" or \"license\" for more information.\\r\\n>>> from astropy.table import Table\\r\\n>>> Table.read(\\'test.qdp\\',format=\\'ascii.qdp\\')\\r\\nWARNING: table_id not specified. Reading the first available table [astropy.io.ascii.qdp]\\r\\nTraceback (most recent call last):\\r\\n...\\r\\n    raise ValueError(f\\'Unrecognized QDP line: {line}\\')\\r\\nValueError: Unrecognized QDP line: read serr 1 2\\r\\n```\\r\\n\\r\\nRunning \"qdp test.qdp\" works just fine.\\r\\n\\n\\n### Versions\\n\\nPython 3.10.9 (main, Dec  7 2022, 02:03:23) [Clang 13.0.0 (clang-1300.0.29.30)]\\r\\nastropy 5.1\\r\\nNumpy 1.24.1\\r\\npyerfa *******\\r\\nScipy 1.10.0\\r\\nMatplotlib 3.6.3\\r\\n\\n', 'hints_text': \"Welcome to Astropy 👋 and thank you for your first issue!\\n\\nA project member will respond to you as soon as possible; in the meantime, please double-check the [guidelines for submitting issues](https://github.com/astropy/astropy/blob/main/CONTRIBUTING.md#reporting-issues) and make sure you've provided the requested details.\\n\\nGitHub issues in the Astropy repository are used to track bug reports and feature requests; If your issue poses a question about how to use Astropy, please instead raise your question in the [Astropy Discourse user forum](https://community.openastronomy.org/c/astropy/8) and close this issue.\\n\\nIf you feel that this issue has not been responded to in a timely manner, please send a message directly to the [development mailing list](http://groups.google.com/group/astropy-dev).  If the issue is urgent or sensitive in nature (e.g., a security vulnerability) please send an e-mail directly to the private e-mail <EMAIL>.\\nHuh, so we do have this format... https://docs.astropy.org/en/stable/io/ascii/index.html\\r\\n\\r\\n@taldcroft , you know anything about this?\\nThis is the format I'm using, which has the issue: https://docs.astropy.org/en/stable/api/astropy.io.ascii.QDP.html\\r\\n\\nThe issue is that the regex that searches for QDP commands is not case insensitive. \\r\\n\\r\\nThis attached patch fixes the issue, but I'm sure there's a better way of doing it.\\r\\n\\r\\n[qdp.patch](https://github.com/astropy/astropy/files/10667923/qdp.patch)\\r\\n\\n@jak574 - the fix is probably as simple as that. Would you like to put in a bugfix PR?\", 'created_at': '2023-02-06T19:20:34Z', 'version': '5.1', 'FAIL_TO_PASS': '[\"astropy/io/ascii/tests/test_qdp.py::test_roundtrip[True]\"]', 'PASS_TO_PASS': '[\"astropy/io/ascii/tests/test_qdp.py::test_get_tables_from_qdp_file\", \"astropy/io/ascii/tests/test_qdp.py::test_roundtrip[False]\", \"astropy/io/ascii/tests/test_qdp.py::test_read_example\", \"astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example\", \"astropy/io/ascii/tests/test_qdp.py::test_roundtrip_example_comma\", \"astropy/io/ascii/tests/test_qdp.py::test_read_write_simple\", \"astropy/io/ascii/tests/test_qdp.py::test_read_write_simple_specify_name\", \"astropy/io/ascii/tests/test_qdp.py::test_get_lines_from_qdp\"]', 'environment_setup_commit': '5f74eacbcc7fff707a44d8eb58adaa514cb7dcb5'}\n"]}], "source": ["for d in list(dataset['test'])[:3]:\n", "    print(d)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# dataset['test']['instance_id']\n", "root_codezone_id = '760902970966700032'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["15:59:12 heracles:INFO:logger.py:74 Heracles is starting, current log_level is DEBUG\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/clackyai/lib/python3.11/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n", "* 'fields' has been removed\n", "  warnings.warn(message, UserWarning)\n", "15:59:14 heracles:INFO:llm.py:31 langfuse is enabled, load config\n", "15:59:15 heracles:INFO:redis_cache.py:44 [cache] feature is disabled\n"]}], "source": ["from heracles.agent_workspace.paas_sdk.utils import (\n", "    bind_middleware_to_codezone,\n", "    fork_codezone,\n", "    get_playground_id,\n", "    bind_playground_info,\n", "    import_codezone_file,\n", "    stop_playground,\n", "    delete_codezone,\n", ")  # noqa: E501\n", "from heracles.core.schema.test import MockSocketIOServer, Context\n", "from heracles.server.clacky.playground import Playground\n", "from heracles.server.clacky.playground_channel import PlaygroundChannel\n", "from heracles.server.clacky.playground_manager import PlaygroundManager\n", "\n", "socketio_server = MockSocketIOServer()\n", "playground_manager = PlaygroundManager()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': False, 'data': {}, 'error': 'Unexpected status code: 504'}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["await fork_codezone(root_codezone_id)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["async def instance_id_to_codezone(data):\n", "    instance_id = data['instance_id']\n", "    setup_scripts_dir = f'llm_tests/swe_bench/sweb_lite_setup_scripts/{instance_id}'\n", "\n", "    code_zone_response = await fork_codezone(root_codezone_id)\n", "    new_codezone_id = code_zone_response['data']['id']\n", "    response = await get_playground_id(new_codezone_id)\n", "    playground_id = response['data']['id']\n", "    playground = Playground(playground_id, socketio_server)\n", "    playground_manager.add_playground(playground)\n", "    playground_channel = PlaygroundChannel(f'sid-{playground_id}', socketio_server, playground_manager, playground_id)\n", "    await playground_channel.start()\n", "\n", "    playground = playground_channel.current_playground\n", "    workspace = playground.agent_controller.workspace\n", "\n", "    playground = workspace.playground\n", "    await playground.wait_for_ok(reason='swe setup')\n", "    playground_info = await bind_playground_info(playground.playground_id)\n", "    new_codezone_id = playground_info['data']['codeZoneId']\n", "    print(f'new_codezone_id: {new_codezone_id}, playground_id: {playground.playground_id}')\n", "    setup_env_script_name = 'setup_env.sh'\n", "    setup_env_script_path = os.path.join(setup_scripts_dir, setup_env_script_name)\n", "    setup_env_script_content = open(setup_env_script_path).read()\n", "    setup_env_script_content = setup_env_script_content.replace('/opt/miniconda3', '~/miniconda3')\n", "    setup_env_script_content = setup_env_script_content.replace('/testbed', '/home/<USER>/app')\n", "    await import_codezone_file(new_codezone_id, 'setup_env.sh', setup_env_script_content)\n", "    await playground.func_call('agent_terminal_with_result', 'mv setup_env.sh /home/<USER>/', soft_timeout=120)  # noqa: E501\n", "    setup_repo_script_name = 'setup_repo.sh'\n", "    setup_repo_script_path = os.path.join(setup_scripts_dir, setup_repo_script_name)\n", "    setup_repo_script_content = open(setup_repo_script_path).read()\n", "    setup_repo_script_content = setup_repo_script_content.replace('/opt/miniconda3', '~/miniconda3')\n", "    setup_repo_script_content = setup_repo_script_content.replace('/testbed', '/home/<USER>/app')\n", "    await import_codezone_file(new_codezone_id, 'setup_repo.sh', setup_repo_script_content)\n", "    await playground.func_call('agent_terminal_with_result', 'mv setup_repo.sh /home/<USER>/', soft_timeout=120)\n", "    eval_script_name = 'eval.sh'\n", "    eval_script_path = os.path.join(setup_scripts_dir, eval_script_name)\n", "    eval_script_content = open(eval_script_path).read()\n", "    eval_script_content = eval_script_content.replace('/opt/miniconda3', '~/miniconda3')\n", "    eval_script_content = eval_script_content.replace('/testbed', '/home/<USER>/app')\n", "    await import_codezone_file(new_codezone_id, 'eval.sh', eval_script_content)\n", "    await playground.func_call('agent_terminal_with_result', 'mv eval.sh /home/<USER>/', soft_timeout=120)\n", "    await playground.func_call('agent_terminal_with_result', '/bin/bash /home/<USER>/setup_env.sh', soft_timeout=120)  # noqa: E501\n", "    await playground.func_call(\n", "        'agent_terminal_with_result', 'source ~/miniconda3/etc/profile.d/conda.sh && conda activate testbed', soft_timeout=120\n", "    )  # noqa: E501\n", "    await playground.func_call('agent_terminal_with_result', '/bin/bash /home/<USER>/setup_repo.sh', soft_timeout=120)\n", "    await playground.func_call('agent_terminal_with_result', 'cd /home/<USER>/app', soft_timeout=120)\n", "\n", "    d = {\n", "        instance_id: {\n", "            'develop': {\n", "                'codezone_id': new_codezone_id,\n", "                'playground_id': playground.playground_id,\n", "                'environment_ver_id': '403710022024036354',\n", "                'environment_name': 'swe',\n", "                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "            }\n", "        }\n", "    }\n", "    await stop_playground(playground.playground_id)\n", "    return d"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm_notebook as tqdm"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# d = await instance_id_to_codezone(dataset['test'][0])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'astropy__astropy-12907': {'develop': {'codezone_id': '760994121333755904',\n", "   'playground_id': '760994124941217792',\n", "   'environment_ver_id': '403710022024036354',\n", "   'environment_name': 'swe',\n", "   'updated_at': '2025-02-11 16:05:14'}}}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}