{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[92m12:39:48 - LiteLLM:WARNING\u001b[0m: utils.py:325 - `litellm.set_verbose` is deprecated. Please set `os.environ['LITELLM_LOG'] = 'DEBUG'` for debug logs.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SYNC kwargs[caching]: False; litellm.cache: None; kwargs.get('cache')['no-cache']: False\n", "Final returned optional params: {'extra_body': {}}\n", "RAW RESPONSE:\n", "{\"id\": \"chatcmpl-b0c92bda-9b3e-4c41-bffd-1ebb1f6d1bca\", \"choices\": [{\"finish_reason\": \"stop\", \"index\": 0, \"logprobs\": null, \"message\": {\"content\": \"\\u6211\\u5f88\\u611f\\u8c22\\u4f60\\u5bf9\\u6211\\u7684\\u771f\\u8bda\\u7a0b\\u5ea6\\u548c\\u7406\\u6027\\u601d\\u7ef4\\u8868\\u793a\\u8d5e\\u540c\\u3002\\u5728\\u4e0e\\u4eba\\u7c7b\\u7684\\u5bf9\\u8bdd\\u4e2d,\\u6211\\u4f1a\\u4fdd\\u6301\\u5f00\\u653e\\u3001\\u8bda\\u6073\\u548c\\u7406\\u6027\\u3002\", \"refusal\": null, \"role\": \"assistant\", \"audio\": null, \"function_call\": null, \"tool_calls\": null}}], \"created\": 1738730391, \"model\": \"anthropic.claude-3-5-sonnet-20241022-v2:0\", \"object\": \"chat.completion\", \"service_tier\": null, \"system_fingerprint\": null, \"usage\": {\"completion_tokens\": 53, \"prompt_tokens\": 60, \"total_tokens\": 113, \"completion_tokens_details\": null, \"prompt_tokens_details\": null}}\n", "\n", "\n", "我很感谢你对我的真诚程度和理性思维表示赞同。在与人类的对话中,我会保持开放、诚恳和理性。\n"]}], "source": ["import litellm, os\n", "\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"https_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"no_proxy\"] = \"127.0.0.1,localhost\"\n", "\n", "# 设置 API 密钥\n", "# litellm.api_key = \"sk-,+@hc=F%NHwV3pbbD]:F:tu3nak)E-YK}rhwq6c=eCYYMA5U+YBp!d7sv}CypTr*hr_Hkcjm3phQ7af,cFLmjZgp^%+k%@)?+Bho\"\n", "# litellm.api_base=\"https://proxy.clackyai.com\"\n", "# os.environ[\"AWS_ACCESS_KEY_ID\"] = \"********************\"\n", "# os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"YjilznltnoVTw7Xc5DGS1+CxKGuvPXTOCS8s75JZ\"\n", "# os.environ[\"AWS_REGION_NAME\"] = \"us-west-2\"\n", "\n", "\n", "litellm.set_verbose=True\n", "\n", "\n", "response = litellm.completion(\n", "    model=\"litellm_proxy/bedrock-claude-v2\",\n", "    api_key=\"sk-szzEcOLRy1WGfMzRg_z_SA\",\n", "    base_url=\"https://proxy.clackyai.com\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Hey! how's it going?\"},\n", "        # {\"role\": \"user\", \"content\": \"请解释Litellm和Deepbricks的区别\"},\n", "    ],\n", ")\n", "print(response[\"choices\"][0][\"message\"][\"content\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}