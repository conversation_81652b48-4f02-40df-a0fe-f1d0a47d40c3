{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import json\n", "output_dict = {}\n", "with open('swe_codezones_sample.json') as f:\n", "    for line in f:\n", "        d = json.loads(line)\n", "        output_dict.update(d)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["with open('llm_tests/datasets/swe/codezones.json', 'w') as f:\n", "    json.dump(output_dict, f, indent=4)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['no_proxy'] = 'localhost,127.0.0.1'"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset('princeton-nlp/SWE-bench_Lite')\n", "res = []\n", "for ds in list(dataset['test']):\n", "    project_info = {}\n", "    project_info['name'] = ds['instance_id']\n", "    project_info['description'] = ds['instance_id']\n", "    project_info['github_repo'] = ds['repo']\n", "    project_info['env_code'] = \"swe\"\n", "    project_info['states'] = []\n", "    res.append(project_info)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["300"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["len(res)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import yaml\n", "\n", "with open('llm_tests/datasets/swe/projects.yml', 'w') as f:\n", "    yaml.dump(res, f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}