{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'git'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mgit\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'git'"]}], "source": ["import git"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"https_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"no_proxy\"] = \"127.0.0.1,localhost\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<git.repo.base.Repo '/Users/<USER>/works/exp/test_repo/wblog/.git'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["repo_url = 'https://github.com/windy/wblog.git'  # 替换为您的仓库URL\n", "target_directory = './test_repo/wblog'  # 替换为您想要克隆到的目录\n", "\n", "git.Repo.clone_from(repo_url, target_directory)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["repo = git.Repo(target_directory)\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<git.Commit \"6ce4852da1216eb46a0fa69e3cf87fdf4ac7b53b\">,\n", " <git.Commit \"b76f9782523141751af1b46546a7604c18c37f3c\">,\n", " <git.Commit \"8b395034953d81905346d2220d5185cf186dc191\">,\n", " <git.Commit \"3ccd40c93bdb3d80370477e53f5e9dfd41b3263d\">,\n", " <git.Commit \"51248c60ff9eefa1f9ac0b30c47865d41d042303\">,\n", " <git.Commit \"87f1ce9b55351d8aa35ba3a9f3c2ae005de8600c\">,\n", " <git.Commit \"8d8c9f622690cc5f1cefc587b2caf8e26d23a588\">,\n", " <git.Commit \"e149b1e12df9c415a2c316962d527700ee34ab2a\">,\n", " <git.Commit \"edc538062b089bc4dbd8ffeee462b3b7daa30af1\">,\n", " <git.Commit \"de1753662883e9563baa3d2b66caf6a6e970c1cb\">,\n", " <git.Commit \"41a731a81636b1d895cf335e5206d1f39eaac401\">,\n", " <git.Commit \"a484277ea8ea0c7c6006bde07339a7f83c6ed1cf\">,\n", " <git.Commit \"5cc439aec1601f1531b33d429d4f5480e5282180\">,\n", " <git.Commit \"50324413c13b375b5551c5e40a9ac96ca5beabbc\">,\n", " <git.Commit \"a98b3c7cfd063b8e741952c1a6b7593287c9cfff\">,\n", " <git.Commit \"e838e8ea386ff4a0a0d05e39e78aaa0ee53145b8\">,\n", " <git.Commit \"b3f37d5ff7f938ca95b85502fc05ec4277dfae2b\">,\n", " <git.Commit \"6eabd4f39743d2b8887096e3a28d04b5cdbe1e11\">,\n", " <git.Commit \"4b808d112399bbc8f967a4d309630e20f0f98fba\">,\n", " <git.Commit \"404445f3afc28ddb149ba0ba72f09aa4509fdd99\">,\n", " <git.Commit \"3460058519355fac855f776eb6d742d9078569b9\">,\n", " <git.Commit \"a0f2a6fdae0daa9933772ad268cfa0bd1213ed02\">,\n", " <git.Commit \"9be0e91079e00e5b0904cea590d49103dbf90423\">,\n", " <git.Commit \"8c3a947fd7ae196961a17e6049f4b7127dfd927d\">,\n", " <git.Commit \"8b0729b4ce362d7fcd42cf4d1d93fbe4a10be602\">,\n", " <git.Commit \"1463dee352bbcac11cf01013804f71c4b4a379fd\">,\n", " <git.Commit \"c07844b412c9eeb5c1101e58297831db818cdd34\">,\n", " <git.Commit \"ae15b53171bb03fce8a1b8058cc22dead2761294\">,\n", " <git.Commit \"8a84e244abee38484e0dbe413da792768228cda4\">,\n", " <git.Commit \"1b7ece79c3bc20d495b5445d46709883be05e00c\">,\n", " <git.Commit \"8f26d1d5a083b6e151ba00f938c10764af346c4e\">,\n", " <git.Commit \"1c740b6ee409fecedaee945dfe85cf333f31a100\">,\n", " <git.Commit \"9456163fc2d5c85264b6998cdead1597f1f824f7\">,\n", " <git.Commit \"89ccb06efc7b875febb55156984d0045cd268762\">,\n", " <git.Commit \"90a983fc49c1e817c445d27174d247730ec3953c\">,\n", " <git.Commit \"61d07908dbec47fb1bd61ccee3a1660bc912e14e\">,\n", " <git.Commit \"b5bc17a2d01af6fec44080424cb0fb02149a19c4\">,\n", " <git.Commit \"0687644d972d7776dcc03537c37e01866dec00c8\">,\n", " <git.Commit \"c076a2dd09106ef3bfbffb07fd029c604548be90\">,\n", " <git.Commit \"f364927ae416b190346884efaf7132ca3dc25c87\">,\n", " <git.Commit \"00140a12556a30bfac7ee85a7a77e424b6e66989\">,\n", " <git.Commit \"cc70226a90a6d90d6ff7926049e9e416cbaaedd3\">,\n", " <git.Commit \"c287e676ede226cf91a8371387b8b97a97263445\">,\n", " <git.Commit \"59eeaeb71be758011c974873d2e03f5e69861e54\">,\n", " <git.Commit \"f3743a10c14d364757ddaf24ddb2316d78f13ba9\">,\n", " <git.Commit \"fdd03ad58a13e16ef8c69d1f40f8964ae7f49a05\">,\n", " <git.Commit \"59335d392da57834bcf9779346737958120d9e4f\">,\n", " <git.Commit \"ad4cd73af862d884dd482e793d274f3932611f33\">,\n", " <git.Commit \"366830566b88af74176b9795417e26c33e958c4d\">,\n", " <git.Commit \"c5ab21c4e3040c2496565955b1ea86993d54b9cc\">,\n", " <git.Commit \"36610c9c083e49b4bf7e0e9717e460dc6fb6dff4\">,\n", " <git.Commit \"f9af63d30807f60be0b0886922100918b3a146c3\">,\n", " <git.Commit \"79e1c87bb165da218f4cace68fb6438c3b289f96\">,\n", " <git.Commit \"ba4da8c8db2a32e57835549b7423dfb07e0d1704\">,\n", " <git.Commit \"6137a4755a6e71111db5ed55e66d26549d89c2c7\">,\n", " <git.Commit \"ecf4818971688708705c2999882b7459c96ec86b\">,\n", " <git.Commit \"27adb4e47eb9fe68db52626eadde2fd38c9df86f\">,\n", " <git.Commit \"5800f6f31a6b47cf087c77d7e03775f468883504\">,\n", " <git.Commit \"b36619b3d11ea16c045ddf9b64e82b46ae7f64c0\">,\n", " <git.Commit \"e1fd97b2d6123e096a3d5c1c0e6301026b7c2e93\">,\n", " <git.Commit \"2aaee7c468caf34c4a4f40c5cf41580d8bf9cc4d\">,\n", " <git.Commit \"ae93913e6010df2915874a73e252cca9c0e2f914\">,\n", " <git.Commit \"4e464531593eef960ba3e8cb82514c1544cfc0a4\">,\n", " <git.Commit \"4d6f059c1766f7890257c7093b40d8da89ecaa66\">,\n", " <git.Commit \"20441526e70bf5537035db9c0ccf2c2321658215\">,\n", " <git.Commit \"98c5afa0ec297d8764a8c6615ee8f28b6a094aab\">,\n", " <git.Commit \"bd1be6b303398fab437c8d3b2b72f523d8ddd4dd\">,\n", " <git.Commit \"a6f809ed412d1296b447cb092e49f651a8fee04a\">,\n", " <git.Commit \"02c540f9fd58001269857793eaeda23998652b09\">,\n", " <git.Commit \"5b4fd113e7086ceeabcca6c235c783e25bc3a62d\">,\n", " <git.Commit \"a4ced042dfd616c8d8f0c98a6b0acdeab488cf3f\">,\n", " <git.Commit \"da79c2ab95ea90dcec2957013fa78326b111a388\">,\n", " <git.Commit \"eb5b2cc615eadf1238bddb946a84efcbd32f884c\">,\n", " <git.Commit \"6e4f1bff8cff4ad99ec918122e88a878aa9ecb28\">,\n", " <git.Commit \"4da020f996ffac3be31a81c1a41ea32c7546d307\">,\n", " <git.Commit \"26ae9ac753d49c7c23acb97e333c70c183300d5f\">,\n", " <git.Commit \"c335f8e03b5a671a089123650fec74e050521cd0\">,\n", " <git.Commit \"ed0747812aea304376dd8ae3eedf19658145cfe7\">,\n", " <git.Commit \"261ce3bfec2c4e678441474a9c3542fa1cdf9f48\">,\n", " <git.Commit \"69b90ae5fd99356e0dec86a55393c14e59c14c61\">,\n", " <git.Commit \"152e30026819f3fe567c5c2a19ed9db8aa21304f\">,\n", " <git.Commit \"cfdcf69742b9be6461a2bef37687aa2a6221d533\">,\n", " <git.Commit \"a73682c0e183bf553f8edd0fcd14cec617cdc79d\">,\n", " <git.Commit \"1e7fa94fde2c67925422320831798bb12d159c9c\">,\n", " <git.Commit \"a253f8bf50abae206d81edcab5782ea20f20493e\">,\n", " <git.Commit \"c1cb4930adb62fd9da838ac219c701c37e361bf1\">,\n", " <git.Commit \"2e8cba59e2b2c5cc916b67d36fb9539ca36c04db\">,\n", " <git.Commit \"608ae8909707b9635eba4e397204f3620eda5e33\">,\n", " <git.Commit \"0e523d6ff4753596e0923cdd42e68e86c9c76698\">,\n", " <git.Commit \"bbb3f213f959587b10e3ecc2e5be44d0cc150477\">,\n", " <git.Commit \"5965a8d52f31144010bc267816dc3e3aa786f7b8\">,\n", " <git.Commit \"c35089bb860daac4f4067bcea17fc17146fe01bf\">,\n", " <git.Commit \"90ef5250bb05ecc01a37f0f3622cc66afd14e283\">,\n", " <git.Commit \"fd805df5aadd18b3b2fd09b1d7612761917071b5\">,\n", " <git.Commit \"e50cfaadb1fb7084f0e81f5dd3b72d649d844b02\">,\n", " <git.Commit \"92c5440229c58238dd714ebd16850e308735e561\">,\n", " <git.Commit \"727234814c6b49ca1a8a3b61561f56c97473de73\">,\n", " <git.Commit \"80f62e0160614b71ea827697df68379660545685\">,\n", " <git.Commit \"16a656934f96951aece7cc5499ec67c739f65c6c\">,\n", " <git.Commit \"0234b221664e72608d75f34d7582ccc1809e11c6\">,\n", " <git.Commit \"455fadec6db6669272f138873a510e0279d82bb8\">,\n", " <git.Commit \"75a003964496aac4c2ad4a668345654006fdd898\">,\n", " <git.Commit \"a1c5e52b378c7f9d82dde84cd4b5646749a067d5\">,\n", " <git.Commit \"5e7c35d9b341db5264d156ed301c28c57979a477\">,\n", " <git.Commit \"da330610b223f2901f546a11d4b1af76b86f1c0d\">,\n", " <git.Commit \"41cf8b5e3ac0e203c8d004db6805bf80c55b2644\">,\n", " <git.Commit \"9bd33674f609ff6679c7d0fc7722aada7d77ce09\">,\n", " <git.Commit \"e6fcc62d39c2b6966b12dd890b8463d2cdc3ce03\">,\n", " <git.Commit \"0252887a0fa2f1c1b5e29f8f53c942070abe0bc8\">,\n", " <git.Commit \"3388920aefd027ac73128a29b8cf612779e57b6e\">,\n", " <git.Commit \"a857ea6dad982ae7fd651e2e3a467760d71e52eb\">,\n", " <git.Commit \"8db612ae0a61f55b9aed1905c12ae43f48e49962\">,\n", " <git.Commit \"e5ba083bc9e36d7b2f6cdd13f7bb35ebc617e367\">,\n", " <git.Commit \"6267b9d465e11a17b765ea88edc0bcf8a8e7138e\">,\n", " <git.Commit \"293b5bb63bfe43bad4c7dcbda091b0df42895af9\">,\n", " <git.Commit \"1069d6a1a600b595f85ff0564b836621f77fc23a\">,\n", " <git.Commit \"6a40e454750e78cbbf99d4454251c1b373688a74\">,\n", " <git.Commit \"691c5dd40de37eabf295f9312fe16717ca61965a\">,\n", " <git.Commit \"9dc1ad2ad95078b55778fa90456c763aa2a7e370\">,\n", " <git.Commit \"3843ffa9fca662e853b10f09d95d3f9b589ff36c\">,\n", " <git.Commit \"a8cdc20dbbcf9b070d31a8fae8df03789e82a437\">,\n", " <git.Commit \"bb9e21f8803f32349e2ce7c9ba13b8be8f1e1ec4\">,\n", " <git.Commit \"e7e3b4582b6a818795d09a4c5ac86fad63184389\">,\n", " <git.Commit \"539f3dd5f311414249e291deeda786f9b22223c6\">,\n", " <git.Commit \"26e97749d2be91a81b77fa19f01b05c356177efe\">,\n", " <git.Commit \"bb38212029f518505562a353a9fd937a1b9c66ea\">,\n", " <git.Commit \"981a5fea064cba6adbfe10a9deb3b3ec2b076965\">,\n", " <git.Commit \"2e89bf9a0409bf3baec2eb4db0eaa9b0d00e0d5a\">,\n", " <git.Commit \"e04186cd5ceac49569449caad2790fc2bc0cfdfc\">,\n", " <git.Commit \"8ecf79644f8589774795c06803f7d5605583a236\">,\n", " <git.Commit \"3e981b1f51777871345a77d0e4af3d90a4640133\">,\n", " <git.Commit \"1fc62a384fc8b09ef8792e90ad059364cfac95e6\">,\n", " <git.Commit \"1939dfd500ee69201b92e23fdb9db6c90dcd3d6a\">,\n", " <git.Commit \"34dd921895d4cbc43a2139a70969394281db256e\">,\n", " <git.Commit \"196a830e1ea0023ff05ec5ee61ef66f50654935d\">,\n", " <git.Commit \"23f07094bfa34ca18a63d57492cb72485ff97600\">,\n", " <git.Commit \"2ec5f8e82aad84c0807fe5a181fae974271030e6\">,\n", " <git.Commit \"68eebb8989ab3e21e9a161b977c2e97a8f41fcfc\">,\n", " <git.Commit \"7913917d514c78f387d3343f118e5306c058d4dc\">,\n", " <git.Commit \"952fb4ffa0a2cccb9df85ceb775c081a296436f6\">,\n", " <git.Commit \"1d68df79c12b107c59cbae04ea0c2f06fc1ba45f\">,\n", " <git.Commit \"317604b6408df5a4f8c761d8ddaa5247777ef360\">,\n", " <git.Commit \"c6da3ea7dc7c4ad33e9edf52f3dc58a9e94c8a2c\">,\n", " <git.Commit \"0fbc18ec9e6c19f09e8953166323d8e9777e918c\">,\n", " <git.Commit \"2228a804cca479143dc3cf9f3088730659fb042f\">,\n", " <git.Commit \"d293cea7ec97481d5f23983adf2abe27d6b47024\">,\n", " <git.Commit \"455e240dd3975a5f2027daa756a87589ebc025f2\">,\n", " <git.Commit \"1e1aa79820c7f905325feceb534deaab068fd390\">,\n", " <git.Commit \"3f2174366623f5de6100626a922bfc0cc19295bf\">,\n", " <git.Commit \"c029fd7accb70fe3f48f2ea32ac0cdda29b7c403\">,\n", " <git.Commit \"67dcabf8c60f245fcb2dc35031bf82478f8a1e49\">,\n", " <git.Commit \"799bcecc92081224fc25e0a894c91e725d42f55d\">,\n", " <git.Commit \"c45de0d387f49fb1536d660835ed045dbb9090fb\">,\n", " <git.Commit \"a84e820b296ee358d0b4559c8f05588f6a0807a7\">,\n", " <git.Commit \"8033297fa448d3167b1e3b030a2f945a407e3fbc\">,\n", " <git.Commit \"3046e782f625f2393b4cae626c62a590efc99d41\">,\n", " <git.Commit \"c4e5334f8c6df04d282a834b3aafc5b9e7800ef1\">,\n", " <git.Commit \"235f210481bbe2b4662f3c938574b0929ad4eda5\">,\n", " <git.Commit \"927e7f49d5715ce24973e3f4c86347ca5baccf1f\">,\n", " <git.Commit \"0f99e22e07b948c5c212a0e4da5eb8c7e114fb4c\">,\n", " <git.Commit \"90aa28753e84e493a3b7f2a6977d723a17d2c70e\">,\n", " <git.Commit \"131ead0f8835bbf2fd22e673c6aa74bd1fb8e86c\">,\n", " <git.Commit \"6fe828d8eca1e462de122f6ad1479eb2aac9e695\">,\n", " <git.Commit \"107b5c292baa92c3ce2bf1cc762505e2c14c6ebb\">,\n", " <git.Commit \"10be0f5a5d851c9e3f1fd7a213501b652e467527\">,\n", " <git.Commit \"3486e055a924df5ba868de588a38ea6f3c1fa957\">,\n", " <git.Commit \"d0fe2e350b42ccf2e196cb07aa08eb3040d7906d\">,\n", " <git.Commit \"347ebd5933f204280132b668c0552aa9c5ea2acb\">,\n", " <git.Commit \"5359b6f6960ea4d0d760a741bddf6293bd499b56\">,\n", " <git.Commit \"381fbfa67b90fa6defea2741d4f563769fa942f2\">,\n", " <git.Commit \"f741cb8456fcc275afc60b5af22ea82eca0b4558\">,\n", " <git.Commit \"c01300fa4e8fe60eb0382285c27511f25d5f4f51\">,\n", " <git.Commit \"5e590cf55025ae906c1d19bd8a2c8fe1d9a982d0\">,\n", " <git.Commit \"14471e968244292991cf133f96cea3d33c8530a9\">,\n", " <git.Commit \"ffb2820d3e292c6ed0c09ea63e7dabd691cd18b5\">,\n", " <git.Commit \"0727c942ae2cc707c8bc024a062b2adeea82b174\">,\n", " <git.Commit \"69bc42c4effe797689b4e1b4d2923ab30b20c9fb\">,\n", " <git.Commit \"a6d18cb05f8942708758e2e839247288aeb62323\">,\n", " <git.Commit \"37cb858eec5e971cb5582c6346879110ff2f81f2\">,\n", " <git.Commit \"4dfe7a4bdb769deea052b1563585787fdbdf4c82\">,\n", " <git.Commit \"dadc70e0f40c9c4f20f5f5ac8ac6360f7e55c9b7\">,\n", " <git.Commit \"08699c3dce5c616198db8a3137d3c946a2cc4af5\">,\n", " <git.Commit \"cfabcd037f085ac63cd982859e3d257c4aab55a3\">,\n", " <git.Commit \"c8a592c2708b0898e02f2f9cbcef87a36060c6ff\">,\n", " <git.Commit \"838b97f74688c7d59ef18c9c112ddf2be6268bb1\">,\n", " <git.Commit \"5b4adb942df877ddc29a9776bbe9861ff93d111a\">,\n", " <git.Commit \"20b7ce70992cebb34bcbe4ad7ab819d83713efd9\">,\n", " <git.Commit \"fe71e7a7646d5eacdc74a611585e34a3e6d1c612\">,\n", " <git.Commit \"fd5f37b2cc2577774eb53a7a586b011f3ce9f575\">,\n", " <git.Commit \"93f14db3f8f30e8daebf13a6f67eda657ec0aef6\">,\n", " <git.Commit \"199a8ae61756cebf0c96c79a3943de5edca6b2cd\">,\n", " <git.Commit \"b6f0502a93f8114520c431a3c44b9ef00773aa80\">,\n", " <git.Commit \"369184d4f1f74ca92313e64076602748c01af74e\">,\n", " <git.Commit \"193a96d0550fefe5bdce8a63ca2325642dec62af\">,\n", " <git.Commit \"b106c6f8d1a06f82db951ef0f6795eb576319aae\">,\n", " <git.Commit \"eff064027795ebfc6c0f723fa554fdde71238f7f\">,\n", " <git.Commit \"a74c737df8ba139e85580cbc83ff94d96423bda7\">,\n", " <git.Commit \"1c91c046f88e778d4feead24ceb82ea639b46e22\">,\n", " <git.Commit \"31827d94e1e0f9e06b56ff6db49d8d4673338ea5\">,\n", " <git.Commit \"9dc427bfe6329d08c1327cdb853452868c973158\">,\n", " <git.Commit \"310b82885d2c458a489564ddebce845753b4e7bf\">,\n", " <git.Commit \"4db287aea6abb6ab71f1224d67a6e6d441e2225c\">,\n", " <git.Commit \"205cb641f868af8cf164d2aff31f633d0ef0450e\">,\n", " <git.Commit \"09912100239f24acd8b26cfd2c7c785c665d11f7\">,\n", " <git.Commit \"51ad5412cfe75666b12085ee6da4926e65de84b1\">,\n", " <git.Commit \"fed92dd16b2f22c4efdbdfa95957f8e99e6477dd\">,\n", " <git.Commit \"6564bf967a48c610407961a9a90ec7ce07bc8c1c\">,\n", " <git.Commit \"ab0af3cabf02889a124f4e86a1581883a0b726c7\">,\n", " <git.Commit \"cb114f077746c799f8ae44339cb4965e5e7ab41f\">,\n", " <git.Commit \"12a23429d692709dc36a8eefe8966a8d0a0c9fd6\">,\n", " <git.Commit \"db62889114f54f0028935eaa438a98325f1bafc6\">,\n", " <git.Commit \"aa2c60f6f04cdf357a413737b1869adb77b5d31e\">,\n", " <git.Commit \"823a38de1819fde54e4a85f3564b6ebc1c24bec7\">,\n", " <git.Commit \"8ae2d8e23b35d32703ae52e855a401de74b5179b\">,\n", " <git.Commit \"19775e2ab0ca719a2a22f88d846dcd9ab5092ecd\">,\n", " <git.Commit \"99e8125952e86e2772933d33f45dff5bc34b0945\">,\n", " <git.Commit \"febb325c76ae25c5f7684f3889b632126c8affc1\">,\n", " <git.Commit \"f504116af9438bd9102d3bec0299c69b908aec0d\">,\n", " <git.Commit \"fceecc4b77c2d6f72d606125c9a3a2e15c6faf2e\">,\n", " <git.Commit \"4a3ab3c9e6533221d855a737fd9456eff2bd856d\">,\n", " <git.Commit \"7922404b691bfcc254ad8902208c521af7c51a79\">,\n", " <git.Commit \"d26d1912d6cdc8d980676f53505f9c770455a8f3\">,\n", " <git.Commit \"7c9ddc5f363ca7ca305ce4f7e0b54aad6b4ca399\">,\n", " <git.Commit \"bec53420c19cd2b2f93c40af75f893b03ae71b58\">,\n", " <git.Commit \"22acead72029735f53ffcd795dddd833c6451388\">,\n", " <git.Commit \"6c5a71abc77a1a7ac2d7a7742da66bdbf44017c0\">,\n", " <git.Commit \"960d89315e659726080374f1b3bab353a69c6ebf\">,\n", " <git.Commit \"fe13b7c830afee505f83c36197bec9bb775f465d\">,\n", " <git.Commit \"0c5f5b29122ed8bfb5e3a6542c82386626a11dc2\">,\n", " <git.Commit \"8f0a1119115912323f831b37cd5eb73981711f9d\">,\n", " <git.Commit \"1fe2f73cf578009a4210dac2274d8287baaa00e8\">,\n", " <git.Commit \"468d43094a0085a0b84e05c7f478c661cdced3ad\">,\n", " <git.Commit \"ea313b92a8c26a3ac7384b13af784a4c5c534470\">,\n", " <git.Commit \"1e3fc9129ff29aca44a7c7ed7068ee12fb328f70\">,\n", " <git.Commit \"386f7b7f9709e6ea5a058039126aa8c46e837424\">,\n", " <git.Commit \"30c227634b07cea83a0078e24970e5f5fe913cd8\">,\n", " <git.Commit \"94ad9f9be593d2e56f2444cbc9fbd91367433dcb\">,\n", " <git.Commit \"40a6d7aacd3497fe257b6141694169cca295cab2\">,\n", " <git.Commit \"4d3b09974f2825335ff210660c7a54ec3d54ec1a\">,\n", " <git.Commit \"e6ff40548a1ee26ffc62d93c5d096a3ff90a25c4\">,\n", " <git.Commit \"e6f3b208626ea0d4758f058745919d8d9338373e\">,\n", " <git.Commit \"0fb026449c8c104ab7d1cbac70390b7b934ca693\">,\n", " <git.Commit \"dfeee9a63e70daeb498aedad93a52e0bf82461bd\">,\n", " <git.Commit \"c8920f29f64a730d247e351d19ab97fb53a240e5\">,\n", " <git.Commit \"45ff953d39f9b11a02c0d8ac3a42bad52164a435\">,\n", " <git.Commit \"841f4099ac6c4ca2fea6e7bac2ebf716cd8ad3d6\">,\n", " <git.Commit \"bda47b22f62079d7c37f61e97effbd2cff1fbc91\">,\n", " <git.Commit \"d9255068b788e2a3ee16f5fbd1c47208c7554e52\">,\n", " <git.Commit \"c1cfde53090fbe2ce524178d753cf0ef2ed84533\">,\n", " <git.Commit \"023a135480b99a1c3427c9d4e8ce54ac77d2a917\">,\n", " <git.Commit \"9112b200ab577facaf5c49a32e84647197e64eca\">,\n", " <git.Commit \"5600568ac2dba352155cb3e506d449661cb21599\">,\n", " <git.Commit \"b18525bfdeef704a82c153057c6a0387117c3ed6\">,\n", " <git.Commit \"dc3e755dfb891297c2d0377f5b3b0c1ca3a192a0\">,\n", " <git.Commit \"48794d81387918cc54d750a14700ef5f3fc7fc9d\">,\n", " <git.Commit \"87d0cc5d3e0486061c42ae430e0f591cb8af800b\">,\n", " <git.Commit \"91d617758e6f2916cc63bc9f56070b2703cbd7f5\">,\n", " <git.Commit \"05db669f91820afbfbcf0fea2575ff56ed3b8a38\">,\n", " <git.Commit \"77a34a80a31ab1c00ac70814389b2d2c31828790\">,\n", " <git.Commit \"1b59c3c31613b41d033448e59e5d3f59b3cf9f59\">,\n", " <git.Commit \"e519db03415dcd43e64cd8f59bb333f24e6ec9db\">,\n", " <git.Commit \"5be6e41a65aa23081b5d30057e4c4aa11005f166\">,\n", " <git.Commit \"8df7b0557fb1cca2ca8b65c49e2f634e2c080001\">,\n", " <git.Commit \"0c266b809a3b643d643e32b6660e156fabf85d50\">,\n", " <git.Commit \"36fe8db384d1196c06c0a4de9a069cb49dbfb7c8\">,\n", " <git.Commit \"7a58f78cd0b0dce0deede3da5f82caae8845cc1f\">,\n", " <git.Commit \"a03f83d3dda052ad4edcd57b0cf895ca242c712c\">,\n", " <git.Commit \"6520bd2195fae04277b564b08ec9be098a8b5804\">,\n", " <git.Commit \"84b40928cab05b3592f1c92a521fe75ffd3e9fc9\">,\n", " <git.Commit \"d6ef1990606dced11faf15e41db9fdd290050745\">,\n", " <git.Commit \"d34a5099e189b9dfa4733e6b83ecae37f5da1477\">,\n", " <git.Commit \"bba190697d316a4e02200c20f6838e6eb1086bb9\">,\n", " <git.Commit \"0e51685dcf6eb1e5ae9c124c87e52a3b0e9eeb3d\">,\n", " <git.Commit \"24f72db77d44e4223c144d02de841418e0cb2d97\">,\n", " <git.Commit \"9fc6c6a8b8267ce233f766fe25d104e99c9ce8bb\">,\n", " <git.Commit \"5f6e8e846a92b8d490793d5d86fc320006232548\">,\n", " <git.Commit \"71840b89e4f4d2801cd2f7db02e320e6894f0e9f\">,\n", " <git.Commit \"bcf2caa61fb5a3f95cab3ffafbd82bc60c215de1\">,\n", " <git.Commit \"4eda09048c1abfbe4d7e4ddd4c72f515eb55b2ff\">,\n", " <git.Commit \"cb3b9b4e0508a4ce1c1ba0264f54fa8ed4a1972a\">,\n", " <git.Commit \"0d2eb65e2134e00027c33ef4b0d21c330871b5c9\">,\n", " <git.Commit \"d2c5a7aeadd38a6a42c3712739c527a1e1096fd3\">,\n", " <git.Commit \"cdc9cb2f92d9c30bc827cdf7a658c56323d0558f\">,\n", " <git.Commit \"562a5f742956cd2e03dcb97d5f5c988fa3763d7f\">,\n", " <git.Commit \"bbf15afc58704ff76ba6eee1d0eeede673f2cd91\">,\n", " <git.Commit \"99f17c0dd2103c673763d9bc8162a95f5a9c6a48\">,\n", " <git.Commit \"c366eded752ff8731bdf49a2695d896840944099\">,\n", " <git.Commit \"9bc071939f98c228adeb208a0b301b8cf5d14500\">,\n", " <git.Commit \"a4c0d16d652f93931fce61b956e8cec00ebccac5\">,\n", " <git.Commit \"73c1a8fe8f2c320d76e0aca65ebf3faed49183f3\">,\n", " <git.Commit \"2808c5da5ed3836f0d3becb74845e1c92c8b3023\">,\n", " <git.Commit \"708773916884974d6f3ed105443d394423adbb3d\">,\n", " <git.Commit \"08ab6051f7636b153182d81a39fab00b9001038f\">,\n", " <git.Commit \"7e59a6d9809d01104a87319a6b7e6334eaab54f3\">,\n", " <git.Commit \"98624a4d38e58b97c482d675bec03e2b6a3177d6\">,\n", " <git.Commit \"60fb24d284c33fa91c78dd3c7ed9ef1d75cca1ea\">,\n", " <git.Commit \"19e77e5ff68a27e2d2ae3969fe4d15a7a2451826\">,\n", " <git.Commit \"9da550115ca848cdf4f0fce9259648b4db596e8d\">,\n", " <git.Commit \"396356893749d6fde3baef5907b7596ffeaadee1\">,\n", " <git.Commit \"ea48856bb92ac0b620327571441bec996c4bd822\">,\n", " <git.Commit \"f58696a06361b536a5aa05a2d78cc054e71636ca\">,\n", " <git.Commit \"19af687b700f0a363caae61a74ee70b226a58a4d\">,\n", " <git.Commit \"273753e336f3b07aae6b987a7bbd1c2cc8896179\">,\n", " <git.Commit \"f643e38a24fb3972bc7a9162d7df6f0e0ee1cd29\">,\n", " <git.Commit \"c7fcede3a94a35fe2f9e767dbe6ca70c915b3184\">,\n", " <git.Commit \"7f6bada4112d2c57a03fa2c8652df038e88c8e1b\">,\n", " <git.Commit \"b319c6b7f41c887a1d51aa82ee3ad2c8c99af567\">,\n", " <git.Commit \"8829ab6973190b5b44255f42a464a93bbec50ac1\">,\n", " <git.Commit \"8bd6545c1bad3524e4e7e8ab1aa8fa391e785832\">,\n", " <git.Commit \"3042c7c08e8aa3c65df35a0a7ed29d0c96fbceeb\">,\n", " <git.Commit \"f66d32113a6cfa416bb106a503df4205bb6329e5\">,\n", " <git.Commit \"b25e10dd2c6607da94cd724bc97051ed4f466d5b\">,\n", " <git.Commit \"0b63b7e660a22649477a0b59be3beca3c24a6fb1\">,\n", " <git.Commit \"0f2d85ee4dd8aa84ec7f9af9f5dda0c5ae4a50ae\">,\n", " <git.Commit \"91849527ea97d0a0aa085c9643e11b44f8446b4e\">,\n", " <git.Commit \"8ad118935a872bfe36a444facb2896f415dbbba0\">,\n", " <git.Commit \"a515044c2bf34aaccbabbe49a5ef3bc8e48818cd\">,\n", " <git.Commit \"fd88036b8dc04a5efe27a39765d00f510793a3eb\">,\n", " <git.Commit \"c8d96a8f56796021579acad957603fb9725c5d92\">,\n", " <git.Commit \"a607d06261f1fdb5262f26a9221dc8643104546f\">,\n", " <git.Commit \"b4ab3b5b701ac1620ca782c0ede84c22c4b07d89\">,\n", " <git.Commit \"01d9e3a1056e8c4f0717951e2ac9f156f29209e1\">,\n", " <git.Commit \"e8984c7eb45954de83568ac90a5fce2b7eea1b0c\">,\n", " <git.Commit \"65255ab52270e14bbd8db5295b8c987292c5f0aa\">,\n", " <git.Commit \"a2df71bcc923be9bb580412a4ad690b820ab2b1b\">,\n", " <git.Commit \"115c94931b2c35f7fe53de71dfdb6f648fa6a5f9\">,\n", " <git.Commit \"e137c6748633fa59414651bb426169a878234e7c\">,\n", " <git.Commit \"4bf8483d0295f8c6f2c34c0383721c0cac905ac2\">,\n", " <git.Commit \"176485d1c33d4ab515840dbdb1d25b4163eb64e2\">,\n", " <git.Commit \"86f4652c8bb579baf77f76b0a370a2c3dfcf7d2e\">,\n", " <git.Commit \"7b7c992b7db63bb69388680033197ad993cddf23\">,\n", " <git.Commit \"053161e7ff3f7b03dbb994d39123afa4ff7a0b12\">,\n", " <git.Commit \"144a12296bfb0286d63b69ed527d5cdf62c9c17d\">,\n", " <git.Commit \"d8772de27671fbcf27147527591a047cf50ac1de\">,\n", " <git.Commit \"c4587c2ada1c796a8c0a6c8b5ce119a814835ee0\">,\n", " <git.Commit \"dd88f5c080ccb8d00ee0d4082e68d37ee4a55f02\">,\n", " <git.Commit \"021c247c6ebe858510b8022f679f45e14ea0e986\">,\n", " <git.Commit \"91516f53135968ab75e95bc60b0b8cd81329d134\">,\n", " <git.Commit \"c367c55afaff15b261a3ede9f9c27830d93b50f9\">,\n", " <git.Commit \"98c9701a6ffef4e0ab40ba5d5de40c8ad44b9399\">,\n", " <git.Commit \"10cf40e3587895363f7e2c869da5f9d9cd66e471\">,\n", " <git.Commit \"a5ca25d60e74c56bc39b7565089afee5a6b5031f\">,\n", " <git.Commit \"0befd6474d3a727e340cf137077e172af935560a\">,\n", " <git.Commit \"157307a1e8712456eb3a48265cc42c47011ccf83\">,\n", " <git.Commit \"f5cfb2e603a7129677c3d758beef99e9106e2001\">,\n", " <git.Commit \"617f66b35f890899b9bf178c6d74fec1b26cc7cf\">,\n", " <git.Commit \"ed3d425a6b13a5a55b9013542dc5e55b16c7834e\">,\n", " <git.Commit \"dd6aaf7afcb45c3dea6b4be855930796bc7c2884\">,\n", " <git.Commit \"306e3c01ab1591d4877aab2d4f766fbec7ddb9c0\">,\n", " <git.Commit \"edd192135ab71dd746c0ff50adecf24bda379e6d\">,\n", " <git.Commit \"15cb174f57add729f053c1487dbbf53430407e46\">,\n", " <git.Commit \"db0f63b5cab9ad5ef7e628b577401ed54680a71c\">,\n", " <git.Commit \"4227fbaaf33cecd0ca15adc3f29e1579cc928f80\">,\n", " <git.Commit \"79563379cb6ed5f23df0db93736826ca73c599cc\">,\n", " <git.Commit \"e0711f265df974dd82924f4de4675dce7e32c019\">,\n", " <git.Commit \"5d70203684d5281954b425aa7f7c1925e3d4d6dd\">,\n", " <git.Commit \"44236edd2efd576b256f1a717ab65cce88125532\">,\n", " <git.Commit \"4132fbaacd46ce502d8b46bd4973ae7beb12dd2c\">,\n", " <git.Commit \"1a773563d8ff751bae23a9d02df29d90354b4570\">,\n", " <git.Commit \"5c5dca0d497a761d7a29a5433cac838065982679\">,\n", " <git.Commit \"ff24c47af05f821203c163f0b8877144a01d1c01\">,\n", " <git.Commit \"e3e611857d936af09fafb359e0975e002e3b1125\">,\n", " <git.Commit \"dd51f89d5cd4e547a8b80c878ebed80172378e0b\">,\n", " <git.Commit \"81a99895f7dfcae346105bea740b61a63828bb74\">,\n", " <git.Commit \"15d034ae79e398997ac096849283a96ceb9bf2c2\">,\n", " <git.Commit \"939b67cf261cee61e91092437f67efa9193da7f9\">,\n", " <git.Commit \"c467c698398ed6a9d1edb771b9a76ae4058ce668\">,\n", " <git.Commit \"4c67657b1bfd07a36f5ff7facf828affb5811b3a\">,\n", " <git.Commit \"181066c7b12387d1be08d8041544b4fd273c0446\">,\n", " <git.Commit \"b94081f5a554a2f0acc4cb6a8df075c4990b3898\">,\n", " <git.Commit \"740b6c0eced4869fb8cd08e347775dab55ed2bc7\">,\n", " <git.Commit \"400d801519df9cdac2012779fe385f973e533233\">,\n", " <git.Commit \"62d1f394a30a88fe90647bccbf788e1e864aab93\">,\n", " <git.Commit \"59e79af1768dd554c2f4ab9d5d91671878c8fb3a\">,\n", " <git.Commit \"2399bef5ee5eed407fb94b45c5411a11857cbe5f\">,\n", " <git.Commit \"b98aa04711099c8719fc8349e687435b2da7d2f7\">,\n", " <git.Commit \"a4a35854d0664981bd3f5d9a825ef35b7f363404\">,\n", " <git.Commit \"49655e1ce1ba565bc997efa24c61abb38401fa1b\">,\n", " <git.Commit \"18a63a39d957482c6c320a612e9fa6ae785bc41e\">,\n", " <git.Commit \"dd7f235e04aed0c6957ea5490ce94e06771f992f\">,\n", " <git.Commit \"c436abb34aa26006b8a7320c925548c0e74ceee3\">,\n", " <git.Commit \"d18d89b8b66b29923b4c9f631bf26c9dc7dfa068\">,\n", " <git.Commit \"d79d6f87a0839db5d906e2613941c98ea5889c89\">,\n", " <git.Commit \"677507d62f93e30e5fb9c0e58e9b085acf647086\">,\n", " <git.Commit \"288f87572660a1c803862e76f1c170693642a100\">,\n", " <git.Commit \"e7aa52903248a8bc83c2dbbe11530c7ae20788fd\">,\n", " <git.Commit \"c9637bac50d2e1c3e52db9451fb541284b78ad88\">,\n", " <git.Commit \"645520df5b33c7f8e4298dcb7d9bd8fe02b788a4\">,\n", " <git.Commit \"570d11ca5534e06938c394e4a08963a41e50afea\">,\n", " <git.Commit \"942936a7f0dd4929e7dd4aa74dac090fb7bf0ce2\">,\n", " <git.Commit \"42befd540071387ef3a7f0ff7e66abcf94afaa79\">,\n", " <git.Commit \"1e599a506308733353d903c66aa2262b22382a96\">,\n", " <git.Commit \"e3d7af99c51fe57c2e195593db9131c38c4cd405\">,\n", " <git.Commit \"69d019bee45e7d2631390d326e4069aadd0255e5\">,\n", " <git.Commit \"20aaffb83915b68be75022b4e9693311bb69ae16\">,\n", " <git.Commit \"1813a14bbeacd99716923c8bc5f706780be51273\">,\n", " <git.Commit \"079404c237993e4e0f1676ba4a142c97e02f71d0\">,\n", " <git.Commit \"2532bf1de81773149d9c3eebffecc4bfb25e2a04\">,\n", " <git.Commit \"8baef749ee0275dd3d2559219a9d3283e356863d\">,\n", " <git.Commit \"77805681c3b21503346c0183b3d69fd208dc4c87\">,\n", " <git.Commit \"5abbdfe27da15067d589883012f7129d05e5ea93\">,\n", " <git.Commit \"fa37a50c8c32fe14b8f9c250167072ea8020d9c6\">,\n", " <git.Commit \"c285aa3576469199df4a424a287b6e5529307cfb\">,\n", " <git.Commit \"0e9f99811bca25fed302fc4451624b0ab5dd7f4d\">,\n", " <git.Commit \"3d691176b87e21e3f4b3e93edc0360fe0eba5fa7\">,\n", " <git.Commit \"2c5bba3fb05ac3d88aaa01e04167e091ee11c37b\">,\n", " <git.Commit \"e3ea34a4f470f7617e92862c021e7586b3c1c4a7\">,\n", " <git.Commit \"a36c272e2844fd1c7b521c26f898527ce0c4b43a\">,\n", " <git.Commit \"859d6bbf221a21b3205a625f36650f13122d8536\">,\n", " <git.Commit \"368467dfad14adc0f85dd43e47281422f647fd65\">,\n", " <git.Commit \"3615504b76d05e7344254c7f89a34338f493c5cf\">,\n", " <git.Commit \"02f09f1d38b6b7f6c9b8d90f9fd294817f6e6546\">,\n", " <git.Commit \"27314da99a51d1304140169227053caa3448c942\">,\n", " <git.Commit \"e1060b93afb07265165c5a92e1f04bb1517b62fd\">,\n", " <git.Commit \"6bf723a3f7257b803afde64c93d4189bb8f7772d\">,\n", " <git.Commit \"c749707d9809988ed3683d1bac17464052af5fb1\">,\n", " <git.Commit \"033339182b2cab4661b0348998bb9d7e266c2840\">,\n", " <git.Commit \"e0346dca01d6bc88228804e72f245a1ae0026669\">,\n", " <git.Commit \"dee8533df94cf4a6258936d2f55f79309aa48d08\">,\n", " <git.Commit \"c041191aea13e6ce97c1fbea321e2d2f9c0c2cdc\">,\n", " <git.Commit \"86fdce2b43c92eb416988ae998286cd7aba2db42\">,\n", " <git.Commit \"650e1951a86b93ea29b215349b10aac6fb703c2d\">,\n", " <git.Commit \"d8a2e96c787c8c7437b0e91ea0b22082569312d3\">,\n", " <git.Commit \"55b0380865a6567e9a9f054e7542b985bfdd10e5\">,\n", " <git.Commit \"276f37c08178729287f339ee1ac998f5b8e86307\">,\n", " <git.Commit \"179a79675ce59fdc945c56f1dab668a5ce96dc3d\">,\n", " <git.Commit \"1dfafd5c6b0ad39ed221a519b653d4b35bf0964b\">,\n", " <git.Commit \"7139ab742927a20e6e3360a2228a802892917819\">,\n", " <git.Commit \"6ec662459fc420e5e7c813d6404a76b6b0b64c18\">,\n", " <git.Commit \"33b7f6666d5af760302754dda3e59a2e66dc8f26\">,\n", " <git.Commit \"e700fdac7eddd33ea7dabd5a855ec41422832afc\">,\n", " <git.Commit \"e9e89f19f38dcee89d13943b23b0585d374c3854\">,\n", " <git.Commit \"53aaa7aa00774dab0b1d3eab2e7fa10eb7f6ca94\">,\n", " <git.Commit \"468364302df36986f17438f329951a661a679468\">,\n", " <git.Commit \"ca48a639c593e91810dd44d9566569ebb926a218\">,\n", " <git.Commit \"a956a915018292cb360781d19d622443d5f93421\">,\n", " <git.Commit \"deaa69f89e2a3da703411b70040736e3cf58a285\">,\n", " <git.Commit \"9b68bbde0b616492d88d0fe3f9e30b4eb3fb4780\">,\n", " <git.Commit \"eb2886e686179564de758f2e8de216934534a4ef\">,\n", " <git.Commit \"91e9201bea0e3215f37ee9a81756bedd603b315f\">,\n", " <git.Commit \"8203757d1ef3c35bf1be31e4fc95b1e2006c6059\">,\n", " <git.Commit \"d92ac0ce51be151d4157feb2cc256abf7ba8c63b\">,\n", " <git.Commit \"45072bef37b3342f71c19c9f1f2da1db80948708\">,\n", " <git.Commit \"5f292edafceb02f540d010158804c18271a71aaf\">,\n", " <git.Commit \"1e2255e14f1b4edeb97bbbd5d98fdbdf3b4592aa\">,\n", " <git.Commit \"0f83d3b12721f60277b7c91ab2be70c671a2ef0c\">,\n", " <git.Commit \"218548c3b4bbf6cfb816901bad45c580f1452a32\">,\n", " <git.Commit \"f543fad37d1c8547c30da312e24b364b7f50d7aa\">,\n", " <git.Commit \"26eb22b895e72c22501018656d1f734d3dcf2ca3\">,\n", " <git.Commit \"386a6f89a3121cdf76a96842146ee0c99ad8d7e3\">,\n", " <git.Commit \"70e9e1c43ffad15fec7bf4532c0d6d9fb65a7421\">,\n", " <git.Commit \"560229b101fd7b0dff8db6100f6ac55990702545\">,\n", " <git.Commit \"db6a585467eb7ab219200fa456819662c5f59879\">,\n", " <git.Commit \"61236c2d0921d0d707770a733d8411a8e4aa53ee\">,\n", " <git.Commit \"f0d9191ffccaa8bba36f3560520c530213ab74ab\">,\n", " <git.Commit \"2aa033e0b0a12521291874f4076c8340d2466761\">,\n", " <git.Commit \"990889e09b621cdbc9274274949296454b9afa30\">,\n", " <git.Commit \"a2c5e9c677d686979ff0bd9d13643aa0f24c6828\">,\n", " <git.Commit \"5f90e480781b255fcbe47f867036861f041d070f\">,\n", " <git.Commit \"4de197f4c4be0490a19efd77b82110109c40e9b0\">,\n", " <git.Commit \"31af9ab6d01f8e1dc6ee88b01f639aaaa7b1b9e7\">,\n", " <git.Commit \"f615325a2db8c81d372236019009c2f227998292\">,\n", " <git.Commit \"da83ca8dd05442d21e97914defeba5b8d6c8295d\">,\n", " <git.Commit \"4083ec4b6bc0efa8141e426b99fe0aef9f1f7642\">,\n", " <git.Commit \"7bec3b85f825905f9348df5054de3839aca1fb24\">,\n", " <git.Commit \"14e9b70206f6e2e08aa0ee80a93b53f069a07182\">,\n", " <git.Commit \"91fc33cb23caeb2ad89279af6d4d120532c2d8b6\">,\n", " <git.Commit \"6944c55aeee3371adf141d67ae83db880d30a942\">,\n", " <git.Commit \"432956aae9b246543ad5f1d20a2d914e16ebce67\">,\n", " <git.Commit \"8442751b5f536e6ab96817cacfef9d3598c9ee1d\">,\n", " <git.Commit \"fc63d8fe42b14eb5d36efd6ed951dcd000d96912\">,\n", " <git.Commit \"9c3902f9ba2ef1873fcd638d92ae029ff227abcf\">,\n", " <git.Commit \"b27c3d4a14585640d828411135b7c78bb8a5fc84\">,\n", " <git.Commit \"c0ae414e8f51988f254251f73f3e9165f02b35fb\">,\n", " <git.Commit \"e7fb967d557c1962226c2d4fce8c0ed70bc4e672\">,\n", " <git.Commit \"d8fdc0dd20bf6abe16e72f377fb9f501f3b97efc\">,\n", " <git.Commit \"0132f5c1d480507e68a175cf04d8688a2e406330\">,\n", " <git.Commit \"d75b5fce18f0d09ea72f05f55150dfc153c94cb4\">,\n", " <git.Commit \"3e2b0a137eee4ad838c97396f10c6e0288d787e9\">,\n", " <git.Commit \"712275037f992e4295c9db119379a37324e055ce\">,\n", " <git.Commit \"22ca31bbb52632b42ab8d7ec61114355e59546a7\">,\n", " <git.Commit \"6f29e536d7aa191c7a554061f1acc5135830dd6b\">,\n", " <git.Commit \"ca7b1e3de286565514c5cc0c78b063e74a03cc01\">,\n", " <git.Commit \"6faad25f2de21b4eee803fec9087bc18bdc7ed2b\">,\n", " <git.Commit \"65daae4668cbc793ce62018464690fe03dd4e1b6\">,\n", " <git.Commit \"2c72356c9c2f94f9c984fcbc5803a719a6a6712e\">,\n", " <git.Commit \"1982ba33b5fd391aae0b8a847846b71903b3c759\">,\n", " <git.Commit \"fee635b5567fe7fa60a56d1b20876be58c85a35f\">,\n", " <git.Commit \"c36f7e54774a97eb2f3310b98c06cb1aec88b20c\">,\n", " <git.Commit \"24b02736433db61e2280bd040f2b0eeb513bbcbe\">,\n", " <git.Commit \"74a2d01f8eef7e13340c6134e81440ca84a7d75a\">,\n", " <git.Commit \"2f24cc14db223da462677a7ad11658c4ca2d57c7\">,\n", " <git.Commit \"2129ab60fb0de45f3c513c43d843a987390ecc60\">,\n", " <git.Commit \"a27ac6278cdbbfb08aea971bfb2bcb977d0885e3\">,\n", " <git.Commit \"9bf7ad5b6d0c374589cf61a72d1c6fba732f632c\">,\n", " <git.Commit \"5367b01b10a2c27c188a1c95b0076e742381572b\">,\n", " <git.Commit \"8d3666cc5c6528eb8b5ce73aa2f7cadb1134dc77\">,\n", " <git.Commit \"a58ae723a71450b8f02a81bb290123c74fc874aa\">,\n", " <git.Commit \"fabb1d642bbe369ff84513c75f3544612e8902b5\">,\n", " <git.Commit \"7e0aba298dc09f3a8662d0e232099d0b611eb8b8\">,\n", " <git.Commit \"b038f4cce29eb55f44fa63e41d28ab91b8494e23\">,\n", " <git.Commit \"78c4f0efd90bbded751163853d183e1b6e197363\">,\n", " <git.Commit \"8d278adaa24da9d0ce6fcd446653e9daab65b020\">,\n", " <git.Commit \"e81ea9b25ff438370c413112d1227396ffdec9e3\">,\n", " <git.Commit \"8431d42a6a270cbb96cb76a4ae64c657a2b07934\">,\n", " <git.Commit \"9de1aa67d4706b5f3f31fed7471018ac3c1a2db3\">,\n", " <git.Commit \"a2873718bb058e70d1e1caa304dd4927b031eaf7\">,\n", " <git.Commit \"df3622f5dedff3499acb6d5c770d0f70eabc5210\">,\n", " <git.Commit \"1d71f7cb6179e73f2bfa3cdc6e36a33c1dc56e27\">,\n", " <git.Commit \"87b3d659a250d3632eaf6cd22d5ddc4cab601bd2\">,\n", " <git.Commit \"efcf826b2515ed9daa3ebbb48c354c1fb606adb7\">,\n", " <git.Commit \"0bc44937107092cf0e663b413231142c64a2b9b9\">,\n", " <git.Commit \"46653b9154b44c6e68924d487313728aea069cdd\">,\n", " <git.Commit \"dcdb43f6019916dd7ef4fc4c8dd371c95f544343\">,\n", " <git.Commit \"3fa8b966b75900582787d3e894032abbc774d208\">,\n", " <git.Commit \"ccf261963242a432c526680e0ed97eca80818a36\">,\n", " <git.Commit \"a10730782a9cd6b939eb4dd27ea807a796d0d544\">,\n", " <git.Commit \"784eb7226446eec8bcb2c0ff4c221d3846b177b0\">,\n", " <git.Commit \"429a3b29df2b863b272ee018ccd6a0db6ce38f2e\">,\n", " <git.Commit \"4ec5f33ed39471c00c13614327fb0d32c9384f86\">,\n", " <git.Commit \"a2a1bd2b14fb6a535d74f46216c9bb23536ba667\">,\n", " <git.Commit \"ba83d586077d0a80353bd84e6fe856c0037bc338\">,\n", " <git.Commit \"c80354f6946fd837dc51a97b82ce9b2303e63f71\">,\n", " <git.Commit \"ffeeac9e506a278b215c57910906e23f3629605c\">,\n", " <git.Commit \"23cc51cce452e2e09d555679f6dd3d58f16daa9b\">,\n", " <git.Commit \"fe1e2fd94cdd2dee3eb86a2a508db9a0e4c9f2f4\">,\n", " <git.Commit \"5322991e3aa50ecfa3647b28daed44a0a8357b7f\">,\n", " <git.Commit \"5aecbce3d9cb900a6301a5519a7cd2d0daead677\">,\n", " <git.Commit \"6ff72b22b1d6bf832a98883906ff987fabaf95f8\">,\n", " <git.Commit \"fea65c7addba541fad307b5c677797f33bcb2eea\">,\n", " <git.Commit \"ce6f6200c2aae10d1a5e14aabe5daedb4e384fae\">,\n", " <git.Commit \"685a076514464ff9f6a2e80d9423a669e8f85b35\">,\n", " <git.Commit \"0d70145d1c7f0415aad6f5a1ef23294bea261c26\">,\n", " <git.Commit \"797fdb2e2d40bef203725f85893a9b8c9c3769ae\">,\n", " <git.Commit \"5728d874e0b3dee508448a393a9d96e705eefa60\">,\n", " <git.Commit \"6e8be8df843cf4763555ab149fe5204c507f16bb\">,\n", " <git.Commit \"7da3e7cb140e1bd57d8d2d675a4638e2f68d2459\">,\n", " <git.Commit \"4a7180243a88a0ae604d6180a750dea72f25ddb1\">,\n", " <git.Commit \"0eefb5bcd6a971868dbef4b330a8ad88e99298fd\">,\n", " <git.Commit \"44bb2ff66467cab148e41be0e3f23441ebd46999\">,\n", " <git.Commit \"c087c9784af2279437a12470920fa6939e238163\">,\n", " <git.Commit \"83557a2d1da522ca6fa75281e089540e7e44bc3f\">,\n", " <git.Commit \"19faca7cc49bec8cd3fef3f9014578c86ddd427a\">,\n", " <git.Commit \"08dfe9c94ca6d20c5fc6066eeff5ec4035ccb958\">,\n", " <git.Commit \"cf28c7f35bd9e80f1e0aff46605b8d33d94c29b2\">,\n", " <git.Commit \"5de2b5043275048171582dc8f6cce744f3be0176\">,\n", " <git.Commit \"1e445eff0ee18de896b682a418dc9d93f9575734\">,\n", " <git.Commit \"67f9bc8c3c6a15b893017226a7622a9402e182dc\">,\n", " <git.Commit \"decafc6f13a03c6df5a4234e543708ed66355ca9\">,\n", " <git.Commit \"fe207b1b6cdafbf57c94cb8894b8a4ec6c123f1e\">,\n", " <git.Commit \"222a25ad61c20cefa519726238ea44cf482b2f65\">,\n", " <git.Commit \"234987418c1063ab78223bf7f4b20f2de64dcd38\">,\n", " <git.Commit \"20acd4df84f4319fae3934a29231392be774a1b3\">,\n", " <git.Commit \"bca8914f17a8a619843281ec280988cb2540ebfc\">,\n", " <git.Commit \"6a48b118f3c6feab8fffce9f7e220dbf14f1f8cf\">,\n", " <git.Commit \"0fe0db5db9107318794d129fa268f7d0ceee2aeb\">,\n", " <git.Commit \"f3e3214c7a5ffff709d6c4d86442d0fbf686be2e\">]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["commits = list(repo.iter_commits(\"master\"))[::-1]\n", "commits"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<git.diff.Diff at 0x1074701f0>]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["for i in range(1, len(commits)):\n", "    commits[i].diff(commits[0])\n", "    '\\n'.join(commits[1].diff(commits[0],create_patch=True))"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["'public/index.html\\n=======================================================\\nlhs: None\\nrhs: 100644 | a1d50995c53e3f1d72bf6eaf8f0b203c8c34569d\\nfile added in rhs\\n---@@ -0,0 +1,241 @@\\n+<!DOCTYPE html>\\n+<html>\\n+  <head>\\n+    <title>Ruby on Rails: Welcome aboard</title>\\n+    <style type=\"text/css\" media=\"screen\">\\n+      body {\\n+        margin: 0;\\n+        margin-bottom: 25px;\\n+        padding: 0;\\n+        background-color: #f0f0f0;\\n+        font-family: \"Lucida Grande\", \"Bitstream Vera Sans\", \"Verdana\";\\n+        font-size: 13px;\\n+        color: #333;\\n+      }\\n+\\n+      h1 {\\n+        font-size: 28px;\\n+        color: #000;\\n+      }\\n+\\n+      a  {color: #03c}\\n+      a:hover {\\n+        background-color: #03c;\\n+        color: white;\\n+        text-decoration: none;\\n+      }\\n+\\n+\\n+      #page {\\n+        background-color: #f0f0f0;\\n+        width: 750px;\\n+        margin: 0;\\n+        margin-left: auto;\\n+        margin-right: auto;\\n+      }\\n+\\n+      #content {\\n+        float: left;\\n+        background-color: white;\\n+        border: 3px solid #aaa;\\n+        border-top: none;\\n+        padding: 25px;\\n+        width: 500px;\\n+      }\\n+\\n+      #sidebar {\\n+        float: right;\\n+        width: 175px;\\n+      }\\n+\\n+      #footer {\\n+        clear: both;\\n+      }\\n+\\n+      #header, #about, #getting-started {\\n+        padding-left: 75px;\\n+        padding-right: 30px;\\n+      }\\n+\\n+\\n+      #header {\\n+        background-image: url(\"assets/rails.png\");\\n+        background-repeat: no-repeat;\\n+        background-position: top left;\\n+        height: 64px;\\n+      }\\n+      #header h1, #header h2 {margin: 0}\\n+      #header h2 {\\n+        color: #888;\\n+        font-weight: normal;\\n+        font-size: 16px;\\n+      }\\n+\\n+\\n+      #about h3 {\\n+        margin: 0;\\n+        margin-bottom: 10px;\\n+        font-size: 14px;\\n+      }\\n+\\n+      #about-content {\\n+        background-color: #ffd;\\n+        border: 1px solid #fc0;\\n+        margin-left: -55px;\\n+        margin-right: -10px;\\n+      }\\n+      #about-content table {\\n+        margin-top: 10px;\\n+        margin-bottom: 10px;\\n+        font-size: 11px;\\n+        border-collapse: collapse;\\n+      }\\n+      #about-content td {\\n+        padding: 10px;\\n+        padding-top: 3px;\\n+        padding-bottom: 3px;\\n+      }\\n+      #about-content td.name  {color: #555}\\n+      #about-content td.value {color: #000}\\n+\\n+      #about-content ul {\\n+        padding: 0;\\n+        list-style-type: none;\\n+      }\\n+\\n+      #about-content.failure {\\n+        background-color: #fcc;\\n+        border: 1px solid #f00;\\n+      }\\n+      #about-content.failure p {\\n+        margin: 0;\\n+        padding: 10px;\\n+      }\\n+\\n+\\n+      #getting-started {\\n+        border-top: 1px solid #ccc;\\n+        margin-top: 25px;\\n+        padding-top: 15px;\\n+      }\\n+      #getting-started h1 {\\n+        margin: 0;\\n+        font-size: 20px;\\n+      }\\n+      #getting-started h2 {\\n+        margin: 0;\\n+        font-size: 14px;\\n+        font-weight: normal;\\n+        color: #333;\\n+        margin-bottom: 25px;\\n+      }\\n+      #getting-started ol {\\n+        margin-left: 0;\\n+        padding-left: 0;\\n+      }\\n+      #getting-started li {\\n+        font-size: 18px;\\n+        color: #888;\\n+        margin-bottom: 25px;\\n+      }\\n+      #getting-started li h2 {\\n+        margin: 0;\\n+        font-weight: normal;\\n+        font-size: 18px;\\n+        color: #333;\\n+      }\\n+      #getting-started li p {\\n+        color: #555;\\n+        font-size: 13px;\\n+      }\\n+\\n+\\n+      #sidebar ul {\\n+        margin-left: 0;\\n+        padding-left: 0;\\n+      }\\n+      #sidebar ul h3 {\\n+        margin-top: 25px;\\n+        font-size: 16px;\\n+        padding-bottom: 10px;\\n+        border-bottom: 1px solid #ccc;\\n+      }\\n+      #sidebar li {\\n+        list-style-type: none;\\n+      }\\n+      #sidebar ul.links li {\\n+        margin-bottom: 5px;\\n+      }\\n+\\n+      .filename {\\n+        font-style: italic;\\n+      }\\n+    </style>\\n+    <script type=\"text/javascript\">\\n+      function about() {\\n+        info = document.getElementById(\\'about-content\\');\\n+        if (window.XMLHttpRequest)\\n+          { xhr = new XMLHttpRequest(); }\\n+        else\\n+          { xhr = new ActiveXObject(\"Microsoft.XMLHTTP\"); }\\n+        xhr.open(\"GET\",\"rails/info/properties\",false);\\n+        xhr.send(\"\");\\n+        info.innerHTML = xhr.responseText;\\n+        info.style.display = \\'block\\'\\n+      }\\n+    </script>\\n+  </head>\\n+  <body>\\n+    <div id=\"page\">\\n+      <div id=\"sidebar\">\\n+        <ul id=\"sidebar-items\">\\n+          <li>\\n+            <h3>Browse the documentation</h3>\\n+            <ul class=\"links\">\\n+              <li><a href=\"http://guides.rubyonrails.org/\">Rails Guides</a></li>\\n+              <li><a href=\"http://api.rubyonrails.org/\">Rails API</a></li>\\n+              <li><a href=\"http://www.ruby-doc.org/core/\">Ruby core</a></li>\\n+              <li><a href=\"http://www.ruby-doc.org/stdlib/\">Ruby standard library</a></li>\\n+            </ul>\\n+          </li>\\n+        </ul>\\n+      </div>\\n+\\n+      <div id=\"content\">\\n+        <div id=\"header\">\\n+          <h1>Welcome aboard</h1>\\n+          <h2>You&rsquo;re riding Ruby on Rails!</h2>\\n+        </div>\\n+\\n+        <div id=\"about\">\\n+          <h3><a href=\"rails/info/properties\" onclick=\"about(); return false\">About your application&rsquo;s environment</a></h3>\\n+          <div id=\"about-content\" style=\"display: none\"></div>\\n+        </div>\\n+\\n+        <div id=\"getting-started\">\\n+          <h1>Getting started</h1>\\n+          <h2>Here&rsquo;s how to get rolling:</h2>\\n+\\n+          <ol>\\n+            <li>\\n+              <h2>Use <code>rails generate</code> to create your models and controllers</h2>\\n+              <p>To see all available options, run it without parameters.</p>\\n+            </li>\\n+\\n+            <li>\\n+              <h2>Set up a default route and remove <span class=\"filename\">public/index.html</span></h2>\\n+              <p>Routes are set up in <span class=\"filename\">config/routes.rb</span>.</p>\\n+            </li>\\n+\\n+            <li>\\n+              <h2>Create your database</h2>\\n+              <p>Run <code>rake db:create</code> to create your database. If you\\'re not using SQLite (the default), edit <span class=\"filename\">config/database.yml</span> with your username and password.</p>\\n+            </li>\\n+          </ol>\\n+        </div>\\n+      </div>\\n+\\n+      <div id=\"footer\">&nbsp;</div>\\n+    </div>\\n+  </body>\\n+</html>\\n\\n---'"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["diff_messages = commits[1].diff(commits[0],create_patch=True)\n", "'\\n'.join(list(map(str, diff_messages)))"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["358\n"]}], "source": ["filter_commits = []\n", "for commit in commits:\n", "    lowercase_commit = commit.summary.lower()\n", "    if (\n", "        lowercase_commit.find(\"merge\") != -1\n", "        or lowercase_commit.find(\"bump\") != -1\n", "        or lowercase_commit.find(\"readme\") != -1\n", "        or lowercase_commit.find(\"delete\") != -1\n", "        or lowercase_commit.find(\"downgrade\") != -1\n", "        or lowercase_commit.find(\"upgrade\") != -1\n", "        or lowercase_commit.find(\"refactor\") != -1\n", "        or lowercase_commit.find(\"update\") != -1\n", "        or lowercase_commit.find(\"remove\") != -1\n", "        or lowercase_commit.find(\"deploy\") != -1\n", "    ):\n", "        continue\n", "    filter_commits.append(commit)\n", "print(len(filter_commits))"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["for commit in filter_commits:\n", "    diff_messages = commits[1].diff(commits[0], create_patch=True)\n", "    diff_patch = \"\\n\".join(list(map(str, diff_messages)))"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["def list_files_in_commit(commit):\n", "    \"\"\"\n", "    Lists all the files in a repo at a given commit\n", "\n", "    :param commit: A gitpython Commit object\n", "    \"\"\"\n", "    file_list = []\n", "    dir_list = []\n", "    stack = [commit.tree]\n", "    while len(stack) > 0:\n", "        tree = stack.pop()\n", "        # enumerate blobs (files) at this level\n", "        for b in tree.blobs:\n", "            file_list.append(b.path)\n", "        for subtree in tree.trees:\n", "            stack.append(subtree)\n", "    # you can return dir_list if you want directories too\n", "    return file_list"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["# The prompts format is jinja template\n", "\n", "SUMMARY_SYSTEM_PROMPT = \"\"\"\n", "You are a responsible software engineer and always write good summaries.\n", "Below is the project you are working on:\n", "\n", "# Context\n", "\n", "{FILE_TREE}\n", "{GIT_DIFF}\n", "\n", "\"\"\"  # noqa\n", "\n", "SUMMARY_USER_PROMPT = \"\"\"\n", "Please analyze the project context, understand the context, task and file diff changes. Identify key elements like:\n", "\n", "Which files are affected?\n", "What types of changes were made (e.g., new features, bug fixes, refactoring, documentation, testing)?\n", "\n", "Try to understand what the purpose the diff changes is for.\n", "Then you should generate a summary that succinctly summarizes the changes in English.\n", "\n", "The summary should include:\n", "A title that clearly states the purpose of the changes.\n", "A detailed description if the changes are complex or need further explanation.\n", "A procedure of steps to modify files according to diff messages. Imagine you are splitting the diff messages into smaller, precise, human-friendly steps.\n", "\n", "You should return json response using below format, and DO NOT include '```' or '```json' in beginning and end of the output:\n", "{{\n", "    'title': str, # purpose of the changes.\n", "    'description': str # further explanation of the changes\n", "    'tags': list[str] # a list of tags to describes the topic of the changes, e.g. programming language, used framwork, and any other word or phrase to help identify the changes\n", "    'steps': list[object] # a list of detailed step by step instructions of what file diff do and what goal to implement\n", "}}\n", "below is an example:\n", "{{\n", "    \"title\": \"Add read_cars API to FastAPI application\",\n", "    \"description\": \"Implement a new /car GET endpoint to return all car data and update car data model as necessary.\",\n", "    \"tags\": [\"FastAPI\", \"Python\"],\n", "    \"steps\": [\n", "        {{\n", "            \"title\": \"Add GET endpoint for car data\",\n", "            \"description\": \"Implement a new route in api.py that handles GET requests to '/car' and returns car data from cars.json.\",\n", "        }},\n", "        {{\n", "            \"title\": \"Update Car Model in models.py\",\n", "            \"description\": \"Revise the car model in models.py to ensure it accommodates all necessary attributes for the API response.\",\n", "        }},\n", "        {{\n", "            \"title\": \"Route requests to the new endpoint\",\n", "            \"description\": \"Modify the main.py file to include the new '/car' route in the API.\",\n", "        }}\n", "    ]\n", "}}\n", "\"\"\"  # noqa\n", "\n", "PLAYBOOK_PROCEDURE = \"\"\"\n", "## Overview\n", "{{ procedure.title }}\n", "{{ procedure.description }}\n", "\n", "{% if procedure.steps -%}\n", "## Procedure\n", "{% for step in procedure.steps -%}\n", "{{ loop.index }}. {{ step.title }}: {{ step.description }}\n", "{% endfor %}\n", "{% endif %}\n", "\"\"\"\n", "\n", "PLAYBOOK_CONTENT_TEMPLATE = \"\"\"\n", "{PLAYBOOK_PROCEDURE}\n", "\n", "{FILE_TREE}\n", "{GIT_DIFF}\n", "\"\"\"  # noqa\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from jinja2 import Template\n", "\n", "\n", "class Step(BaseModel):\n", "    title: str = Field(default='')\n", "    description: str = Field(default='')\n", "\n", "\n", "class Procedure(BaseModel):\n", "    title: str = Field(default='')\n", "    description: str = Field(default='')\n", "    tags: list[str] = Field(default=[])\n", "    steps: list[Step] = Field(default=[])\n", "\n", "def summary(file_tree, git_diff):\n", "    sysmtem_prompt = SUMMARY_SYSTEM_PROMPT.format(FILE_TREE=file_tree, GIT_DIFF=git_diff)\n", "    user_prompt = SUMMARY_USER_PROMPT\n", "    summaried_procedure_model = litellm.completion(model='gpt-4o-mini', messages=[\n", "        {\"content\": sysmtem_prompt, \"role\":\"system\"},\n", "        {\"content\": user_prompt, \"role\":\"user\"}\n", "    ], response_model=Procedure)\n", "    playbook_procedure = Template(PLAYBOOK_PROCEDURE).render(procedure=summaried_procedure_model)\n", "    playbook_content = PLAYBOOK_CONTENT_TEMPLATE.format(PLAYBOOK_PROCEDURE=playbook_procedure, FILE_TREE=file_tree, GIT_DIFF=git_diff)\n", "    playbook = Playbook(\n", "        title=summaried_procedure_model.title,\n", "        description=summaried_procedure_model.description,\n", "        original_content=playbook_content,\n", "        tags=summaried_procedure_model.tags+['internal_test_only'],\n", "        applicable_rules=[],\n", "        source='AI',\n", "        status='ready'\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}