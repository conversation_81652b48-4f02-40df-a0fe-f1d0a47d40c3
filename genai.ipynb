{"cells": [{"cell_type": "code", "execution_count": 7, "id": "61c8b5ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["That's a big question, as \"AI\" is a broad field! But let's break down the core concepts of how most modern AI, especially Machine Learning (which powers a lot of what we call AI), works.\n", "\n", "Think of it like this: **AI learns from data to make predictions or decisions, much like a human learns from experience.**\n", "\n", "Here's a simplified breakdown of the common process:\n", "\n", "1.  **Data is King (The Fuel):**\n", "    *   AI systems need vast amounts of data to learn from. This data can be anything: images, text, numbers, sounds, sensor readings, etc.\n", "    *   **Example:** To build an AI that recognizes cats in photos, you'd feed it thousands (or millions) of photos, some labeled \"cat\" and some \"not cat.\"\n", "\n", "2.  **Algorithms (The Engine/Recipe):**\n", "    *   Algorithms are sets of rules or instructions that the AI uses to process the data and learn patterns. There are many different types of algorithms suited for different tasks (e.g., classification, regression, clustering).\n", "    *   **Example:** For cat recognition, you might use a \"Convolutional Neural Network\" (CNN), an algorithm particularly good at image processing.\n", "\n", "3.  **Training (The Learning Process):**\n", "    *   This is where the magic happens. The chosen algorithm processes the input data.\n", "    *   **For supervised learning (most common):**\n", "        *   The AI makes a prediction based on the input data (e.g., \"This photo is a cat\").\n", "        *   It then compares its prediction to the correct label (the \"ground truth\" provided in the training data).\n", "        *   If the prediction is wrong, the algorithm adjusts its internal parameters (called \"weights\" and \"biases\" in neural networks) to try and make a better prediction next time.\n", "        *   This process is repeated millions of times, with the AI gradually getting better at making correct predictions. This is often done through a process called \"backpropagation\" in neural networks, where the error is sent back through the network to adjust the parameters.\n", "    *   **Analogy:** Imagine a child learning to identify a cat. You show them a cat and say \"cat.\" You show them a dog and say \"dog.\" After many examples, they learn the features (fur, pointy ears, whiskers, meow) that distinguish a cat.\n", "\n", "4.  **Model (The Trained Brain):**\n", "    *   The \"model\" is the output of the training process. It's essentially the algorithm with all its parameters fine-tuned based on the data it has learned from. It has \"learned\" the patterns and relationships in the data.\n", "    *   **Example:** The trained cat recognition model now contains the learned features that define what a cat looks like, according to the data it was trained on.\n", "\n", "5.  **Inference/Prediction (Using the AI):**\n", "    *   Once the model is trained, you can feed it new, unseen data.\n", "    *   The model will use what it learned during training to make a prediction or decision about this new data.\n", "    *   **Example:** You show the trained cat model a brand new photo it has never seen before, and it predicts \"cat\" or \"not cat\" with a certain level of confidence.\n", "\n", "**Key Types of AI Learning:**\n", "\n", "*   **Supervised Learning:** The AI is trained on labeled data (input + correct output). Most common for tasks like image classification, spam detection, and language translation.\n", "*   **Unsupervised Learning:** The AI is given unlabeled data and tries to find patterns or structures on its own (e.g., grouping similar customers, anomaly detection).\n", "*   **Reinforcement Learning:** The AI learns by trial and error in an environment. It receives rewards or penalties for its actions and tries to maximize its cumulative reward (e.g., training AI to play games, control robots).\n", "\n", "**A Bit More on Neural Networks (Deep Learning):**\n", "\n", "Many modern AI systems, especially those dealing with complex data like images, sound, and text, use **Neural Networks**, which are inspired by the structure of the human brain.\n", "*   They consist of layers of interconnected \"neurons\" (nodes).\n", "*   Each connection has a \"weight\" that determines the strength of the signal passing through it.\n", "*   During training, these weights are adjusted to improve the network's performance.\n", "*   \"Deep Learning\" refers to neural networks with many layers, allowing them to learn very complex patterns.\n", "\n", "**In a Nutshell:**\n", "\n", "AI (specifically Machine Learning) works by:\n", "1.  **Feeding lots of data** to\n", "2.  **An algorithm**, which\n", "3.  **Learns patterns** by adjusting its internal parameters (training) to create a\n", "4.  **Model**, which can then\n", "5.  **Make predictions or decisions** on new, unseen data.\n", "\n", "It's a continuous cycle of data, learning, and refinement. The more good quality data and the better the algorithms, the more capable the AI becomes.\n"]}], "source": ["from google import genai\n", "from google.genai.types import HttpOptions\n", "import os\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'\n", "\n", "# credentials = {\n", "#     \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n", "#     \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n", "#     \"client_email\": \"<EMAIL>\",\n", "#     \"client_id\": \"107665070555174789662\",\n", "#     \"client_x509_cert_url\": \"https://www.googleapis.com/robot/v1/metadata/x509/vertex%40clacky-456208.iam.gserviceaccount.com\",\n", "#     \"private_key\": \"-----BEGIN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCdd7gvPsFIw3Ps\\n0Il8EQHD/0SEJXsAAcr2ZwHWE6bX0PfZ/DoHkl+RzSY8z4HIxozc0rBFX6Y4o+E6\\nx2Hx2gUx5b+saAPOKbQbJQyvfn03YiStxXsuEtW/w6TIs2gHJTzizVHOMoVmKQ3K\\nH++hIZDuY5IGqEVhOQbEHWyzM9MII2mz/psxwG1bbuwzl7AqqzQez1aueIHRhij2\\nkty5/kkQ/o+tzMPtl/uc6knLmS1+YX9CmzG1v9jnIiv0UYV0LujUMFGQbEU81HdT\\nUiEpFcWGbPAw95NKNRgcT4pxPwEsMHfD4ZUtu9GrbM9uBQu5QdfzR8aji3zi+RIJ\\na3ntF24xAgMBAAECggEADRRKGKaCQGx3nwZtPQBeoKx9wOoqDsSXWqMQrHriY0+o\\nnpMMMN/QD1i2YdusgIQ5ZAgd+w0oTdES3qJ7+K4uu0fFhWj4U/Hz00NfC96aQPTm\\nbgaWpiuLVIvIDqGUNO+7YG1Ea7eXi/ZB/nVrnnsewiZEFUh5xssJTFXTVwkpcKX6\\n2qztN3aTMJ5frRQP9uozemfk3DAwZXOAI0y2oIVdhEVpNq6yWxDSw1mQ1f/AShhE\\nu+y0ELj2cuIGuopxPw2tDQN2zFaCrXGavbt0Bp+AfXuAc35ajJpd1q3zZxBdD7km\\nXjd8gbEcOqY49BSsKg36NiddWQ8l3Eq7g2I2albdYQKBgQDc45DOA0xzS9voUmkk\\nzcv2+7mjiaia6Kc7nwrCm+OFaLB5imwt+P6rqx4hY5pas6OEM0VnHDX0EH/xtW+K\\ns6Kw6hx7iWOiD4zXVosk0be6bDNpVZA4y7yz4UBoGAPR97GlNKYokgNsiFSbPfrO\\nqoJMVFEaYrdakhKFlYEUxRk+SQKBgQC2f2i0NueK1ESs2wKGDLQ8eT9CsdMwpNNI\\niio2gwc43qZiIchUOH/sxtJJTLb5RMFH3zpTwxZKJSwuOgfe+2JVpqlNOw5r0489\\nooU0QTEsPBvy7c0TAzxixUk16WiDl/93AEuvhYRupe4nzgzQRRVEFZbsMTEdAS7L\\nLvYLTkLQqQKBgBKGIwaj0C17Fx4MizTC7W/w/EhyqukSybN+SvC3EgWGGgaXTSst\\nzm2nFAfIypEAr53OjutuujeViqVCifAf0Gz1tR3HD8vvceg9Ib4cuEOx/z2+JzIc\\nI98R7MzN9sQM+aDZIXYViKP5at75+6aJNhQgngyQar/1sAfz2bdcMBnBAoGASsJw\\nwvqG6ZFBPMpPA1jq8Kb+qjSsfg3XW6z10/TjHYHgCO0r4oxuozFjbZGWlxB6WRXy\\nD+QoGmGV8q3lEYxLYSjvy+p4YuV8lVQhWyGloihRjrepvbMippeJASPHo7i9dyNQ\\n3etFMAqbQAUhrKPQLV7mv4T7SJV2dNedBU6fAlkCgYB392f1X7HfCN+dzAxQzcIQ\\nkGpXPwf9QfcYtHtYi0DKzmOgauNoUQ03bap5/Kp/2aAFaXM+nA5u/8Q6iAA2iRRN\\nZEp4WTyYn7rJjkj08avWCSln+Cxirtr98osGa8k1NoRCQRLJ5khMPlUOoTtAKHqs\\nTpHLoVMNkqJBp6PeyIp9Hg==\\n-----END PRIVATE KEY-----\\n\",\n", "#     \"private_key_id\": \"0597b8338ca278a7dd4d506252df2e1741a42337\",\n", "#     \"project_id\": \"clacky-456208\",\n", "#     \"token_uri\": \"https://oauth2.googleapis.com/token\",\n", "#     \"type\": \"service_account\",\n", "#     \"universe_domain\": \"googleapis.com\"\n", "# }\n", "from google.oauth2 import service_account\n", "\n", "# 创建凭证对象\n", "credentials = service_account.Credentials.from_service_account_file(\n", "    'vertex_ai_service_account.json',\n", "    scopes=['https://www.googleapis.com/auth/cloud-platform']\n", ")\n", "\n", "\n", "client = genai.Client(vertexai=True,project=\"clacky-456208\",location=\"us-central1\",credentials=credentials)\n", "response = client.models.generate_content(\n", "    model=\"vertex_ai/gemini-2.5-pro-preview-05-06\",\n", "    contents=\"How does AI work?\",\n", ")\n", "print(response.text)\n", "# Example response:\n", "# Okay, let's break down how AI works. It's a broad field, so I'll focus on the ...\n", "#\n", "# Here's a simplified overview:\n", "# ..."]}, {"cell_type": "code", "execution_count": null, "id": "8da135d4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}