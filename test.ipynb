{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["s1 = ''\n", "with open('chunk1.py') as f:\n", "    s1 = f.read()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["s2 = ''\n", "with open('chunk2.py') as f:\n", "    s2 = f.read()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'```\\nimport logging\\nfrom datetime import datetime\\n\\nfrom django.db.backends.ddl_references import (\\n    Columns, ForeignKeyName, IndexName, Statement, Table,\\n)\\nfrom django.db.backends.utils import names_digest, split_identifier\\nfrom django.db.models import Index\\nfrom django.db.transaction import TransactionManagementError, atomic\\nfrom django.utils import timezone\\n\\nclass BaseDatabaseSchemaEditor:\\n\\n    def _alter_field(self, model, old_field, new_field, old_type, new_type,\\n                     old_db_params, new_db_params, strict=False):\\n        \"\"\"Perform a \"physical\" (non-ManyToMany) field update.\"\"\"\\n        # Change check constraints?\\n        if old_db_params[\\'check\\'] != new_db_params[\\'check\\'] and old_db_params[\\'check\\']:\\n            meta_constraint_names = {constraint.name for constraint in model._meta.constraints}\\n            constraint_names = self._constraint_names(\\n                model, [old_field.column], check=True,\\n                exclude=meta_constraint_names,\\n            )\\n            if strict and len(constraint_names) != 1:\\n                raise ValueError(\"Found wrong number (%s) of check constraints for %s.%s\" % (\\n                    len(constraint_names),\\n                    model._meta.db_table,\\n                    old_field.column,\\n                ))\\n            for constraint_name in constraint_names:\\n                self.execute(self._delete_check_sql(model, constraint_name))\\n        # Have they renamed the column?'\n"]}], "source": ["print(repr(s1))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'```\\n        if old_field.column != new_field.column:\\n            self.execute(self._rename_field_sql(model._meta.db_table, old_field, new_field, new_type))\\n            # Rename all references to the renamed column.\\n            for sql in self.deferred_sql:\\n                if isinstance(sql, Statement):\\n                    sql.rename_column_references(model._meta.db_table, old_field.column, new_field.column)\\n        # Next, start accumulating actions to do\\n        actions = []\\n        null_actions = []\\n        post_actions = []\\n        # Type change?\\n        # Reset connection if required\\n        if self.connection.features.connection_persists_old_columns:\\n            self.connection.close()\\n\\n\\n    def _post_migrate_check(self, model):\\n        # Verify that all unique_together constraints still exist\\n        unique_constraints = model._meta.unique_together\\n        for fields in unique_constraints:\\n            constraint_names = self._constraint_names(\\n                model, fields, unique=True, index=False\\n            )\\n            if not constraint_names:\\n                raise MigrationError(f\"Missing unique constraint for fields {fields}\")\\n```'\n"]}], "source": ["print(repr(s2))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```\n", "import logging\n", "from datetime import datetime\n", "\n", "from django.db.backends.ddl_references import (\n", "    Columns, ForeignKeyName, IndexName, Statement, Table,\n", ")\n", "from django.db.backends.utils import names_digest, split_identifier\n", "from django.db.models import Index\n", "from django.db.transaction import TransactionManagementError, atomic\n", "from django.utils import timezone\n", "\n", "class BaseDatabaseSchemaEditor:\n", "\n", "    def _alter_field(self, model, old_field, new_field, old_type, new_type,\n", "                     old_db_params, new_db_params, strict=False):\n", "        \"\"\"Perform a \"physical\" (non-ManyToMany) field update.\"\"\"\n", "        # Change check constraints?\n", "        if old_db_params['check'] != new_db_params['check'] and old_db_params['check']:\n", "            meta_constraint_names = {constraint.name for constraint in model._meta.constraints}\n", "            constraint_names = self._constraint_names(\n", "                model, [old_field.column], check=True,\n", "                exclude=meta_constraint_names,\n", "            )\n", "            if strict and len(constraint_names) != 1:\n", "                raise ValueError(\"Found wrong number (%s) of check constraints for %s.%s\" % (\n", "                    len(constraint_names),\n", "                    model._meta.db_table,\n", "                    old_field.column,\n", "                ))\n", "            for constraint_name in constraint_names:\n", "                self.execute(self._delete_check_sql(model, constraint_name))\n", "        # Have they renamed the column?```\n", "        if old_field.column != new_field.column:\n", "            self.execute(self._rename_field_sql(model._meta.db_table, old_field, new_field, new_type))\n", "            # Rename all references to the renamed column.\n", "            for sql in self.deferred_sql:\n", "                if isinstance(sql, Statement):\n", "                    sql.rename_column_references(model._meta.db_table, old_field.column, new_field.column)\n", "        # Next, start accumulating actions to do\n", "        actions = []\n", "        null_actions = []\n", "        post_actions = []\n", "        # Type change?\n", "        # Reset connection if required\n", "        if self.connection.features.connection_persists_old_columns:\n", "            self.connection.close()\n", "\n", "\n", "    def _post_migrate_check(self, model):\n", "        # Verify that all unique_together constraints still exist\n", "        unique_constraints = model._meta.unique_together\n", "        for fields in unique_constraints:\n", "            constraint_names = self._constraint_names(\n", "                model, fields, unique=True, index=False\n", "            )\n", "            if not constraint_names:\n", "                raise MigrationError(f\"Missing unique constraint for fields {fields}\")\n", "```\n"]}], "source": ["print(s1+s2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}