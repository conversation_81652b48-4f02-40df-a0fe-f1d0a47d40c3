{"cells": [{"cell_type": "code", "execution_count": 39, "id": "bf200199", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"AWS_ACCESS_KEY_ID\"] = \"********************\"\n", "os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8\"\n", "os.environ[\"AWS_REGION_NAME\"] = \"us-west-2\"\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['no_proxy'] = '127.0.0.1,localhost,proxy.clackyai.com'\n", "\n", "import boto3\n", "os.environ[\"AWS_ACCESS_KEY_ID\"] = \"********************\"\n", "os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8\"\n", "os.environ[\"AWS_REGION_NAME\"] = \"us-west-2\"\n", "import requests\n"]}, {"cell_type": "code", "execution_count": 40, "id": "b3c0790f", "metadata": {}, "outputs": [], "source": ["client = boto3.client(\"bedrock-runtime\", region_name=\"us-west-2\")\n", "model_id = \"us.anthropic.claude-3-7-sonnet-20250219-v1:0\""]}, {"cell_type": "code", "execution_count": 41, "id": "112b83e1", "metadata": {}, "outputs": [], "source": ["user_message = \"Describe the purpose of a 'hello world' program in one line.\" * 1000\n", "conversation = [\n", "    {'role': 'user', 'content': [{'text': 'What is the most popular song on WZPZ?'}]},\n", "]\n", "toolConfig = {\n", "    'tools': [\n", "        {\n", "            'toolSpec': {\n", "                'name': 'top_song',\n", "                'description': 'Get the most popular song played on a radio station.',\n", "                'inputSchema': {\n", "                    'json': {\n", "                        'type': 'object',\n", "                        'properties': {\n", "                            'sign': {\n", "                                'type': 'string',\n", "                                'description': 'The call sign for the radio station for which you want the most popular song. Example calls signs are WZPZ, and WKRP.',\n", "                            }\n", "                        },\n", "                        'required': ['sign'],\n", "                    }\n", "                },\n", "            }\n", "        }\n", "    ],\n", "}\n"]}, {"cell_type": "code", "execution_count": 42, "id": "c09527b5", "metadata": {}, "outputs": [], "source": ["response = client.converse(\n", "    modelId=model_id,\n", "    messages=conversation,\n", "    toolConfig=toolConfig\n", ")\n"]}, {"cell_type": "code", "execution_count": 43, "id": "7c8aa6e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ResponseMetadata': {'RequestId': 'f12bc7eb-3d6e-41f8-bf52-0e2d9303afc7',\n", "  'HTTPStatusCode': 200,\n", "  'HTTPHeaders': {'date': '<PERSON><PERSON>, 13 May 2025 12:39:28 GMT',\n", "   'content-type': 'application/json',\n", "   'content-length': '487',\n", "   'connection': 'keep-alive',\n", "   'x-amzn-requestid': 'f12bc7eb-3d6e-41f8-bf52-0e2d9303afc7'},\n", "  'RetryAttempts': 0},\n", " 'output': {'message': {'role': 'assistant',\n", "   'content': [{'text': \"I'll help you find the most popular song on radio station WZPZ. Let me look that up for you.\"},\n", "    {'toolUse': {'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA',\n", "      'name': 'top_song',\n", "      'input': {'sign': 'WZPZ'}}}]}},\n", " 'stopReason': 'tool_use',\n", " 'usage': {'inputTokens': 426,\n", "  'outputTokens': 82,\n", "  'totalTokens': 508,\n", "  'cacheReadInputTokens': 0,\n", "  'cacheWriteInputTokens': 0},\n", " 'metrics': {'latencyMs': 4843}}"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": 45, "id": "f57e07a3", "metadata": {}, "outputs": [], "source": ["conversation.append(response['output']['message'])"]}, {"cell_type": "code", "execution_count": 46, "id": "3d355178", "metadata": {}, "outputs": [], "source": ["tool_requests = response['output']['message']['content']"]}, {"cell_type": "code", "execution_count": 49, "id": "a51668b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA',\n", " 'name': 'top_song',\n", " 'input': {'sign': 'WZPZ'}}"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_requests[1]['toolUse']"]}, {"cell_type": "code", "execution_count": 50, "id": "abd910bd", "metadata": {}, "outputs": [], "source": ["tool = {'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA',\n", " 'name': 'top_song',\n", " 'input': {'sign': 'WZPZ'}}\n", "tool_result = {'toolUseId': tool['toolUseId'], 'content': [{'json': {'song': 'Elemental Hotel', 'artist': '8 Storey Hike'}}]}\n", "tool_result_message = {'role': 'user', 'content': [{'toolResult': tool_result}]}\n"]}, {"cell_type": "code", "execution_count": 51, "id": "c696cbaa", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'role': 'user',\n", " 'content': [{'toolResult': {'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA',\n", "    'content': [{'json': {'song': 'Elemental Hotel',\n", "       'artist': '8 <PERSON>y Hike'}}]}}]}"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_result_message"]}, {"cell_type": "code", "execution_count": 52, "id": "19f3bf68", "metadata": {}, "outputs": [], "source": ["conversation.append(tool_result_message)"]}, {"cell_type": "code", "execution_count": 66, "id": "663880da", "metadata": {}, "outputs": [], "source": ["conversation = [\n", "    {'role': 'user', 'content': [{'text': 'What is the most popular song on WZPZ?'}]},\n", "    {\n", "        'role': 'assistant',\n", "        'content': [\n", "            {'text': \"I'll help you find the most popular song on radio station WZPZ. Let me look that up for you.\"*100},\n", "            {'toolUse': {'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA', 'name': 'top_song', 'input': {'sign': 'WZPZ'}}},\n", "        ],\n", "    },\n", "    {\n", "        'role': 'user',\n", "        'content': [\n", "            {\n", "                'toolResult': {\n", "                    'toolUseId': 'tooluse_5YqAWupVQBq09NzujrOWtA',\n", "                    'content': [{'json': {'song': 'Elemental Hotel', 'artist': '8 Storey Hike'}}],\n", "                }\n", "            },\n", "            {'cachePoint': {'type': 'default'}},\n", "        ],\n", "    },\n", "]\n"]}, {"cell_type": "code", "execution_count": 67, "id": "c3e28ba1", "metadata": {}, "outputs": [], "source": ["response = client.converse(\n", "    modelId=model_id,\n", "    messages=conversation,\n", "    toolConfig=toolConfig\n", ")\n"]}, {"cell_type": "code", "execution_count": 68, "id": "4ded98e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ResponseMetadata': {'RequestId': '369d966e-25a8-4c2b-bccc-cd2cc4026ac0',\n", "  'HTTPStatusCode': 200,\n", "  'HTTPHeaders': {'date': '<PERSON><PERSON>, 13 May 2025 12:44:08 GMT',\n", "   'content-type': 'application/json',\n", "   'content-length': '401',\n", "   'connection': 'keep-alive',\n", "   'x-amzn-requestid': '369d966e-25a8-4c2b-bccc-cd2cc4026ac0'},\n", "  'RetryAttempts': 0},\n", " 'output': {'message': {'role': 'assistant',\n", "   'content': [{'text': 'The most popular song currently playing on radio station WZPZ is \"Elemental Hotel\" by 8 Storey Hike.'}]}},\n", " 'stopReason': 'end_turn',\n", " 'usage': {'inputTokens': 6,\n", "  'outputTokens': 35,\n", "  'totalTokens': 3048,\n", "  'cacheReadInputTokens': 3007,\n", "  'cacheWriteInputTokens': 0},\n", " 'metrics': {'latencyMs': 2301}}"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "id": "81f9e146", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}