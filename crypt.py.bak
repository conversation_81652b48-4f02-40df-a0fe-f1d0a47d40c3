# 定义泛型类型变量，用于保持装饰器的类型提示
T = TypeVar('T', bound=Callable[..., Dict[str, Any]])


def encrypt_response(encrypted_key: str = 'encrypted_data'):
    """
    装饰器：将被装饰函数的返回值进行加密，并将加密结果添加到返回字典中

    该装饰器会将原始函数的返回值(必须是字典)通过encrypt_payload加密,
    然后将加密后的数据以指定的键名添加到原始返回字典中。

    Args:
        encrypted_key (str): 存储加密数据的键名，默认为'encrypted_data'

    Returns:
        Callable: 装饰后的函数

    Example:
        @encrypt_response(encrypted_key='secure_payload')
        def get_payment_info() -> dict[str, Any]:
            return {'amount': 100, 'currency': 'USD'}

        # 调用结果将是:
        # {
        #    'amount': 100,
        #    'currency': 'USD',
        #    'secure_payload': '<加密的JSON字符串>'
        # }
    """

    def decorator(func: T) -> T:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Dict[str, Any]:
            result = func(*args, **kwargs)

            if not isinstance(result, dict):
                raise TypeError(f'被装饰函数的返回值必须是字典类型，而不是{type(result)}')

            encrypted_result = encrypt_payload(result)
            enhanced_result = result.copy()
            if encrypted_result:
                enhanced_result[encrypted_key] = encrypted_result
            return enhanced_result

        return cast(T, wrapper)

    return decorator
