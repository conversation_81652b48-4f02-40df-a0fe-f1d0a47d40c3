- name: "RAGBot Starter"
  description: "An Astra DB and OpenAI chatbot"
  github_repo: "clackyai-examples/ragbot-starter"
  env_code: "code_python_3_9_21"
  states:
    - id: "ragbot-starter-main"
      branch_name: "main"
      # run_steps:
      #   - echo "hello"

- name: "fastapi-admin"
  description: "fastapi-admin is a fast admin dashboard based on FastAPI and TortoiseORM with tabler ui, inspired by Django admin."
  github_repo: "clackyai-examples/fastapi-admin"
  env_code: "code_python_3_9_21"
  states:
    - id: "fastapi-admin-main"
      branch_name: "dev"

- name: "fadeOut-game"
  description: "A simple game using html and javascript"
  github_repo: "clackyai-examples/fadeOut-game"
  env_code: "code_html_css_js"
  states:
    - id: "fadeOut-game-main"
      branch_name: "main"

- name: "simple_music_player"
  description: "A simple music player using html and javascript"
  github_repo: "clackyai-examples/simple_music_player"
  env_code: "code_html_css_js"
  states:
    - id: "simple_music_player-main"
      branch_name: "main"

- name: "ai-chatbot"
  description: "A simple chatbot using next.js"
  github_repo: "clackyai-examples/ai-chatbot"
  env_code: "code_node_22_1_3"
  states:
    - id: "ai-chatbot-main"
      branch_name: "main"

- name: "tldraw-example"
  description: "A simple tldraw using next.js"
  github_repo: "clackyai-examples/tldraw"
  env_code: "code_node_20_1_0"
  states:
    - id: "tldraw-example-main"
      branch_name: "main"
      path: "templates/nextjs"

- name: "nextjs-fastapi"
  description: "Next.js FastAPI Starter"
  github_repo: "clackyai-examples/nextjs-fastapi"
  env_code: "code_node_22_1_3"
  states:
    - id: "nextjs-fastapi-main"
      branch_name: "main"

- name: "nextjs-dashboard"
  description: "A simple dashboard using next.js"
  github_repo: "clackyai-examples/nextjs-dashboard"
  env_code: "code_node_20_1_0"
  states:
    - id: "nextjs-dashboard-main"
      branch_name: "main"

- name: "express-blog"
  description: "基于Express实现的博客站点"
  github_repo: "clackyai-examples/express-blog"
  env_code: "code_node_18_14_1"
  states:
    - id: "express-blog-main"
      branch_name: "main"

- name: "nextra-docs-template"
  description: "A simple docs template using next.js"
  github_repo: "clackyai-examples/nextra-docs-template"
  env_code: "code_node_20_1_0"
  states:
    - id: "nextra-docs-template-main"
      branch_name: "main"

- name: "internal-chat"
  description: "A simple chat app using gin and websocket"
  github_repo: "clackyai-examples/internal-chat"
  env_code: "code_node_16_19_1"
  states:
    - id: "internal-chat-main"
      branch_name: "master"

- name: "chat-websocket-gin"
  description: "A simple chat app using gin and websocket"
  github_repo: "clackyai-examples/chat-websocket-gin"
  env_code: "code_go_1_22"
  states:
    - id: "chat-websocket-gin-main"
      branch_name: "main"

- name: "gin-gorm-todo-app"
  description: "A simple todo app using gin and gorm"
  github_repo: "clackyai-examples/gin-gorm-todo-app"
  env_code: "code_go_1_22"
  states:
    - id: "gin-gorm-todo-app-main"
      branch_name: "master"

- name: "go-blog"
  description: "基于Go实现的博客站点"
  github_repo: "clackyai-examples/go-blog"
  env_code: "code_go_1_22"
  states:
    - id: "go-blog-main"
      branch_name: "master"

- name: "wblog"
  description: "Rails 实现的博客系统"
  github_repo: "clackyai-examples/wblog"
  env_code: "code_ruby_3_1_2"
  states:
    - id: "wblog-main"
      branch_name: "master"
    - id: "wblog-initialized-main"
      branch_name: "master"

- name: "java-master"
  description: "java-master"
  github_repo: "clackyai-examples/java-master"
  env_code: "code_blank"
  states:
    - id: "java-master-main"
      branch_name: "master"

- name: "exercism/python"
  description: "exercism/python"
  github_repo: "exercism/python"
  env_code: "code_python_pyenv_2_5"
  states:
    - id: "exercism-python-main-accumulate"
      branch_name: "main"
      path: "exercises/practice/accumulate"
    - id: "exercism-python-main-acronym"
      branch_name: "main"
      path: "exercises/practice/acronym"

- name: "refactor-benchmark"
  description: "refactor-benchmark"
  github_repo: "Aider-AI/refactor-benchmark"
  env_code: "code_python_pyenv_2_5"
  states:
    - id: "refactor-benchmark-config_AppConfig__path_from_module"
      branch_name: "main"
      path: "refactor-benchmark/config_AppConfig__path_from_module"
    - id: "refactor-benchmark-backends_ModelBackend_with_perm"
      branch_name: "main"
      path: "refactor-benchmark/backends_ModelBackend_with_perm"
    - id: "refactor-benchmark-base_BaseHandler_check_response"
      branch_name: "main"
      path: "refactor-benchmark/base_BaseHandler_check_response"
    - id: "refactor-benchmark-base_BaseHandler_adapt_method_mode"
      branch_name: "main"
      path: "refactor-benchmark/base_BaseHandler_adapt_method_mode"
    - id: "refactor-benchmark-cuda_cpp_scheduling_CUDACPPScheduling__can_fuse_epilogue_impl"
      branch_name: "main"
      path: "refactor-benchmark/cuda_cpp_scheduling_CUDACPPScheduling__can_fuse_epilogue_impl"
    - id: "refactor-benchmark-autosave_AutosaveForPlugin_get_files_to_recover"
      branch_name: "main"
      path: "refactor-benchmark/autosave_AutosaveForPlugin_get_files_to_recover"
    - id: "refactor-benchmark-checks_BaseModelAdminChecks__check_autocomplete_fields_item"
      branch_name: "main"
      path: "refactor-benchmark/checks_BaseModelAdminChecks__check_autocomplete_fields_item"
    - id: "refactor-benchmark-clustering_ops_KMeans__mini_batch_training_op"
      branch_name: "main"
      path: "refactor-benchmark/clustering_ops_KMeans__mini_batch_training_op"

- name: "pydicom"
  description: "pydicom"
  github_repo: "pydicom/pydicom"
  env_code: "code_python_pyenv_2_5"
  states:
    - id: "pydicom__pydicom-995"
      commit_id: "29be72498a4f4131808a45843b15692234ae7652"
    - id: "pydicom__pydicom-958"
      commit_id: "40652fc0a18fd9f1204cb3d4b9829e3a8be5cbe0"

- name: "matplotlib"
  description: "matplotlib"
  github_repo: "matplotlib/matplotlib"
  env_code: "swe"
  states:
    - id: "matplotlib__matplotlib-22711"
      commit_id: "22711"

- name: "blank-project"
  description: "blank-project"
  github_repo: "clackyai-examples/blank-project"
  env_code: "code_html_css_js"
  states:
    - id: "blank-project-main"
      branch_name: "main"

- name: "blank-project-python"
  description: "blank-project with python 3.12.8"
  github_repo: "clackyai-examples/blank-project"
  env_code: "code_python_3_12_8"
  states:
    - id: "blank-project-python-main"
      branch_name: "main"

- name: "blank-project-ruby"
  description: "blank-project with ruby 3.2.6"
  github_repo: "clackyai-examples/blank-project"
  env_code: "code_ruby_3_2_6"
  states:
    - id: "blank-project-ruby-main"
      branch_name: "main"

- name: "hackernew"
  description: "0-1 hackernews"
  github_repo: "clackyai-examples/hackernews"
  env_code: "code_node_22_1_0"
  states:
    - id: "hackernews-error-case-1"
      branch_name: "main"
    - id: "hackernews-error-case-2"
      branch_name: "main"

- name: "snake"
  description: "0-1 snake"
  github_repo: "clackyai-examples/snake"
  env_code: "code_html_css_js"
  states:
    - id: "snake-error-case-1"
      branch_name: "main"

- name: "blog"
  description: "0-1 blog"
  github_repo: "clackyai-examples/blog"
  env_code: "code_python_3_12_8"
  states:
    - id: "blog-error-case-1"
      branch_name: "main"
    - id: "blog-error-case-2"
      branch_name: "main"
    - id: "blog-error-case-3"
      branch_name: "main"

- name: "pomodoro"
  description: "0-1 pomodoro"
  github_repo: "clackyai-examples/pomodoro"
  env_code: "code_node_22_1_0"
  states:
    - id: "pomodoro-error-case-1"
      branch_name: "main"

- name: "fastapi-todolist"
  description: "0-1 fastapi-todolist"
  github_repo: "clackyai-examples/fastapi-todolist"
  env_code: "code_python_3_12_8"
  states:
    - id: "fastapi-todolist-error-case-1"
      branch_name: "main"
    - id: "fastapi-todolist-error-case-2"
      branch_name: "main"
    - id: "fastapi-todolist-error-case-3"
      branch_name: "main"

- name: "rails-twitter-like"
  description: "0-1 rails-twitter-like"
  github_repo: "clackyai-examples/rails-twitter-like"
  env_code: "code_ruby_3_3_5"
  states:
    - id: "rails-twitter-like-error-case-1"
      branch_name: "main"
    - id: "rails-twitter-like-error-case-2"
      branch_name: "main"
    - id: "rails-twitter-like-error-case-3"
      branch_name: "main"

- name: "simple-todolist"
  description: "0-1 simple-todolist"
  github_repo: "clackyai-examples/simple-todolist"
  env_code: "code_node_22_1_0"
  states:
    - id: "simple-todolist-error-case-1"
      branch_name: "main"
    - id: "simple-todolist-error-case-2"
      branch_name: "main"