[{"env_code": "code_go_1_17", "name": "Go 1.17", "runtime": "go1.17 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_18", "name": "Go 1.18", "runtime": "go1.18 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_19", "name": "Go 1.19", "runtime": "go1.19 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_20_gin", "name": "Gin / Go 1.20", "runtime": "go1.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_gvm_1_0_22", "name": "Go Basic Environment ( gvm 1.0.22 )", "runtime": "Managed by  gvm 1.0.22 with go1.22 activated, and go1.17, go1.18, go1.19, go1.20, go1.21, go1.23, node v20.1.0  already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_20", "name": "Go 1.20", "runtime": "go1.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_21", "name": "Go 1.21", "runtime": "go1.21 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_22", "name": "Go 1.22", "runtime": "go1.22 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_go_1_23", "name": "Go 1.23", "runtime": "go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_3_0_5", "name": "Ruby 3.0.5", "runtime": "ruby 3.0.5 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_3_0_1", "name": "Ruby 3.0.1", "runtime": "ruby 3.0.1 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_3_1_2", "name": "Ruby 3.1.2", "runtime": "ruby 3.1.2 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_2_7_7", "name": "Ruby 2.7.7", "runtime": "ruby 2.7.7 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_rbenv_1_3_0", "name": "Ruby Basic Environment ( rbenv 1.3.0 )", "runtime": "Managed by  rbenv 1.3.0 with  ruby 2.7.8 activated, and ruby 2.7.7, ruby 3.0.1, ruby 3.0.5, ruby 3.1.2, ruby 3.2.6, ruby 3.3.5,node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_3_2_6", "name": "Ruby 3.2.6", "runtime": "ruby 3.2.6 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_ruby_3_3_5", "name": "Ruby 3.3.5", "runtime": "ruby 3.3.5 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_2_7_18", "name": "Python 2.7.18", "runtime": "python 2.7.18 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_6_15", "name": "Python 3.6.15", "runtime": "python 3.6.15 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_7_17", "name": "Python 3.7.17", "runtime": "python 3.7.17 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_8_20", "name": "Python 3.8.20", "runtime": "python 3.8.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_9_21", "name": "Python 3.9.21", "runtime": "python 3.9.21 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_10_16", "name": "Python 3.10.16", "runtime": "python 3.10.16 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_11_11", "name": "Python 3.11.11", "runtime": "python 3.11.11 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_3_12_8", "name": "Python 3.12.8", "runtime": "python 3.12.8 and node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_python_pyenv_2_5", "name": "Python Basic Environment ( pyenv 2.5 )", "runtime": "Managed by pyenv 2.5 with python 2.7.18 activated, and python 3.6.15, python 3.7.17, python 3.8.20, python 3.9.21, python 3.10.16, python 3.11.11, python 3.12.8, node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_blank", "name": "blank", "runtime": "node v20.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_node_18_20_4", "name": "Node.js 18", "runtime": null}, {"env_code": "code_node_nvm_0_41_1", "name": "Node.js Basic Environment ( nvm 0.41.1 )", "runtime": "Managed by nvm 0.41.1 with node v20.1.0 activated, and node v14.21.3, node v16.19.1, node v18.14.1, node v20.1.0, node v22.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_html_css_js", "name": "HTML/CSS/JS", "runtime": "browser-sync 2.27.10 and node v20.1.0 already installed,  based on Ubuntu 22.04.4"}, {"env_code": "code_node_18_14_1", "name": "Node.js 18.14.1", "runtime": "node v18.14.1 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_node_16_19_1", "name": "Node.js 16.19.1", "runtime": "node v16.19.1 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_node_14_21_3", "name": "Node.js 14.21.3", "runtime": "node v14.21.3 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_node_22_1_3", "name": "Node.js 22.1.3", "runtime": "node v22.1.0 already installed, based on Ubuntu 22.04.4"}, {"env_code": "code_node_20_1_0", "name": "Node.js 20.1.0", "runtime": "node v20.1.0 already installed, based on Ubuntu 22.04.4"}]