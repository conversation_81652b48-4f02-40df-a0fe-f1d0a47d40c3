import os
import asyncio
import pytest
import pytest_html  # type: ignore

from llm_tests.llm_speed_collector import llm_speed_collector
from heracles.core.schema.test import Mock<PERSON>ocketIOServer, Context
from heracles.core.config import get_env_var
from heracles.core.logger import heracles_logger as logger
from heracles.server.clacky.playground import Playground
from heracles.server.clacky.playground_channel import Playground<PERSON>hannel
from heracles.server.clacky.playground_manager import PlaygroundManager
from llm_tests.utils import update_trace_tag, create_trace_score
from llm_tests.utils.schema import ScoreModel, TestDataBase, BasicTestData
from heracles.agent_workspace.paas_sdk.utils import (
    bind_middleware_to_codezone,
    fork_new_codezone_get_id,
    get_playground_id_from_codezone_id,
    delete_codezone,
)  # noqa: E501
from llm_tests.datasets import LLMTestDataset, LLMTestSWEDataset
from llm_tests.basic.score_rule.smoke_test_score_rule import SmokeTestScoreRule
from heracles.core.exceptions import AgentRunException


def pytest_configure(config):
    os.environ['FORCE_COLOR'] = '1'


def pytest_sessionstart(session):
    """Called before the test session starts"""
    llm_speed_collector._register_litellm_callbacks()


def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Called when terminal report is about to be generated"""
    llm_speed_collector.generate_report()

def run_clean():
    try:
        os.remove('llm_tests/llm_metrics.db')
    except Exception as e:
        logger.error(f'Error cleaning metrics db: {e}')


# run_clean()


socketio_server = MockSocketIOServer()
playground_manager = PlaygroundManager()

paas_manager_url = get_env_var('PAAS_DOMAIN_URL', must=True)
paas_tenant_code = get_env_var('PAAS_TENANT_CODE', must=True)
dataset = LLMTestDataset()


@pytest.fixture
def create_test_basic_data():
    def _create_test_basic_data(**kwargs):
        return TestDataBase(dataset=dataset, **kwargs)

    return _create_test_basic_data


@pytest.fixture
def create_test_data():
    def _create_test_data(**kwargs):
        return BasicTestData(dataset=dataset, **kwargs)

    return _create_test_data


@pytest.fixture
def create_swe_test_data():
    def _create_swe_test_data(**kwargs):
        dataset = LLMTestSWEDataset()
        return BasicTestData(dataset=dataset, **kwargs)

    return _create_swe_test_data


@pytest.fixture
def create_playground_channel(mocker):
    context = Context()

    async def _create_playground_channel(codezone_id):
        new_codezone_id = None
        playground_id = None
        try:
            new_codezone_id = await fork_new_codezone_get_id(codezone_id)
            playground_id = await get_playground_id_from_codezone_id(new_codezone_id)
            logger.info(f'[setup]origin_codezone={codezone_id}, new_codezone={new_codezone_id}, playground_id={playground_id}')
            playground = Playground(playground_id, socketio_server)
            playground_manager.add_playground(playground)
            playground_channel = PlaygroundChannel(f'sid-{playground_id}', socketio_server, playground_manager, playground_id)
            await playground_channel.start()
            return playground_channel, new_codezone_id
        finally:
            context.new_codezone_id = new_codezone_id
            context.playground_id = playground_id

    try:
        yield _create_playground_channel
    finally:
        # logger.info(f'keep codezone: {context.new_codezone_id}')
        if context.playground_id:
            # asyncio.run(stop_playground(context.playground_id))
            logger.warning(f'Open playground in PaaS Demo: {paas_manager_url}/ide/multiterminal/{context.playground_id}')
            langfuse_host = get_env_var('LANGFUSE_HOST_OPTIONAL')
            langfuse_project_id = get_env_var('LANGFUSE_PROJECT_ID_OPTIONAL')
            if langfuse_host and langfuse_project_id:
                logger.warning(f'Open Langfuse trace: {langfuse_host}/project/{langfuse_project_id}/traces/{context.playground_id}')  # noqa
        # asyncio.run(delete_codezone(context.new_codezone_id))


@pytest.fixture
def create_workspace(mocker, create_playground_channel):
    async def _create_workspace(codezone_id):
        with_rag = get_env_var('WITH_RAG', default='True').lower() in ('true', '1')

        playground_channel, new_codezone_id = await create_playground_channel(codezone_id)
        playground = playground_channel.current_playground
        workspace = playground.agent_controller.workspace
        workspace.new_codezone_id = new_codezone_id
        if not with_rag:
            mocker.patch.object(workspace.rag_searcher, 'search', return_value=[])
        return workspace

    return _create_workspace


@pytest.fixture
def run_whole_process(mocker, extras, create_workspace):
    """完整跑全部项目, 用于 basic/smoke 测试
    TODO: 评分逻辑完善
    """
    context = Context()

    async def _run_whole_process(test_data, before_function=None, proposed_list=None):
        codezone_id = test_data.codezone_id
        logger.warning(f'-> step: create_workspace: {test_data.title}')
        workspace = await create_workspace(codezone_id)

        # 绑定中间件
        middlewares = test_data.middlewares
        for middleware_name in middlewares:
            # 原始 codezone 如果已经绑定过中间件 fork 后会继承，可以先解绑
            # logger.warning(f'-> step: unbind_middleware_from_codezone: origin codezone {codezone_id}, {middleware_name}')
            # await unbind_middleware_from_codezone(codezone_id, middleware_name)
            logger.warning(f'-> step: bind_middleware_to_codezone: {middleware_name}')
            result = await bind_middleware_to_codezone(workspace.new_codezone_id, middleware_name)
            if result['success']:
                logger.warning(f'-> bind_middleware_to_codezone success: {middleware_name}')
            else:
                raise AgentRunException(f'bind_middleware_to_codezone failed: {result}')

        logger.warning(f'-> step: run before_function: {test_data.title}')
        # Execute the before_function if provided, passing the playground
        if before_function:
            await before_function(workspace)

        context.workspace = workspace
        controller = workspace.playground.agent_controller
        extras.append(pytest_html.extras.json(test_data.dict(), 'TestData'))
        context.title = test_data.title

        spec_role = controller.spec_role
        plan_role = controller.plan_role
        task_role = controller.task_role
        code_role = controller.code_role

        goal = test_data.goal
        goal_detail = test_data.goal_detail

        logger.warning(f'-> step: run spec_role & plan_role: {test_data.title}')
        spec = await spec_role.run(goal, goal_detail)
        task = await plan_role.run(spec.goal, spec.goal_detail, spec.proposed_list)

        logger.warning(f'-> step: run task_role: {test_data.title}')
        workspace.set_task(task)
        task_role.task_state.plan_task()
        task_role.task_state.start_task()
        await task_role.run(code_role.run)

        logger.info('AUTO_SCORE: ' + get_env_var('AUTO_SCORE', default='False'))
        if get_env_var('AUTO_SCORE', default='False').lower() in ('true', '1'):
            logger.warning(f'-> step: run run_project: {test_data.title}')
            check_role = controller.check_role
            errors = workspace.smart_detect.errors

            try:
                smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)

                await check_role.run_and_check_project()
                # 收集并分析错误信息
                await asyncio.sleep(10)
                if len(errors) > 0:
                    for error in errors:
                        logger.error(f'-> step: RUN failed with error found: \nref_id: {error.ref_id}\n\n{error.content}')
                    error_titles = ', '.join([error.title for error in errors])
                    smoke_test_score_rule.errors.append(f"errors detected: {error_titles}")
                    smoke_test_score_rule.scores.append(ScoreModel(value=0.0, comment='smoke 用例无法通过, 原因: run 日志中包含错误信息'))
                else:
                    smoke_test_score_rule.scores.append(ScoreModel(value=1.0, comment='规则: 运行项目有日志输出，且没有错误信息'))
            except AgentRunException as e:
                logger.error(f'-> wait for clacky terminal log error: {e}')
                smoke_test_score_rule.errors.append(f'运行项目 10 秒超时仍没有日志输出: {e}')
                smoke_test_score_rule.scores.append(ScoreModel(value=0.0, comment='规则: 运行项目有日志输出，且没有错误信息'))

            logger.warning(f'-> step: run smoke_test_score_rule: {test_data.title}')
            context.score_model = await smoke_test_score_rule.execute_rules_and_score('all')

        logger.warning(f'-> step: end run: {test_data.title}')
        return context

    try:
        yield _run_whole_process
    finally:
        update_trace_tag(context.workspace, 'smoke-test', context.title)

        if score_model := context.score_model:
            if not isinstance(score_model, ScoreModel):
                raise ValueError('score_model must be an instance of ScoreModel')
            create_trace_score(context.workspace, score_model.value, score_model.comment)
            extras.append(pytest_html.extras.json(score_model.dict(), 'Score'))
        else:
            create_trace_score(context.workspace, 0.0, 'Initial test score set to 0')


@pytest.fixture
def run_swe_whole_process(mocker, run_whole_process):
    async def _run_swe_whole_process(test_data):
        try:
            context = await run_whole_process(test_data)
            playground = context.workspace.playground

            await playground.func_call('agent_terminal_with_result', 'export QT_XCB_GL_INTEGRATION=none', soft_timeout=120)
            await playground.func_call('agent_terminal_with_result', 'git config --global core.pager cat', soft_timeout=120)
            res = await playground.func_call('agent_terminal_with_result', '/bin/bash /home/<USER>/eval.sh', soft_timeout=120)

            res_lines = res.splitlines()
            passed = True
            for line in res_lines:
                line = line.strip()
                if (
                    line.startswith('FAILED')
                    or line.startswith('ERROR')
                    or line.endswith(' ... FAIL')
                    or line.startswith('FAIL:')
                    or line.endswith(' ... ERROR')
                    or line.startswith('ERROR:')
                    or line.endswith('FAILED')
                    or line.endswith('ERROR')
                ):
                    passed = False
                    break
                if line.startswith('test_') and (line.endswith(' E') or line.endswith(' F')):
                    passed = False
                    break
            logger.info(f'{test_data.title} test passed: {passed}')
            if passed:
                context.score_model = ScoreModel(value=1.0, comment=f'swe test {res}')
            else:
                context.score_model = ScoreModel(value=0.0, comment=f'swe test {res}')
                pytest.fail(f'swe 测试未通过: {test_data.title}')
            return context

        except Exception as e:
            logger.info(f'{test_data.title} test passed: False')
            logger.error(f'run_whole_process error: {e}')
            raise Exception(f'run_whole_process error: {e}') from e

    yield _run_swe_whole_process
