import json
import os
import litellm
import numpy as np
import sqlite3
import hashlib
from collections import defaultdict
from pathlib import Path
from tabulate import tabulate  # type: ignore
from typing import Any
from litellm.integrations.custom_logger import CustomLogger


class MetricsStorage:
    def __init__(self, db_path: str | None = None):
        self._init(db_path)
        self.err_count = 0

    def _init(self, db_path: str | None = None):
        try:
            """Initialize SQLite database for storing metrics"""
            self.db_path = db_path or Path(__file__).parent / 'llm_metrics.db'
            self.conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
            self.cursor = self.conn.cursor()

            # Create metrics table if it doesn't exist
            self.cursor.execute("""
                    CREATE TABLE IF NOT EXISTS metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        generation_name TEXT,
                        trace_id TEXT,
                        trace_name TEXT,
                        model_name TEXT,
                        ttft_cost REAL,
                        total_cost REAL,
                        total_tokens INTEGER,
                        token_throughput REAL,
                        first_two_messages TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        start_time TIMESTAMP
                    )
                """)

            # Create trace_mappings table if it doesn't exist
            self.cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trace_mappings (
                        trace_id TEXT PRIMARY KEY,
                        test_title TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
            self.conn.commit()
        except Exception:
            self.err_count += 1

    def add_metric(
        self,
        generation_name: str,
        trace_id: str,
        trace_name: str,
        model_name: str,
        ttft_cost: float,
        total_cost: float,
        total_tokens: int,
        token_throughput: float,
        first_two_messages: str,
        start_time: float,
    ) -> None:
        try:
            """Add a new metric to the storage"""
            self.cursor.execute(
                """
                INSERT INTO metrics (
                    generation_name, trace_id, trace_name, model_name,
                    ttft_cost, total_cost, total_tokens, token_throughput,
                    first_two_messages, start_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    generation_name,
                    trace_id,
                    trace_name,
                    model_name,
                    ttft_cost,
                    total_cost,
                    total_tokens,
                    token_throughput,
                    first_two_messages,
                    start_time,
                ),
            )
            self.conn.commit()
        except Exception:
            self.err_count += 1

    def get_all_metrics(self) -> list[dict[str, Any]]:
        """Get all metrics from storage"""
        self.cursor.execute("""
            SELECT *
            FROM metrics
        """)
        try:
            data = self.cursor.fetchall()
            columns = [i[0] for i in self.cursor.description]
            return [dict(zip(columns, row)) for row in data]
        except Exception:
            self.err_count += 1
            return []

    def add_trace_mapping(self, trace_id, test_title):
        """Add a trace ID to name mapping to the database"""
        try:
            self.cursor.execute(
                """
                INSERT OR REPLACE INTO trace_mappings (trace_id, test_title)
                VALUES (?, ?)
            """,
                (trace_id, test_title),
            )
            self.conn.commit()
        except Exception:
            self.err_count += 1

    def get_all_trace_mappings(self) -> list[dict[str, Any]]:
        """Get all trace mappings from the database"""
        self.cursor.execute('SELECT * FROM trace_mappings')
        try:
            data = self.cursor.fetchall()
            columns = [i[0] for i in self.cursor.description]
            return [dict(zip(columns, row)) for row in data]
        except Exception:
            self.err_count += 1
            return []

    def clear(self) -> None:
        """Clear all metrics from storage"""
        try:
            self.cursor.execute('DELETE FROM metrics')
            self.conn.commit()
        except Exception:
            self.err_count += 1

    def close(self) -> None:
        """Close the database connection"""
        try:
            if hasattr(self, 'conn'):
                self.conn.close()
        except Exception:
            self.err_count += 1


class LLMSpeedCollector(CustomLogger):
    @classmethod
    def load_from_config(cls, config: dict | str | None):
        """
        {
            "<generation_name>": {
                "default": {
                    "ttft_threshold": 1.0,
                    "total_threshold": 2.0,
                    "token_throughput_threshold": 3.0,
                },
                "<test_title>": {
                    "ttft_threshold": 1.0,
                    "total_threshold": 2.0,
                    "token_throughput_threshold": 3.0,
                }
            }
        }
        """
        if isinstance(config, str):
            config = json.loads(config)
        config = config or {}
        if not isinstance(config, dict):
            raise TypeError(f'Invalid config: {config}, should be a dict or a JSON string.')
        return cls(config)

    def set_logger(self, logger):
        self.logger = logger

    def __init__(self, config: dict):
        self.config = config
        from heracles.core.logger import heracles_logger as logger

        self.logger = logger
        self.threshold = 0.9
        self.storage = MetricsStorage()
        self._register_litellm_callbacks()

    def _register_litellm_callbacks(self):
        litellm.callbacks = [self]

    def _unregister_litellm_callbacks(self):
        pass

    def register_trace_mapping(self, trace_id, test_title):
        """Register a trace ID to name mapping in both memory and database"""
        self.storage.add_trace_mapping(trace_id, test_title)

    async def my_custom_callback(
        self,
        kwargs,
        completion_response,
        start_time,
        end_time,
    ):
        try:
            generation_name = kwargs['litellm_params']['metadata']['generation_name']
            trace_name = kwargs['litellm_params']['metadata']['trace_name']
            trace_id = kwargs['litellm_params']['metadata']['trace_id']

            model_name = kwargs['model']
            ttft_cost = kwargs['standard_logging_object']['completionStartTime'] - kwargs['standard_logging_object']['startTime']  # noqa
            total_cost = kwargs['standard_logging_object']['endTime'] - kwargs['standard_logging_object']['startTime']
            total_tokens = kwargs['standard_logging_object']['response']['usage']['completion_tokens']

            # Get the first two messages from the input
            input_messages = kwargs.get('messages', [])
            first_two_messages = input_messages[:2] if input_messages else []
            for msg in first_two_messages:
                content = msg.get('content', '')
                if isinstance(content, list):
                    content = json.dumps(content)
                if not isinstance(content, str):
                    content = str(content)
                msg['content'] = content
            first_two_messages_content = ''.join([msg.get('content', '') for msg in first_two_messages])
            first_two_messages_md5 = hashlib.md5(first_two_messages_content.encode()).hexdigest()
            start_time = kwargs['standard_logging_object']['startTime']

            # Store metrics
            self.storage.add_metric(
                generation_name=generation_name,
                trace_id=trace_id,
                trace_name=trace_name,
                model_name=model_name,
                ttft_cost=ttft_cost,
                total_cost=total_cost,
                total_tokens=total_tokens,
                token_throughput=total_tokens / total_cost,
                first_two_messages=first_two_messages_md5,
                start_time=start_time,
            )
        except Exception as e:
            self.logger.error(f'LLM Speed Collector callback failed - error: {e}')

    async def async_log_success_event(self, kwargs, response_obj, start_time, end_time):
        await self.my_custom_callback(kwargs, response_obj, start_time, end_time)

    async def async_log_failure_event(self, kwargs, response_obj, start_time, end_time):
        await self.my_custom_callback(kwargs, response_obj, start_time, end_time)

    def _get_merged_metrics(self):
        """
        Merge metrics based on MD5 hash of first two messages:
        - ttft: take from first message
        - total_tokens: sum of all messages
        - total_cost: sum of all messages
        - model_name, trace_id, trace_name, generation_name: take from last message
        - token_throughput: calculated as total_tokens / total_cost
        """
        all_metrics = self.storage.get_all_metrics()
        merged_metrics = []

        grouped_metrics = defaultdict(list)
        for metric in all_metrics:
            grouped_metrics[(metric['trace_id'], metric['first_two_messages'])].append(metric)

        for (trace_id, md5_hash), metrics in grouped_metrics.items():
            metrics.sort(key=lambda x: x['start_time'])
            if len(metrics) == 1:
                ttft_cost = metrics[0]['ttft_cost']
            else:
                ttft_cost = sum(m['total_cost'] for m in metrics[:-1]) + metrics[-1]['ttft_cost']
            total_tokens = sum(m['total_tokens'] for m in metrics)
            total_cost = sum(m['total_cost'] for m in metrics)
            model_name = metrics[-1]['model_name']
            trace_name = metrics[-1]['trace_name']
            generation_name = metrics[-1]['generation_name']
            if 'CodeRole' in generation_name:
                generation_name = generation_name.rsplit(':', 1)[0]

            token_throughput = total_tokens / total_cost if total_cost > 0 else 0

            merged_metrics.append(
                {
                    'generation_name': generation_name,
                    'trace_id': trace_id,
                    'trace_name': trace_name,
                    'model_name': model_name,
                    'ttft_cost': ttft_cost,
                    'total_cost': total_cost,
                    'total_tokens': total_tokens,
                    'token_throughput': token_throughput,
                    'first_two_messages': md5_hash,
                    'step_count': len(metrics),
                }
            )

        return merged_metrics

    def _get_p_metrics(self):
        report = {}
        merged_metrics = self._get_merged_metrics()
        generation_names = set([m['generation_name'] for m in merged_metrics])
        for generation_name in generation_names:
            metrics_list = [m for m in merged_metrics if m['generation_name'] == generation_name]

            if not metrics_list:
                continue

            ttft_costs = [m['ttft_cost'] for m in metrics_list]
            total_costs = [m['total_cost'] for m in metrics_list]
            token_throughputs = [m['token_throughput'] for m in metrics_list]
            step_counts = [m['step_count'] for m in metrics_list]
            model_name = metrics_list[0]['model_name']

            p_ttft = np.percentile(ttft_costs, self.threshold * 100)
            p_total = np.percentile(total_costs, self.threshold * 100)
            p_throughput = np.percentile(token_throughputs, self.threshold * 100)
            p_step_count = np.percentile(step_counts, self.threshold * 100)

            slow_traces = []
            for i, (_ttft, _total, _throughput) in enumerate(zip(ttft_costs, total_costs, token_throughputs)):
                if _ttft >= p_ttft:
                    trace_id = metrics_list[i]['trace_id']
                    slow_traces.append(trace_id)

            report[generation_name] = {
                f'p{self.threshold*100}_ttft': p_ttft,
                f'p{self.threshold*100}_total': p_total,
                f'p{self.threshold*100}_token_throughput': p_throughput,
                f'p{self.threshold*100}_step_count': p_step_count,
                'sample_count': len(metrics_list),
                'model_name': model_name,
                'slow_traces': slow_traces,
            }

        return report

    def check_rules(self):
        fail_results = []
        merged_metrics = self._get_merged_metrics()

        for metric in merged_metrics:
            generation_name = metric['generation_name']
            trace_name = metric['trace_name']
            trace_id = metric['trace_id']
            ttft_cost = metric['ttft_cost']
            total_cost = metric['total_cost']
            token_throughput = metric['token_throughput']

            if generation_name not in self.config:
                continue

            generation_config = self.config[generation_name]
            testcase_config = generation_config.get(trace_name, generation_config['default'])

            ttft_check = ttft_cost <= testcase_config['ttft_threshold']
            total_check = total_cost <= testcase_config['total_threshold']
            throughput_check = token_throughput >= testcase_config['token_throughput_threshold']

            if not all([ttft_check, total_check, throughput_check]):
                fail_results.append(
                    {
                        'ttft_check': (ttft_check, ttft_cost, testcase_config['ttft_threshold']),
                        'total_check': (total_check, total_cost, testcase_config['total_threshold']),
                        'throughput_check': (
                            throughput_check,
                            token_throughput,
                            testcase_config['token_throughput_threshold'],
                        ),
                        'trace_id': trace_id,
                        'trace_name': trace_name,
                        'generation_name': generation_name,
                        'config': 'DEFAULT' if trace_name in generation_config else 'CUSTOM',
                    }
                )
        return fail_results

    def __del__(self):
        """Cleanup storage connection"""
        if hasattr(self, 'storage'):
            self.storage.close()

    def generate_report(self):
        """
        Generate a report with p90 metrics for each generation_name.

        Args:
            threshold: The percentile threshold to use (default 0.9 for p90)

        Returns:
            A dictionary with generation_name as keys and p90 metrics as values
        """
        if self.storage.err_count >= 2:
            err_str = (
                '\n'
                + '=' * 100
                + '\n'
                + f'LLM Speed Collector failed to collect metrics - error count: {self.storage.err_count}'
                + '\n'
                + 'May be you need to delete the db file (llm_tests/llm_metrics.db) and try again.'
                + '\n'
                + '=' * 100
                + '\n'
            )
            self.logger.error(err_str)
            return

        p_metrics = self._get_p_metrics()

        fail_results = self.check_rules()

        self._print_formatted_report(p_metrics, fail_results)

        return p_metrics, fail_results

    def _print_formatted_report(self, p_metrics, fail_results):
        """
        Print a formatted report of the metrics and fail results.

        Args:
            p_metrics: Dictionary of percentile metrics for each generation_name
            fail_results: List of failed metric checks
        """
        report_str = '\n'
        # Print header
        report_str += '=' * 100 + '\n'
        report_str += f'LLM SPEED COLLECTOR REPORT (P{self.threshold*100})\n'
        report_str += '-' * 100 + '\n'

        # Print metrics for each generation
        report_str += 'GENERATION METRICS:\n'

        # Prepare data for tabulate
        headers = ['GENERATION', 'TTFT (s)', 'TOTAL (s)', 'THROUGHPUT', 'STEP COUNT', 'SAMPLES', 'MODEL', 'SLOW TRACES']
        table_data = []

        for generation_name, metrics in p_metrics.items():
            ttft = metrics[f'p{self.threshold*100}_ttft']
            total = metrics[f'p{self.threshold*100}_total']
            throughput = metrics[f'p{self.threshold*100}_token_throughput']
            step_count = metrics[f'p{self.threshold*100}_step_count']
            samples = metrics['sample_count']
            model_name = metrics['model_name']
            slow_traces = metrics['slow_traces']
            table_data.append(
                [
                    generation_name,
                    f'{ttft:.4f}',
                    f'{total:.4f}',
                    f'{throughput:.4f}',
                    step_count,
                    samples,
                    model_name,
                    ', '.join(slow_traces),
                ]  # noqa
            )

        report_str += tabulate(table_data, headers=headers, tablefmt='grid')

        report_str += '\n' + '-' * 100 + '\n'

        if fail_results:
            report_str += '\nFAILED CHECKS:\n'

            # Prepare data for failed checks table
            fail_headers = ['GENERATION', 'TRACE', 'TRACE ID', 'TTFT', 'TOTAL', 'THROUGHPUT', 'CONFIG']
            fail_table_data = []

            for result in fail_results:
                generation = result['generation_name']
                trace = result['trace_name']
                trace_id = result['trace_id']

                ttft_check, ttft_value, ttft_threshold = result['ttft_check']
                total_check, total_value, total_threshold = result['total_check']
                throughput_check, throughput_value, throughput_threshold = result['throughput_check']

                # Format status with values and thresholds for failed checks
                ttft_status = 'PASS' if ttft_check else f'FAIL ({ttft_value:.4f}s > {ttft_threshold:.4f}s)'
                total_status = 'PASS' if total_check else f'FAIL ({total_value:.4f}s > {total_threshold:.4f}s)'
                throughput_status = 'PASS' if throughput_check else f'FAIL ({throughput_value:.4f} < {throughput_threshold:.4f})'
                config = result['config']

                fail_table_data.append([generation, trace, trace_id, ttft_status, total_status, throughput_status, config])

            report_str += tabulate(fail_table_data, headers=fail_headers, tablefmt='grid')
        else:
            report_str += '\nALL CHECKS PASSED!\n'

        report_str += '\n' + '-' * 100 + '\n'
        # Add slow trace details section
        report_str += '\nSLOW TRACE DETAILS:\n'

        trace_mappings = {m['trace_id']: m['test_title'] for m in self.storage.get_all_trace_mappings()}

        slow_headers = ['GENERATION', 'TEST TITLE', 'TRACE ID', 'TTFT (s)', 'TOTAL (s)', 'THROUGHPUT', 'STEP COUNT']
        slow_table_data = []

        for generation_name, metrics in p_metrics.items():
            for trace_id in metrics['slow_traces']:
                if 'score_result' in generation_name:  # FIXME 注意hardcode
                    continue
                trace_metric = next(
                    (m for m in self._get_merged_metrics() if m['trace_id'] == trace_id and m['generation_name'] == generation_name), None
                )
                if trace_metric:
                    test_title = trace_mappings.get(trace_id, 'Unknown')
                    slow_table_data.append(
                        [
                            generation_name,
                            test_title,
                            trace_id,
                            f"{trace_metric['ttft_cost']:.4f}",
                            f"{trace_metric['total_cost']:.4f}",
                            f"{trace_metric['token_throughput']:.4f}",
                            trace_metric['step_count'],
                        ]
                    )

        if slow_table_data:
            report_str += tabulate(slow_table_data, headers=slow_headers, tablefmt='grid')
        else:
            report_str += 'No slow traces found.\n'

        report_str += '\n' + '=' * 100 + '\n'

        self.logger.info(report_str)


llm_speed_collector = LLMSpeedCollector.load_from_config(os.environ.get('LLM_SPEED_COLLECTOR_CONFIG'))

if __name__ == '__main__':
    llm_speed_collector.generate_report()
