from typing import Literal, Optional, List
from llm_tests.basic.score_rule import ScoreRule
from llm_tests.utils.schema import ScoreModel, FileContentAssert, APIAssert
from heracles.core.utils.context_prompt_builder import PromptBuilder
from llm_tests.utils import custom_score_result
from heracles.core.exceptions import IDEServerFunCallException

class SmokeTestScoreRule(ScoreRule):
    weights = [1]

    def __init__(self, workspace, expectations: Optional[List[str | FileContentAssert | APIAssert]] = None, is_web_service: bool = False):
        super().__init__(workspace)
        self.expectations = expectations or []
        self.expectations += [
            FileContentAssert(file_path='.gitignore', content='.1024*', assert_type='contains'),
            FileContentAssert(file_path='.gitignore', content='!.1024', assert_type='contains'),
        ]
        self.is_web_service = is_web_service

    async def rule_file_changes_meet_task(self) -> ScoreModel:
        "规则: 文件变更符合任务需求"

        USER_PROMPT = """
{TASK}
{RECENT_FILE_CHANGES}

Identify if the task is successful or not.
"""
        user_message_builder = PromptBuilder(USER_PROMPT, self.workspace)
        message = await user_message_builder.format()
        res = await custom_score_result(self.workspace, message)
        return res

    async def rule_curl_local_server_success(self) -> ScoreModel:
        "规则: 本地 server 对应端口可以正常响应 curl"

        local_url = await self.workspace.tools.local_url()
        if not local_url:
            self.errors.append(f"无法通过{self.rule_curl_local_server_success.__doc__}, 原因: 无法获取本地 url")
            return ScoreModel(value=0.0, comment=f"无法通过{self.rule_curl_local_server_success.__doc__}, 原因: 无法获取本地 url")
        try:
            res = await self.workspace.playground.func_call('agent_terminal_with_result', f"curl -s {local_url}")
            return ScoreModel(value=1.0, comment='本地 server 启动成功')
        except IDEServerFunCallException as e:
            self.errors.append(f"无法通过{self.rule_curl_local_server_success.__doc__}, 原因: {str(e)} {res}")
            return ScoreModel(value=0.0, comment=f"无法通过{self.rule_curl_local_server_success.__doc__}, 原因: {str(e)} {res}")

    async def rule_result_can_not_contain_abandoned(self) -> ScoreModel:
        "规则: task 中所有 action 不能失败(abandoned)"

        total_actions: int = 0
        abandoned_actions: int = 0
        score_model = ScoreModel(value=1.0, comment=f"成功通过{self.rule_result_can_not_contain_abandoned.__doc__}")

        # 轮询 task.steps.actions，计算 abandoned 类型的 actions 占比，打印结果，如果有任何失败，则报告失败
        for step in self.workspace.task.task_steps:
            for action in step.task_actions:
                total_actions += 1
                if action.status.value == 'abandoned':
                    abandoned_actions += 1

        if total_actions > 0 and abandoned_actions > 0:
            score_model = ScoreModel(value=0.0, comment=f'无法通过{self.rule_result_can_not_contain_abandoned.__doc__}, \
                原因: abandoned actions: {abandoned_actions} / {total_actions}')
            self.errors.append(score_model.comment)
        return score_model

    def _get_rule_funcs(self, rule_set: Literal['all'] = 'all'):
        if rule_set == "all":
            rule_funcs = self._get_all_rules()
        if not self.is_web_service:
            rule_funcs.remove(self.rule_curl_local_server_success)

        return rule_funcs

    def _actual_result_for_expectations(self):
        return self.workspace.task.pretty_print()
