import pytest

from heracles.agent_roles.plan_role import PlanR<PERSON>
from heracles.core.schema import FileSnippet
from llm_tests.basic.score_rule.plan_role_score_rule import PlanRoleScoreRule


@pytest.mark.asyncio
async def test_plan_role_for_think(create_role, create_test_data, mocker):
    test_data = create_test_data(
        title='PlanRole: think格式化',
        description='Test if PlanRole avoids redundant file reads when File Snippets are provided',
        project_state_id='fadeOut-game-main',
        goal='Enhance game win feedback with sound and visual elements',
        goal_detail=(
            'Implement a more engaging win condition by adding sound effects and visual feedback. '
            'Replace alert dialogs with an integrated DOM element to display the winner, '
            'and add sound effects for a better user experience.'
        ),
        proposed_list=[
            'Add a winner display element to index.html for visual feedback',
            'Implement sound effects and winner display logic in script.js',
        ],
        expectations=[
            '输出的纯文本中, 大多数必要的函数名或代码片段都正确使用了行内代码格式或代码块格式，eg: `<div>`, `.tooltip`',
            '输出的纯文本中, 正确使用了markdown链接语法去引用文件， eg: `[script.js](src/script.js#L1-L116)`',
            '输出的纯文本中, HTML标签都使用了代码块格式或行内代码格式',
        ],
    )

    plan_role, context = await create_role(test_data, PlanRole)

    related_file_snippets: list[FileSnippet] = [
        FileSnippet(
            path=fp.replace('file://', ''),
            content=await context.workspace.tools.read_file(fp.replace('file://', ''), should_read_entire_file=True),
            row_start=-1,
            row_end=-1,
        )
        for fp in ['file://script.js', 'file://index.html']
    ]

    think_result = await plan_role._think_about_plan(
        goal=test_data.goal,
        goal_detail=test_data.goal_detail,
        proposed_list=test_data.proposed_list,
        related_snippets=related_file_snippets,
    )

    final_score = await PlanRoleScoreRule(plan_role.workspace, think_result, test_data.expectations).execute_rules_and_score()
    context.score_model = final_score
    return final_score
