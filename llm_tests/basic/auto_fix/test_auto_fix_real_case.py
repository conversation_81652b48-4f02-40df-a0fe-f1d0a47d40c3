# title playground_id
# 为 HackerNews 开发一个介绍首页 802669041264230400
# 为 HackerNews 开发一个介绍首页 802693141437218816

# 开发一个博客 802669041452974080
# 开发一个博客 802695970176499712
# 开发一个博客 802701311937216512



# FastAPI 开发一个 todolist 项目，支持 PG 数据库 802669041117745152
# FastAPI 开发一个 todolist 项目，支持 PG 数据库 802698278281019392
# FastAPI 开发一个 todolist 项目，支持 PG 数据库 802708470632992768

# 开发一个番茄钟 802699714368765952


# 开发一个 rails 的 twitter-like app 802669041050320896
# 开发一个 rails 的 twitter-like app 802712262052806656

# 前端 Simple Todolist 802669041985966080
# 前端 Simple Todolist 802692854501003264



import pytest
import re
from heracles.core.schema.task import Task
from llm_tests.basic.score_rule.smoke_test_score_rule import SmokeTestScoreRule

@pytest.mark.asyncio
async def test_autofix_no_error(create_workspace, create_test_data):

    test_data = create_test_data(
        title="nextjs-fastapi初始化环境",
        description="为 nextjs-fastapi 模板项目初始化开发环境",
        project_state_id="nextjs-fastapi-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        is_web_service=True
    )
    workspace = await create_workspace(test_data.codezone_id)
    task = Task(title='nextjs-fastapi初始化环境', description='')
    workspace.set_task(task)
    controller = workspace.playground.agent_controller
    controller.task_state.set_state("working")
    await controller.start_auto_fix()
    smoke_test_score_rule = SmokeTestScoreRule(workspace, test_data.expectations)
    score_model = await smoke_test_score_rule.execute_rules_and_score('all')
    return score_model

