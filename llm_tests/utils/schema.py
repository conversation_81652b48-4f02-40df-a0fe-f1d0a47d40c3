import json

from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Literal
from heracles.core.schema import FileSnippet
from heracles.core.exceptions import IDEServerFunCallException
from llm_tests.datasets import LLMTestDataset

def extract_response(lines: List[str]):
    # 查找 body 开始的标志
    separator_index = -1
    for i, line in enumerate(lines):
        if line.strip() == '':
            separator_index = i
            break
        if ':' not in line and not line.startswith('HTTP/'):
            separator_index = i
            break
        if line.strip().startswith('{') or line.strip().startswith('<'):
            separator_index = i
            break

    if separator_index == -1:
        raise ValueError("无法找到 body 开始的标志")
    else:
        headers = lines[:separator_index]
        body = '\n'.join(lines[separator_index:])  # 包含标记行
        try:
            body = json.loads(body)
        except json.JSONDecodeError:
            pass
    return headers, body


class FileContentAssert(BaseModel):
    """FileContentAssert: 文件内容断言"""

    file_path: str = Field(description='文件路径')
    content: str = Field(description='文件内容')
    assert_type: Literal['contains', 'not_contains', 'equals', 'not_equals'] = Field(description='断言类型', default='contains')

    def assert_content(self, content: str):
        if self.assert_type == 'contains':
            assert self.content in content, f"'{self.content}' not in '{content}'"
        elif self.assert_type == 'not_contains':
            assert self.content not in content, f"'{self.content}' in '{content}'"
        elif self.assert_type == 'equals':
            assert self.content == content, f"'{self.content}' != '{content}'"
        elif self.assert_type == 'not_equals':
            assert self.content != content, f"'{self.content}' == '{content}'"
        else:
            raise ValueError(f'Invalid assert type: {self.assert_type}')

    async def assert_file(self, workspace):
        workspace.logger.info(f">> 执行文件内容断言: {self.file_path}")
        file_content = await workspace.tools.read_file_content(self.file_path)
        self.assert_content(file_content)


class APIAssert(BaseModel):
    """APIAssert: API 断言"""

    request_path: str = Field(description='API 相对地址，例如 /api/v1/users')
    request_method: Literal['GET', 'POST', 'PUT', 'DELETE'] = Field(description='API 方法')
    request_headers: Optional[dict] = Field(description='API 头', default=None)
    request_body: Optional[str | dict] = Field(description='API 体', default=None)
    response_status: int = Field(description='API 响应状态码')
    response_body: Optional[str | dict] = Field(description='API 响应体', default=None)
    assert_type: Optional[Literal['contains', 'not_contains', 'equals', 'not_equals']] = Field(description='response body 的断言类型', default=None) # noqa

    def assert_response_body(self, body: str | dict):
        if self.response_body is None:
            return
        if isinstance(self.response_body, str):
            if self.assert_type == 'contains':
                assert self.response_body in body, f"'{self.response_body}' not in '{body}'"
            elif self.assert_type == 'not_contains':
                assert self.response_body not in body, f"'{self.response_body}' in '{body}'"
            elif self.assert_type == 'equals':
                assert self.response_body == body, f"'{self.response_body}' != '{body}'"
            elif self.assert_type == 'not_equals':
                assert self.response_body != body, f"'{self.response_body}' == '{body}'"
        elif isinstance(self.response_body, dict):
            assert isinstance(body, dict), "response body must be json object"
            if self.assert_type == 'contains':
                for key, value in self.response_body.items():
                    assert body.get(key) == value, f"'{body[key]}' != '{value}'"
            elif self.assert_type == 'not_contains':
                for key, value in self.response_body.items():
                    assert body.get(key) != value, f"'{body[key]}' == '{value}'"
            elif self.assert_type == 'equals':
                assert self.response_body == body, f"'{self.response_body}' != '{body}'"
            elif self.assert_type == 'not_equals':
                assert self.response_body != body, f"'{self.response_body}' == '{body}'"

    def assert_response_status(self, status: int):
        assert self.response_status == status, f"{self.response_status} != {status}"

    async def assert_response(self, workspace, host=None):
        workspace.logger.info(f">> 执行 API 断言: {self.request_method} {self.request_path}")
        if not host:
            host = await workspace.tools.local_url()
            assert host, "无法获取本地 url"

        if self.request_headers:
            headers = ' '.join([f'-H "{k}: {v}"' for k, v in self.request_headers.items()])
        else:
            headers = ''

        try:
            if self.request_method == 'GET':
                res = await workspace.tools.run_cmd(
                    f"curl -si '{host}{self.request_path}' -X '{self.request_method}' {headers}"
                )
            else:
                if self.request_body:
                    if isinstance(self.request_body, dict):
                        json_data = json.dumps(self.request_body)
                        body = f"-d '{json_data}'"
                    else:
                        body = f"-d '{self.request_body}'"
                    res = await workspace.tools.run_cmd(
                        f"curl -si '{host}{self.request_path}' -X '{self.request_method}' {headers} {body}"
                    )
                else:
                    res = await workspace.tools.run_cmd(
                        f"curl -si '{host}{self.request_path}' -X '{self.request_method}' {headers}"
                    )
        except IDEServerFunCallException:
            pass

        lines: List[str] = res.splitlines()
        # 从 "HTTP/1.1 200 OK" 提取状态码
        status_code = int(lines[0].split()[1])
        self.assert_response_status(status_code)

        if self.response_body:
            # 处理 headers 和 body 的分隔
            try:
                headers, body = extract_response(lines)
                headers_str = '\n'.join(headers)
                workspace.logger.info(f'<<< headers: {headers_str}')
                workspace.logger.info(f'<<< body: {body}')
            except Exception as e:
                workspace.logger.error(f'解析响应时发生错误: {str(e)}')
                raise

            self.assert_response_body(body)

class AIAssert(BaseModel):
    """AIAssert: AI 辅助进行断言判断"""

    expect: str = Field(description='期望结果')
    actual: str = Field(description='实际结果')
    is_pass: bool = Field(description='是否通过')
    reason: str = Field(description='断言失败原因')

class TestDataBase(BaseModel):
    """TestDataBase: 测试用例基础结构"""

    title: str = Field(description='测试用例标题, 便于报告中理解')
    description: str = Field(description='测试用例说明, 便于报告中理解', default='')
    project_state_id: str = Field(description='项目状态ID')
    middlewares: List[Literal['redis', 'mysql', 'mongodb', 'postgres']] = Field(description='中间件', default_factory=list)
    is_web_service: bool = Field(description='是否为 web service', default=False)
    run_timeout: int = Field(description='run_command 运行等待时间', default=10)
    expectations: List[str | FileContentAssert | APIAssert] = Field(description='预期结果', default_factory=list)
    expect: str = Field(description='预期结果, 请详细说明, 便于报告中理解', default="", deprecated=True)
    dataset: LLMTestDataset = Field(description='测试数据集')

    @property
    def codezone_id(self):
        codezone = self.dataset.get_codezone_id_by_state_id(self.project_state_id)
        if not codezone:
            raise ValueError(f'codezone_id not found for state_id: {self.project_state_id}')
        return codezone.id

class BasicTestData(TestDataBase):
    """标准测试用例结构"""

    goal: str = Field(description='任务目标')
    goal_detail: str = Field(description='任务详情', default='')
    proposed_list: List[str] = Field(description='澄清列表', default_factory=list)

class TestRagData(TestDataBase):
    question: str = Field(description='RAG 问题')
    rag_expect: list[FileSnippet] = Field(description='RAG 期望结果')

class ScoreModel(BaseModel):
    """ScoreModel: 评分结构"""

    value: float = Field(description='评分结果, 0.0 - 1.0, 越大越代表优秀', ge=0.0, le=1.0, multiple_of=0.01)
    comment: str = Field(description='评分说明, 讲明为什么这么打分；返回时先体现规则名称')
