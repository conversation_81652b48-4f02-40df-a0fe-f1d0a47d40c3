# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_rails_twitter_like_app(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: Twitter-like APP",
        description="",
        project_state_id="blank-project-ruby-main",
        goal="开发一个 Twitter-like 系统",
        goal_detail="""Develop a twitter-like system based on rails + tailwindcss +  postgresql
- no need for authentication, just for demo
        """,
        middlewares=['postgres'],
        expectations=[
        ],
    )

    await run_whole_process(test_data)
