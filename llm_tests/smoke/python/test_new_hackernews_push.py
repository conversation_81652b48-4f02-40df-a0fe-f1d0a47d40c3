# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
# @pytest.mark.build_from_scratch
async def test_hackernews_push(run_whole_process, create_test_data):
    # SLACK_API_TOKEN=*********************************************************
    # SLACK_CHANNEL_ID=C08UL3APW9E
    test_data = create_test_data(
        title="0-1: hackernews 新闻推送到 slack",
        description="",
        project_state_id="blank-project-python-main",
        goal="Create a HackerNews to Slack notification script",
        goal_detail="""Develop a script to periodically fetch HackerNews stories and push to Slack:
1. Fetch latest news titles and links from HackerNews (via official API if available) every minute
2. Push fetched news to a specified Slack channel via Slack API
3. Use .env for managing user configuration parameters""",
        expectations=[
        ],
    )

    await run_whole_process(test_data)
