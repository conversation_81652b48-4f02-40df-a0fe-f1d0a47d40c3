import pytest


@pytest.mark.asyncio
# @pytest.mark.build_from_scratch
async def test_new_clone_github_repo(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 克隆 github 项目",
        description="",
        project_state_id="blank-project-python-main",
        goal="运行开源项目 OpenManus",
        goal_detail="""运行开源项目：https://github.com/FoundationAgents/OpenManus""",
        expectations=[
        ],
    )

    await run_whole_process(test_data)
