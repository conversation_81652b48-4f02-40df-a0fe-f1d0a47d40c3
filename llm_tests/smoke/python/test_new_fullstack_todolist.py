import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_fullstack_todolist(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 全栈todolist",
        description="",
        project_state_id="blank-project-python-main",
        goal="开发一个基于 FastAPI 的全栈 todolist 应用",
        goal_detail="""Help me create a todolist application with the following requirements:
- Use FastAPI as the backend framework
- Use SQLAlchemy for database operations
- Use Jinja2 templates for the frontend
- Implement basic CRUD operations for todo items
- Ensure data persistence using PostgreSQL database""",
        middlewares=['postgres'],
        expectations=[
        ],
    )

    await run_whole_process(test_data)
