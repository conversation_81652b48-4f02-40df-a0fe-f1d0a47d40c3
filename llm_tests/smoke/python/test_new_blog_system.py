import pytest


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_blog_system(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 博客系统",
        description="",
        project_state_id="blank-project-python-main",
        goal="Develop a blog system in Django and PostgreSQL, with comment, search, admin dashboard support editing and publishing",
        goal_detail="""Frontend (Next.js + TailwindCSS):
- Blog article list and detail pages
- Anonymous comment system
- Search with title and tags

Admin Dashboard:
- User authentication and authorization
- Article and Comment management, with rich text editor
""",
        middlewares=['postgres'],
        expectations=[
        ],
    )

    await run_whole_process(test_data)
