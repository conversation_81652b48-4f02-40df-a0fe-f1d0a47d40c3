import pytest

from llm_tests.utils.schema import FileContentAssert


@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_todolist(run_whole_process, create_test_data):
    test_data = create_test_data(
        title='从0到1：todolist 初始化',
        description='Nodejs环境,todolist,nextjs,shadcn,tailwindcss',
        project_state_id='blank-project-main',
        goal='Develop a simple todolist with Next.js, Shadcn, TailwindCSS',
        goal_detail="""1. add, delete, edit tasks in one page;
2. support task priority, deadline, tags, description, status (todo, in progress, completed);
3. use nextjs, shadcn, tailwindcss, and use sqlite to store task data;
""",
        expectations=[
            FileContentAssert(file_path='.1024', content='npm run dev', assert_type='contains'),  # noqa: E501
        ],
    )

    await run_whole_process(test_data)
