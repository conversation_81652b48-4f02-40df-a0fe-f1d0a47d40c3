import pytest

@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_pomodoro_timer_app(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 开发一个漂亮的 Pomodoro 计时器应用",
        description="开发一个漂亮的 Pomodoro 计时器应用",
        project_state_id="blank-project-main",
        goal="Develop a beautiful Pomodoro timer app",
        goal_detail="""Help me create a Pomodoro timer web application with the following requirements:
1. Use React with TypeScript for the frontend, TailwindCSS for styling
2. Implement timer functionality: start, pause, reset; with several different preset periods
3. Add visual indicators for work/break periods""",
    )

    await run_whole_process(test_data)
