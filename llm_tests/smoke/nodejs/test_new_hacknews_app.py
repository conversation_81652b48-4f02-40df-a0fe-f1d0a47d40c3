import pytest

@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_hacknews_app(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 基于 HackerNews API 开发一个漂亮的客户端",
        description="基于 HackerNews API 开发一个漂亮的客户端：API",
        project_state_id="blank-project-main",
        goal="Develop a HackerNews client app",
        goal_detail="""Develop a HackerNews API client with the following specifications:
1. Tech Stack: React(with React Hooks), TailwindCSS, PostCSS, use Axios for API requests
2. Core Features: Display lists of newest, top, and best stories, Story detail page with threaded comments view, User profile viewing, Pagination support, Story search functionality
3. User Experience: Responsive design for mobile and desktop, Dark/Light theme toggle, Loading states and error handling, Smooth page transitions

API Reference: `https://github.com/HackerNews/API`
""",  # noqa: E501
        expectations=[
        ],
    )

    await run_whole_process(test_data)
