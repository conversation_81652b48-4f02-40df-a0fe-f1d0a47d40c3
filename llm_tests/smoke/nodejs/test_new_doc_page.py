# -*- coding: UTF-8 -*-
import pytest


@pytest.mark.asyncio
# @pytest.mark.build_from_scratch
async def test_new_nextra_website(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: 产品文档网站",
        description="",
        project_state_id="blank-project-main",
        goal="build a doc website with nextra",
        goal_detail="""Initialize a technical documentation website based on Nextra v2:
1. use the 'nextra-theme-docs' theme
2. set the primary color to #120394.""",
        expectations=[
        ],
    )

    await run_whole_process(test_data)
