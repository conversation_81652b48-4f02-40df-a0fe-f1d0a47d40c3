import pytest

@pytest.mark.asyncio
@pytest.mark.build_from_scratch
async def test_new_hacknews_homepage(run_whole_process, create_test_data):
    test_data = create_test_data(
        title="0-1: HackerNews 介绍首页",
        description="开发一个介绍 HackerNews 的网站首页",
        project_state_id="blank-project-main",
        goal="开发一个介绍 HackerNews 的网站首页",
        goal_detail="""Develop a beautiful homepage for HackerNews.
1. Use Next.js 13+ + shadcn/ui + Tailwind CSS
2. Page content requirements:
- Top navigation bar: including website logo, navigation menu
- Homepage banner: display the introduction and main features of HackerNews
- News list area: display the latest HackerNews news items
* Each news item contains information such as title, release time, author, number of comments, etc.
* Support paging or infinite scroll loading
* Click on the news item to jump to the original text""",
        expectations=[
        ],
    )

    await run_whole_process(test_data)
