import pytest
import re
from llm_tests.utils.schema import FileContentAssert

@pytest.mark.asyncio
async def test_remove_1024(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="tldraw-example初始化环境",
        description="为 Next.js 项目 simple tldraw 初始化开发环境",
        project_state_id="tldraw-example-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            FileContentAssert(file_path='.1024', content='run_command', assert_type='contains'),
        ],
    )
    async def after_function(workspace):
        await workspace.tools.delete_file('.1024')

    await run_whole_process(test_data, after_function=after_function)


@pytest.mark.asyncio
async def test_wrong_run_command(run_whole_process, create_test_data):

    test_data = create_test_data(
        title="tldraw-example初始化环境",
        description="为 Next.js 项目 simple tldraw 初始化开发环境",
        project_state_id="tldraw-example-main",
        goal="Initialize the Development Environment",
        goal_detail="",
        expectations=[
            ".1024文件的run_command命令是正常的,比如yarn dev等等",
        ],
    )

    async def after_function(workspace):
        content = await workspace.tools.read_file_content('.1024')
        modified_content = re.sub(r'run_command:.*?(?=\n|$)', "run_command: './yyyy'", content)
        await workspace.tools.write_file('.1024', modified_content)

    await run_whole_process(test_data, after_function=after_function)
