import pytest

from llm_tests.utils.schema import FileContentAssert


@pytest.mark.build_from_scratch
@pytest.mark.asyncio
async def test_snake_game_init(run_whole_process, create_test_data):
    test_data = create_test_data(
        title='从0到1：snake-game 初始化',
        description='HTML环境,贪吃蛇小游戏',
        project_state_id='blank-project-main',
        goal='开发一个贪吃蛇小游戏，支持积分计算及排名',
        goal_detail="""Develop a snake game that supports points calculation and ranking
1. Supports points calculation and ranking (shows points and ranking after each game);
2. Static front-end(html+javascript+css) implementation, Famicom(FC) style (pixel style, retro color matching, classic font);
3. Supports keyboard arrow keys to control the movement of the snake;
""",
        expectations=[
            FileContentAssert(
                file_path='.1024', content='browser-sync start', assert_type='contains'
            ),
        ],
    )

    await run_whole_process(test_data)
