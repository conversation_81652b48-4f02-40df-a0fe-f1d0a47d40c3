{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['no_proxy'] = '127.0.0.1,localhost,proxy.clackyai.com'\n", "# os.environ[\"LANGFUSE_TRACING_ENVIRONMENT\"] = \"develop\"\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import litellm\n", "litellm._turn_on_debug()\n", "litellm.set_verbose = True\n", "os.environ['LITELLM_LOG'] = 'DEBUG'\n", "os.environ['LANGFUSE_DEBUG'] = 'True'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m05-30 18:19:14.719\u001b[0m \u001b[1mINFO logger.py:133 -> AIAgent is starting, current log_level is INFO\u001b[0m\n", "\u001b[32m05-30 18:19:14.740\u001b[0m \u001b[1mINFO llm.py:39 -> langfuse is enabled, load config\u001b[0m\n", "\u001b[32m05-30 18:19:14.740\u001b[0m \u001b[1mINFO llm.py:66 -> Fetch model name dict from proxy: {}\u001b[0m\n"]}], "source": ["from heracles.agent_controller.llm import load_langfuse\n", "\n", "litellm.set_verbose=True\n", "litellm._turn_on_debug()\n", "litellm.drop_params=True\n", "load_langfuse()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m05-30 18:19:50.972\u001b[0m \u001b[33m\u001b[1mWARNING llm.py:85 -> Failed to get real name for model: litellm_proxy/gemini-2.5-flash-preview\u001b[0m\n"]}, {"data": {"text/plain": ["{'trace_id': 'auto-eb4bd31b-f1fc-48ba-9039-533fa8ecfe9e',\n", " 'trace_name': '',\n", " 'generation_name': 'auto-litellm-generation',\n", " 'tags': ['cxd']}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from heracles.core.config import get_env_var\n", "\n", "os.environ['LANGFUSE_PUBLIC_KEY'] = get_env_var('LANGFUSE_PUBLIC_KEY_OPTIONAL')\n", "os.environ['LANGFUSE_SECRET_KEY'] = get_env_var('LANGFUSE_SECRET_KEY_OPTIONAL')\n", "os.environ['LANGFUSE_HOST'] = get_env_var('LANGFUSE_HOST_OPTIONAL')\n", "# if debug := get_env_var('LANGFUSE_DEBUG_OPTIONAL'):\n", "os.environ['LANGFUSE_DEBUG'] = 'True'\n", "# litellm.success_callback = ['langfuse']\n", "# litellm.failure_callback = ['langfuse']\n", "from heracles.agent_controller.llm import LLM\n", "metadata = LLM().get_metadata()\n", "metadata\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message_json = {\n", "    'messages': [\n", "        {\n", "            'role': 'user',\n", "            'content': [\n", "                {'type': 'text', 'text': '这是什么图片'},\n", "            ],\n", "        }\n", "    ],\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置 API 密钥\n", "\n", "# litellm.api_key = \"sk-,+@hc=F%NHwV3pbbD]:F:tu3nak)E-YK}rhwq6c=eCYYMA5U+YBp!d7sv}CypTr*hr_Hkcjm3phQ7af,cFLmjZgp^%+k%@)?+Bho\"\n", "\n", "# litellm.api_base=\"https://proxy.clackyai.com\"\n", "\n", "\n", "# os.environ[\"AWS_ACCESS_KEY_ID\"] = \"********************\"\n", "\n", "# os.environ[\"AWS_SECRET_ACCESS_KEY\"] = \"0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8\"\n", "\n", "# os.environ[\"AWS_REGION_NAME\"] = \"us-west-2\"\n", "\n", "\n", "# LLM_FAST_MODEL_OPTIONAL=\"openrouter/google/gemini-2.0-flash-001\"\n", "\n", "# LLM_FAST_API_KEY_OPTIONAL=\"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\"\n", "\n", "# LLM_FAST_BASE_URL_OPTIONAL=\"https://openrouter.ai/api/v1\"\n", "\n", "import litellm\n", "\n", "\n", "res = await litellm.acompletion(\n", "    model='litellm_proxy/bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    api_key='sk-o8tUSr3R1dW_qzRgSQfmeg',\n", "    base_url='https://proxy.clackyai.com',\n", "    # api_key='sk-1234',\n", "    # base_url='http://127.0.0.1:4000',\n", "    **message_json,\n", "    # messages=messages,\n", "    # stream=True,\n", "    # stream_options={\"include_usage\": True}\n", ")\n", "\n", "\n", "# print(res.choices[0].message.content)\n", "\n", "# async for chunk in res:\n", "#     print(chunk.choices[0].delta.content or \"\")\n", "#     raise Exception(\"exception\")\n", "\n", "res.json()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "os.environ['AWS_ACCESS_KEY_ID'] = '********************'\n", "\n", "os.environ['AWS_SECRET_ACCESS_KEY'] = '0yu/P9qnYYGpfAZH5cM5oz5dlyTFcjkKF+diKRJ8'\n", "\n", "os.environ['AWS_REGION_NAME'] = 'us-west-2'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "\n", "client = boto3.client('bedrock-runtime', region_name='us-west-2')\n", "\n", "model_id = 'us.anthropic.claude-3-7-sonnet-20250219-v1:0'\n", "\n", "user_message = \"Describe the purpose of a 'hello world' program in one line.\"\n", "\n", "conversation = [\n", "    {\n", "        'role': 'user',\n", "        'content': [{'text': user_message}],\n", "    }\n", "]\n", "\n", "response = client.converse(\n", "    modelId=model_id,\n", "    messages=conversation,\n", "    inferenceConfig={'maxTokens': 512, 'temperature': 0.5, 'topP': 0.9},\n", ")\n", "\n", "# Extract and print the response text.\n", "\n", "response_text = response['output']['message']['content'][0]['text']\n", "\n", "print(response_text)\n"]}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}