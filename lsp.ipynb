{"cells": [{"cell_type": "code", "execution_count": 66, "id": "d69dbca1", "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import json\n", "import uuid\n", "\n", "# 启动pyright-langserver进程\n", "process = subprocess.Popen(\n", "    ['pylsp'],\n", "    cwd='/Users/<USER>/works/clacky-ai-agent/',\n", "    stdin=subprocess.PIPE,\n", "    stdout=subprocess.PIPE,\n", "    stderr=subprocess.PIPE,\n", "    universal_newlines=False,  # 使用二进制模式\n", ")\n", "\n", "\n", "# 发送LSP请求的函数\n", "def send_request(method, params=None):\n", "    request_id = str(uuid.uuid4())\n", "    request = {\n", "        'jsonrpc': '2.0',\n", "        'id': request_id,\n", "        'method': method,\n", "    }\n", "    if params is not None:\n", "        request['params'] = params\n", "\n", "    # 序列化请求\n", "    content = json.dumps(request).encode('utf-8')\n", "\n", "    # 添加Content-Length头部，这是LSP协议要求的\n", "    header = f'Content-Length: {len(content)}\\r\\n\\r\\n'.encode('utf-8')\n", "\n", "    # 发送请求\n", "    process.stdin.write(header + content)\n", "    process.stdin.flush()\n", "\n", "    # 返回请求ID，用于匹配响应\n", "    return request_id\n", "\n", "\n", "# 读取LSP响应的函数\n", "def read_response():\n", "    # 读取头部\n", "    content_length = None\n", "    while True:\n", "        header_line = process.stdout.readline().decode('utf-8').strip()\n", "        if header_line.startswith('Content-Length: '):\n", "            content_length = int(header_line[16:])\n", "        elif header_line == '':  # 空行表示头部结束\n", "            break\n", "\n", "    if content_length is None:\n", "        return None\n", "\n", "    # 读取内容\n", "    content = process.stdout.read(content_length).decode('utf-8')\n", "    return json.loads(content)\n"]}, {"cell_type": "code", "execution_count": 67, "id": "f3a8343b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始化响应: {'jsonrpc': '2.0', 'id': '7d2d046c-ce5b-41f6-99a6-cad11b3d4200', 'result': {'capabilities': {'codeActionProvider': True, 'codeLensProvider': {'resolveProvider': False}, 'completionProvider': {'resolveProvider': True, 'triggerCharacters': ['.']}, 'documentFormattingProvider': True, 'documentHighlightProvider': True, 'documentRangeFormattingProvider': True, 'documentSymbolProvider': True, 'definitionProvider': True, 'executeCommandProvider': {'commands': []}, 'hoverProvider': True, 'referencesProvider': True, 'renameProvider': True, 'foldingRangeProvider': True, 'signatureHelpProvider': {'triggerCharacters': ['(', ',', '=']}, 'textDocumentSync': {'change': 2, 'save': {'includeText': True}, 'openClose': True}, 'notebookDocumentSync': {'notebookSelector': [{'cells': [{'language': 'python'}]}]}, 'workspace': {'workspaceFolders': {'supported': True, 'changeNotifications': True}}, 'experimental': {}}, 'serverInfo': {'name': 'pylsp', 'version': '1.12.2'}}}\n"]}], "source": ["# 初始化LSP会话\n", "initialize_params = {\n", "    'processId': process.pid,\n", "    'clientInfo': {'name': 'Visual Studio Code - Insiders', 'version': '1.81.0-insider'},\n", "    'locale': 'en',\n", "    'rootPath': '/Users/<USER>/works/clacky-ai-agent/',\n", "    'rootUri': 'file:///Users/<USER>/works/clacky-ai-agent',\n", "    'capabilities': {\n", "        'workspace': {\n", "            'applyEdit': True,\n", "            'workspaceEdit': {\n", "                'documentChanges': True,\n", "                'resourceOperations': ['create', 'rename', 'delete'],\n", "                'failureHandling': 'textOnlyTransactional',\n", "                'normalizesLineEndings': True,\n", "                'changeAnnotationSupport': {'groupsOnLabel': True},\n", "            },\n", "            'configuration': True,\n", "            'didChangeWatchedFiles': {'dynamicRegistration': True, 'relativePatternSupport': True},\n", "            'symbol': {\n", "                'dynamicRegistration': True,\n", "                'symbolKind': {'valueSet': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]},\n", "                'tagSupport': {'valueSet': [1]},\n", "                'resolveSupport': {'properties': ['location.range']},\n", "            },\n", "            'codeLens': {'refreshSupport': True},\n", "            'executeCommand': {'dynamicRegistration': True},\n", "            'didChangeConfiguration': {'dynamicRegistration': True},\n", "            'workspaceFolders': True,\n", "            'semanticTokens': {'refreshSupport': True},\n", "            'fileOperations': {\n", "                'dynamicRegistration': True,\n", "                'didCreate': True,\n", "                'did<PERSON><PERSON><PERSON>': True,\n", "                'didDelete': True,\n", "                'willCreate': True,\n", "                'will<PERSON><PERSON><PERSON>': True,\n", "                'willDelete': True,\n", "            },\n", "            'inlineValue': {'refreshSupport': True},\n", "            'inlayHint': {'refreshSupport': True},\n", "            'diagnostics': {'refreshSupport': True},\n", "        },\n", "        'textDocument': {\n", "            'publishDiagnostics': {\n", "                'relatedInformation': True,\n", "                'versionSupport': <PERSON><PERSON><PERSON>,\n", "                'tagSupport': {'valueSet': [1, 2]},\n", "                'codeDescriptionSupport': True,\n", "                'dataSupport': True,\n", "            },\n", "            'synchronization': {'dynamicRegistration': True, 'willSave': True, 'willSaveWaitUntil': True, 'didSave': True},\n", "            'completion': {\n", "                'dynamicRegistration': True,\n", "                'contextSupport': True,\n", "                'completionItem': {\n", "                    'snippetSupport': True,\n", "                    'commitCharactersSupport': True,\n", "                    'documentationFormat': ['markdown', 'plaintext'],\n", "                    'deprecatedSupport': True,\n", "                    'preselectSupport': True,\n", "                    'tagSupport': {'valueSet': [1]},\n", "                    'insertReplaceSupport': True,\n", "                    'resolveSupport': {'properties': ['documentation', 'detail', 'additionalTextEdits']},\n", "                    'insertTextModeSupport': {'valueSet': [1, 2]},\n", "                    'labelDetailsSupport': True,\n", "                },\n", "                'insertTextMode': 2,\n", "                'completionItemKind': {'valueSet': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]},\n", "                'completionList': {'itemDefaults': ['commitCharacters', 'editRange', 'insertTextFormat', 'insertTextMode']},\n", "            },\n", "            'hover': {'dynamicRegistration': True, 'contentFormat': ['markdown', 'plaintext']},\n", "            'signatureHelp': {\n", "                'dynamicRegistration': True,\n", "                'signatureInformation': {\n", "                    'documentationFormat': ['markdown', 'plaintext'],\n", "                    'parameterInformation': {'labelOffsetSupport': True},\n", "                    'activeParameterSupport': True,\n", "                },\n", "                'contextSupport': True,\n", "            },\n", "            'definition': {'dynamicRegistration': True, 'linkSupport': True},\n", "            'references': {'dynamicRegistration': True},\n", "            'documentHighlight': {'dynamicRegistration': True},\n", "            'documentSymbol': {\n", "                'dynamicRegistration': True,\n", "                'symbolKind': {'valueSet': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]},\n", "                'hierarchicalDocumentSymbolSupport': True,\n", "                'tagSupport': {'valueSet': [1]},\n", "                'labelSupport': True,\n", "            },\n", "            'codeAction': {\n", "                'dynamicRegistration': True,\n", "                'isPreferredSupport': True,\n", "                'disabledSupport': True,\n", "                'dataSupport': True,\n", "                'resolveSupport': {'properties': ['edit']},\n", "                'codeActionLiteralSupport': {\n", "                    'codeActionKind': {\n", "                        'valueSet': [\n", "                            '',\n", "                            'quickfix',\n", "                            'refactor',\n", "                            'refactor.extract',\n", "                            'refactor.inline',\n", "                            'refactor.rewrite',\n", "                            'source',\n", "                            'source.organizeImports',\n", "                        ]\n", "                    }\n", "                },\n", "                'honorsChangeAnnotations': <PERSON><PERSON><PERSON>,\n", "            },\n", "            'codeLens': {'dynamicRegistration': True},\n", "            'formatting': {'dynamicRegistration': True},\n", "            'rangeFormatting': {'dynamicRegistration': True},\n", "            'onTypeFormatting': {'dynamicRegistration': True},\n", "            'rename': {\n", "                'dynamicRegistration': True,\n", "                'prepareSupport': True,\n", "                'prepareSupportDefaultBehavior': 1,\n", "                'honorsChangeAnnotations': True,\n", "            },\n", "            'documentLink': {'dynamicRegistration': True, 'tooltipSupport': True},\n", "            'typeDefinition': {'dynamicRegistration': True, 'linkSupport': True},\n", "            'implementation': {'dynamicRegistration': True, 'linkSupport': True},\n", "            'colorProvider': {'dynamicRegistration': True},\n", "            'foldingRange': {\n", "                'dynamicRegistration': True,\n", "                'rangeLimit': 5000,\n", "                'lineFoldingOnly': True,\n", "                'foldingRangeKind': {'valueSet': ['comment', 'imports', 'region']},\n", "                'foldingRange': {'collapsedText': False},\n", "            },\n", "            'declaration': {'dynamicRegistration': True, 'linkSupport': True},\n", "            'selectionRange': {'dynamicRegistration': True},\n", "            'callHierarchy': {'dynamicRegistration': True},\n", "            'semanticTokens': {\n", "                'dynamicRegistration': True,\n", "                'tokenTypes': [\n", "                    'namespace',\n", "                    'type',\n", "                    'class',\n", "                    'enum',\n", "                    'interface',\n", "                    'struct',\n", "                    'typeParameter',\n", "                    'parameter',\n", "                    'variable',\n", "                    'property',\n", "                    'enumMember',\n", "                    'event',\n", "                    'function',\n", "                    'method',\n", "                    'macro',\n", "                    'keyword',\n", "                    'modifier',\n", "                    'comment',\n", "                    'string',\n", "                    'number',\n", "                    'regexp',\n", "                    'operator',\n", "                    'decorator',\n", "                ],\n", "                'tokenModifiers': [\n", "                    'declaration',\n", "                    'definition',\n", "                    'readonly',\n", "                    'static',\n", "                    'deprecated',\n", "                    'abstract',\n", "                    'async',\n", "                    'modification',\n", "                    'documentation',\n", "                    'defaultLibrary',\n", "                ],\n", "                'formats': ['relative'],\n", "                'requests': {'range': True, 'full': {'delta': True}},\n", "                'multilineTokenSupport': <PERSON><PERSON><PERSON>,\n", "                'overlappingTokenSupport': <PERSON><PERSON><PERSON>,\n", "                'serverCancelSupport': True,\n", "                'augmentsSyntaxTokens': <PERSON><PERSON><PERSON>,\n", "            },\n", "            'linkedEditingRange': {'dynamicRegistration': True},\n", "            'typeHierarchy': {'dynamicRegistration': True},\n", "            'inlineValue': {'dynamicRegistration': True},\n", "            'inlayHint': {\n", "                'dynamicRegistration': True,\n", "                'resolveSupport': {'properties': ['tooltip', 'textEdits', 'label.tooltip', 'label.location', 'label.command']},\n", "            },\n", "            'diagnostic': {'dynamicRegistration': True, 'relatedDocumentSupport': False},\n", "        },\n", "        'window': {\n", "            'showMessage': {'messageActionItem': {'additionalPropertiesSupport': True}},\n", "            'showDocument': {'support': True},\n", "            'workDoneProgress': True,\n", "        },\n", "        'general': {\n", "            'staleRequestSupport': {\n", "                'cancel': True,\n", "                'retryOnContentModified': [\n", "                    'textDocument/semanticTokens/full',\n", "                    'textDocument/semanticTokens/range',\n", "                    'textDocument/semanticTokens/full/delta',\n", "                ],\n", "            },\n", "            'regularExpressions': {'engine': 'ECMAScript', 'version': 'ES2020'},\n", "            'markdown': {\n", "                'parser': 'marked',\n", "                'version': '1.1.0',\n", "                'allowedTags': [\n", "                    'ul',\n", "                    'li',\n", "                    'p',\n", "                    'code',\n", "                    'blockquote',\n", "                    'ol',\n", "                    'h1',\n", "                    'h2',\n", "                    'h3',\n", "                    'h4',\n", "                    'h5',\n", "                    'h6',\n", "                    'hr',\n", "                    'em',\n", "                    'pre',\n", "                    'table',\n", "                    'thead',\n", "                    'tbody',\n", "                    'tr',\n", "                    'th',\n", "                    'td',\n", "                    'div',\n", "                    'del',\n", "                    'a',\n", "                    'strong',\n", "                    'br',\n", "                    'img',\n", "                    'span',\n", "                ],\n", "            },\n", "            'positionEncodings': ['utf-16'],\n", "        },\n", "        'notebookDocument': {'synchronization': {'dynamicRegistration': True, 'executionSummarySupport': True}},\n", "        'experimental': {\n", "            'snippetTextEdit': True,\n", "            'codeActionGroup': True,\n", "            'hoverActions': True,\n", "            'serverStatusNotification': True,\n", "            'colorDiagnosticOutput': True,\n", "            'openServerLogs': True,\n", "            'commands': {\n", "                'commands': [\n", "                    'rust-analyzer.runSingle',\n", "                    'rust-analyzer.debugSingle',\n", "                    'rust-analyzer.showReferences',\n", "                    'rust-analyzer.gotoLocation',\n", "                    'editor.action.triggerParameterHints',\n", "                ]\n", "            },\n", "        },\n", "    },\n", "    'initializationOptions': {\n", "        'cargoRunner': None,\n", "        'runnables': {'extraEnv': None, 'problemMatcher': ['$rustc'], 'command': None, 'extraArgs': []},\n", "        'server': {'path': None, 'extraEnv': None},\n", "        'trace': {'server': 'verbose', 'extension': True},\n", "        'debug': {\n", "            'engine': 'auto',\n", "            'sourceFileMap': {'/rustc/<id>': '${env:USERPROFILE}/.rustup/toolchains/<toolchain-id>/lib/rustlib/src/rust'},\n", "            'openDebugPane': <PERSON><PERSON><PERSON>,\n", "            'engineSettings': {},\n", "        },\n", "        'restartServerOnConfigChange': <PERSON><PERSON><PERSON>,\n", "        'typing': {'continueCommentsOnNewline': True, 'autoClosingAngleBrackets': {'enable': False}},\n", "        'diagnostics': {\n", "            'previewRustcOutput': <PERSON><PERSON><PERSON>,\n", "            'useRustcErrorCode': False,\n", "            'disabled': [],\n", "            'enable': True,\n", "            'experimental': {'enable': False},\n", "            'remapPrefix': {},\n", "            'warningsAsHint': [],\n", "            'warningsAsInfo': [],\n", "        },\n", "        'discoverProjectCommand': None,\n", "        'showUnlinkedFileNotification': True,\n", "        'showDependenciesExplorer': True,\n", "        'assist': {'emitMustUse': False, 'expressionFillDefault': 'todo'},\n", "        'cachePriming': {'enable': True, 'numThreads': 0},\n", "        'cargo': {\n", "            'autoreload': True,\n", "            'buildScripts': {\n", "                'enable': True,\n", "                'invocationLocation': 'workspace',\n", "                'invocationStrategy': 'per_workspace',\n", "                'overrideCommand': None,\n", "                'useRustcWrapper': True,\n", "            },\n", "            'cfgs': {},\n", "            'extraArgs': [],\n", "            'extraEnv': {},\n", "            'features': [],\n", "            'noDefaultFeatures': <PERSON><PERSON><PERSON>,\n", "            'sysroot': 'discover',\n", "            'sysrootSrc': None,\n", "            'target': None,\n", "            'unsetTest': ['core'],\n", "        },\n", "        'checkOnSave': True,\n", "        'check': {\n", "            'allTargets': True,\n", "            'command': 'check',\n", "            'extraArgs': [],\n", "            'extraEnv': {},\n", "            'features': None,\n", "            'invocationLocation': 'workspace',\n", "            'invocationStrategy': 'per_workspace',\n", "            'noDefaultFeatures': None,\n", "            'overrideCommand': None,\n", "            'targets': None,\n", "        },\n", "        'completion': {\n", "            'autoimport': {'enable': True},\n", "            'autoself': {'enable': True},\n", "            'callable': {'snippets': 'fill_arguments'},\n", "            'limit': None,\n", "            'postfix': {'enable': True},\n", "            'privateEditable': {'enable': False},\n", "            'snippets': {\n", "                'custom': {\n", "                    'Arc::new': {\n", "                        'postfix': 'arc',\n", "                        'body': 'Arc::new(${receiver})',\n", "                        'requires': 'std::sync::Arc',\n", "                        'description': 'Put the expression into an `Arc`',\n", "                        'scope': 'expr',\n", "                    },\n", "                    'Rc::new': {\n", "                        'postfix': 'rc',\n", "                        'body': 'Rc::new(${receiver})',\n", "                        'requires': 'std::rc::Rc',\n", "                        'description': 'Put the expression into an `Rc`',\n", "                        'scope': 'expr',\n", "                    },\n", "                    'Box::pin': {\n", "                        'postfix': 'pinbox',\n", "                        'body': 'Box::pin(${receiver})',\n", "                        'requires': 'std::boxed::Box',\n", "                        'description': 'Put the expression into a pinned `Box`',\n", "                        'scope': 'expr',\n", "                    },\n", "                    'Ok': {\n", "                        'postfix': 'ok',\n", "                        'body': 'Ok(${receiver})',\n", "                        'description': 'Wrap the expression in a `Result::Ok`',\n", "                        'scope': 'expr',\n", "                    },\n", "                    'Err': {\n", "                        'postfix': 'err',\n", "                        'body': 'Err(${receiver})',\n", "                        'description': 'Wrap the expression in a `Result::Err`',\n", "                        'scope': 'expr',\n", "                    },\n", "                    'Some': {\n", "                        'postfix': 'some',\n", "                        'body': 'Some(${receiver})',\n", "                        'description': 'Wrap the expression in an `Option::Some`',\n", "                        'scope': 'expr',\n", "                    },\n", "                }\n", "            },\n", "        },\n", "        'files': {'excludeDirs': [], 'watcher': 'client'},\n", "        'highlightRelated': {\n", "            'breakPoints': {'enable': True},\n", "            'closureCaptures': {'enable': True},\n", "            'exitPoints': {'enable': True},\n", "            'references': {'enable': True},\n", "            'yieldPoints': {'enable': True},\n", "        },\n", "        'hover': {\n", "            'actions': {\n", "                'debug': {'enable': True},\n", "                'enable': True,\n", "                'gotoTypeDef': {'enable': True},\n", "                'implementations': {'enable': True},\n", "                'references': {'enable': False},\n", "                'run': {'enable': True},\n", "            },\n", "            'documentation': {'enable': True, 'keywords': {'enable': True}},\n", "            'links': {'enable': True},\n", "            'memoryLayout': {'alignment': 'hexadecimal', 'enable': True, 'niches': False, 'offset': 'hexadecimal', 'size': 'both'},\n", "        },\n", "        'imports': {\n", "            'granularity': {'enforce': False, 'group': 'crate'},\n", "            'group': {'enable': True},\n", "            'merge': {'glob': True},\n", "            'prefer': {'no': {'std': False}},\n", "            'prefix': 'plain',\n", "        },\n", "        'inlayHints': {\n", "            'bindingModeHints': {'enable': False},\n", "            'chainingHints': {'enable': True},\n", "            'closingBraceHints': {'enable': True, 'minLines': 25},\n", "            'closureCaptureHints': {'enable': False},\n", "            'closureReturnTypeHints': {'enable': 'never'},\n", "            'closureStyle': 'impl_fn',\n", "            'discriminantHints': {'enable': 'never'},\n", "            'expressionAdjustmentHints': {'enable': 'never', 'hideOutsideUnsafe': False, 'mode': 'prefix'},\n", "            'lifetimeElisionHints': {'enable': 'never', 'useParameterNames': False},\n", "            'maxLength': 25,\n", "            'parameterHints': {'enable': True},\n", "            'reborrowHints': {'enable': 'never'},\n", "            'renderColons': True,\n", "            'typeHints': {'enable': True, 'hideClosureInitialization': False, 'hideNamedConstructor': False},\n", "        },\n", "        'interpret': {'tests': False},\n", "        'joinLines': {'joinAssignments': True, 'joinElseIf': True, 'removeTrailingComma': True, 'unwrapTrivialBlock': True},\n", "        'lens': {\n", "            'debug': {'enable': True},\n", "            'enable': True,\n", "            'forceCustomCommands': True,\n", "            'implementations': {'enable': True},\n", "            'location': 'above_name',\n", "            'references': {\n", "                'adt': {'enable': False},\n", "                'enumVariant': {'enable': False},\n", "                'method': {'enable': False},\n", "                'trait': {'enable': False},\n", "            },\n", "            'run': {'enable': True},\n", "        },\n", "        'linkedProjects': [],\n", "        'lru': {'capacity': None, 'query': {'capacities': {}}},\n", "        'notifications': {'cargoTomlNotFound': True},\n", "        'numThreads': None,\n", "        'procMacro': {'attributes': {'enable': True}, 'enable': True, 'ignored': {}, 'server': None},\n", "        'references': {'excludeImports': False},\n", "        'rustc': {'source': None},\n", "        'rustfmt': {'extraArgs': [], 'overrideCommand': None, 'rangeFormatting': {'enable': False}},\n", "        'semanticHighlighting': {\n", "            'doc': {'comment': {'inject': {'enable': True}}},\n", "            'nonStandardTokens': True,\n", "            'operator': {'enable': True, 'specialization': {'enable': False}},\n", "            'punctuation': {'enable': False, 'separate': {'macro': {'bang': False}}, 'specialization': {'enable': False}},\n", "            'strings': {'enable': True},\n", "        },\n", "        'signatureInfo': {'detail': 'full', 'documentation': {'enable': True}},\n", "        'workspace': {'symbol': {'search': {'kind': 'only_types', 'limit': 128, 'scope': 'workspace'}}},\n", "    },\n", "    'trace': 'verbose',\n", "    'workspaceFolders': [{'uri': 'file:///Users/<USER>/works/clacky-ai-agent', 'name': ''}],\n", "}\n", "\n", "\n", "# 发送初始化请求\n", "init_id = send_request(\"initialize\", initialize_params)\n", "init_response = read_response()\n", "print(\"初始化响应:\", init_response)"]}, {"cell_type": "code", "execution_count": 69, "id": "2949f9d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'jsonrpc': '2.0', 'id': 'c28c8920-09de-410a-a310-0b0f6055592e', 'result': [{'uri': '/Users/<USER>/works/clacky-ai-agent/heracles/core/config.py', 'range': {'start': {'line': 9, 'character': 4}, 'end': {'line': 9, 'character': 17}}}]}\n"]}], "source": ["# 示例：获取文档中某个位置的悬停信息\n", "params = {\n", "    'textDocument': {\n", "        'uri': 'heracles/core/config.py'  # 替换为你的文件路径\n", "    },\n", "    'position': {\n", "        'line': 9,  # 行号(从0开始)\n", "        'character': 10,  # 字符位置(从0开始)\n", "    },\n", "}\n", "\n", "hover_id = send_request('textDocument/definition', params)\n", "response = read_response()\n", "print(response)"]}, {"cell_type": "code", "execution_count": 65, "id": "94fa32ef", "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[65], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m read_response()\n", "Cell \u001b[0;32mIn[48], line 46\u001b[0m, in \u001b[0;36mread_response\u001b[0;34m()\u001b[0m\n\u001b[1;32m     44\u001b[0m content_length \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON>one\u001b[39;00m\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m---> 46\u001b[0m     header_line \u001b[38;5;241m=\u001b[39m process\u001b[38;5;241m.\u001b[39mstdout\u001b[38;5;241m.\u001b[39mreadline()\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mstrip()\n\u001b[1;32m     47\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m header_line\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mContent-Length: \u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m     48\u001b[0m         content_length \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(header_line[\u001b[38;5;241m16\u001b[39m:])\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["read_response()"]}, {"cell_type": "code", "execution_count": 21, "id": "383975d0", "metadata": {}, "outputs": [], "source": ["# 完成会话\n", "process.terminate()"]}, {"cell_type": "code", "execution_count": null, "id": "c82fb592", "metadata": {}, "outputs": [], "source": ["from multilspy import LanguageServer\n", "from multilspy.multilspy_config import MultilspyConfig\n", "from multilspy.multilspy_logger import MultilspyLogger\n", "\n", "config = MultilspyConfig.from_dict({\"code_language\": \"python\"}) # Also supports \"python\", \"rust\", \"csharp\", \"typescript\", \"javascript\", \"go\", \"dart\", \"ruby\"\n", "logger = MultilspyLogger()\n", "lsp = LanguageServer.create(config, logger, \"/Users/<USER>/works/clacky-ai-agent/\")\n", "async with lsp.start_server():\n", "    result = await lsp.request_definition(\n", "        \"heracles/core/config.py\", # Filename of location where request is being made\n", "        9, # line number of symbol for which request is being made\n", "        10 # column number of symbol for which request is being made\n", "    )\n", "    print(result)"]}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}