{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c6c1bc3d", "metadata": {}, "outputs": [], "source": ["from heracles.core.schema import PaymentAuthenticationContext\n", "from heracles.core.utils.crypt_utils import encrypt_demand_payload, decrypt_demand_payload\n"]}, {"cell_type": "code", "execution_count": 8, "id": "0c8abe88", "metadata": {}, "outputs": [], "source": ["key = \"32-byte-long-secret-key-12345678\"\n", "scene = 'DEMAND_TYPE'\n", "demand_scale = 'micro'\n"]}, {"cell_type": "code", "execution_count": 9, "id": "57abafac", "metadata": {}, "outputs": [], "source": ["import base64\n", "key_b64 = base64.b64encode(key.encode('utf-8')).decode('utf-8')"]}, {"cell_type": "code", "execution_count": 10, "id": "16374d41", "metadata": {}, "outputs": [{"data": {"text/plain": ["'MzItYnl0ZS1sb25nLXNlY3JldC1rZXktMTIzNDU2Nzg='"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["key_b64"]}, {"cell_type": "code", "execution_count": 5, "id": "16e2a2c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'd1ec3f6a-8d03-4d21-bbe6-4d8d6f06d24f|DEMAND_TYPE|micro|TY2Y8yKkMRbPbkGJ|1744547015110'\n"]}], "source": ["res = encrypt_demand_payload(key=key_b64,scene=scene,demand_scale=demand_scale)"]}, {"cell_type": "code", "execution_count": 6, "id": "6e6299ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'demandId': 'd1ec3f6a-8d03-4d21-bbe6-4d8d6f06d24f',\n", " 'scene': 'DEMAND_TYPE',\n", " 'demandScale': 'micro',\n", " 'signData': 'PKBTZ6CqyXs7p/v4WldxO6yWNwNnPRKUe7yTyOh66rIFlh5WVUGMgt5HpHP6oOl5HSihJIeRURi9vdXyJEd3NTspB19gxAfj/cc73+ZpmCDo8gNa9Q==',\n", " 'nonce': 'TY2Y8yKkMRbPbkGJ',\n", " 'tag': 'rQ7eIwzlFSo+pzntHSbbLw==',\n", " 'timestamp': 1744547015110}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["res.dict()"]}, {"cell_type": "code", "execution_count": 7, "id": "ecce1b06", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'demandId': 'd1ec3f6a-8d03-4d21-bbe6-4d8d6f06d24f',\n", " 'scene': 'DEMAND_TYPE',\n", " 'demandScale': 'micro',\n", " 'nonce': 'TY2Y8yKkMRbPbkGJ',\n", " 'timestamp': 1744547015110}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["decrypt_demand_payload(key_b64, res)"]}, {"cell_type": "code", "execution_count": null, "id": "12149e1a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}