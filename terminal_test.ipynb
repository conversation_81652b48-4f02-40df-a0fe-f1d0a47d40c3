{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["values = [' M .1024',\n", "'\\r\\n?? server/package-lock.json',\n", "'\\r\\n',\n", "'\\x1b[?2004h',\n", "'\\x1b[0;32m➜ \\x1b[00mserver\\x1b[32m (chore/init-clacky-env)\\x1b[00m $ ']"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[' M .1024',\n", " '\\r\\n?? server/package-lock.json',\n", " '\\r\\n',\n", " '\\x1b[?2004h',\n", " '\\x1b[0;32m➜ \\x1b[00mserver\\x1b[32m (chore/init-clacky-env)\\x1b[00m $ ']"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["values"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["import re\n", "from heracles.core.utils.string import camelize, remove_ansi_escape_sequences\n", "\n", "\n", "def _deal_terminal_result_iterator(values):  # pragma: no cover\n", "    \"\"\"内部使用: 处理终端返回结果\"\"\"\n", "    parts = re.split(r'\\r\\n|\\n', values)\n", "    # parts = values.splitlines()\n", "    for i, line in enumerate(parts):\n", "        is_last = (i == len(parts) - 1)\n", "        if not is_last or values.endswith('\\r\\n') or values.endswith('\\n'):\n", "            origin_line = line + '\\n'\n", "        else:\n", "            origin_line = line\n", "        parsed_line: list[str] = []\n", "        skip_next = False\n", "        if not line:  # 空字符跳过\n", "            continue\n", "        real_line = remove_ansi_escape_sequences(line)\n", "        for i, char in enumerate(real_line):\n", "            if skip_next:\n", "                skip_next = False\n", "                continue\n", "            if i != 0 and char == '\\r':\n", "                # 跳过非行首的 \\r 后面的一个字符\n", "                skip_next = True\n", "            else:\n", "                parsed_line.append(char)\n", "        value = ''.join(parsed_line)\n", "        real_value = remove_ansi_escape_sequences(value)\n", "        if real_value in ['\\r', '']:\n", "            # 跳过已经空掉的字符\n", "            continue\n", "        yield real_value, origin_line"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'values' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[60], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m v \u001b[38;5;129;01min\u001b[39;00m values:\n\u001b[1;32m      2\u001b[0m     \u001b[38;5;66;03m# print(repr(v))\u001b[39;00m\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m _deal_terminal_result_iterator(v):\n\u001b[1;32m      4\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28mrepr\u001b[39m(res[\u001b[38;5;241m0\u001b[39m]), \u001b[38;5;28mrepr\u001b[39m(res[\u001b[38;5;241m1\u001b[39m]))\n", "\u001b[0;31mNameError\u001b[0m: name 'values' is not defined"]}], "source": ["for v in values:\n", "    # print(repr(v))\n", "    for res in _deal_terminal_result_iterator(v):\n", "        print(repr(res[0]), repr(res[1]))"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["'a' 'a\\rb'\n"]}], "source": ["for res in _deal_terminal_result_iterator('a\\rb'):\n", "    print(repr(res[0]), repr(res[1]))"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b\n"]}], "source": ["print('a\\rb')"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" M .1024\n", "?? server/package-lock.json\n", "\u001b[?2004h\u001b[0;32m➜ \u001b[00mserver\u001b[32m (chore/init-clacky-env)\u001b[00m $ \n"]}], "source": ["import io\n", "string_io = io.StringIO()\n", "\n", "for value in values:\n", "    for ch in value:\n", "        if ch == '\\n':\n", "            print(str(string_io.getvalue()))\n", "            string_io.truncate(0)\n", "            string_io.seek(0)\n", "            # print(string_io.getvalue())\n", "        else:\n", "            string_io.write(ch)\n", "if string_io.tell():\n", "    print(True)\n", "    string_io.write('\\n')\n", "    print(str(string_io.getvalue()))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["s='[0;31m➜ [00mapp[32m (feature/update-prompts-to-english)[00m $'"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0;31m➜ [00mapp[32m (feature/update-prompts-to-english)[00m $\n"]}], "source": ["print(s)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Python-dotenv could not parse statement starting at line 13\n"]}, {"data": {"text/plain": ["'[0;31m➜ [00mapp[32m (feature/update-prompts-to-english)[00m $ '"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from heracles.core.utils.string import camelize, remove_ansi_escape_sequences\n", "\n", "remove_ansi_escape_sequences('[0;31m➜ [00mapp[32m (feature/update-prompts-to-english)[00m $ ')"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "ABCfasdfas\n"]}], "source": ["import io\n", "sio = io.StringIO()\n", "sio.write('asdfasdfas\\rABC')\n", "print(str(sio.getvalue()) == sio.getvalue())\n", "sio.flush()\n", "print(sio.getvalue())"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"'asdfasdfas\\\\rABC'\""]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["repr(str(sio.getvalue()))\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["'asdfasdfas\\rABC'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["(sio.getvalue())"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["'你是否的\\rABC\\n'"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["output = io.StringIO()\n", "print('你是否的\\rABC', file=output)\n", "output.getvalue()"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["['', '你是否的ABC', '']"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["'\\n你是否的ABC\\n\\n'.splitlines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}