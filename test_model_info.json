{"data": [{"model_name": "text-embedding-3-small", "litellm_params": {"api_base": "https://clacky.openai.azure.com/", "api_version": "2023-05-15", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "azure/text-embedding-3-small"}, "model_info": {"id": "f07b2b3045d7138c3e1418eae7131f675533ba964239168b15296378b8f48df1", "db_model": false, "mode": "embedding", "key": "azure/text-embedding-3-small", "max_tokens": 8191, "max_input_tokens": 8191, "max_output_tokens": null, "input_cost_per_token": 2e-08, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 0, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "azure", "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": false, "supports_tool_choice": false, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["temperature", "n", "stream", "stream_options", "stop", "max_tokens", "max_completion_tokens", "tools", "tool_choice", "presence_penalty", "frequency_penalty", "logit_bias", "user", "function_call", "functions", "tools", "tool_choice", "top_p", "logprobs", "top_logprobs", "response_format", "seed", "extra_headers", "parallel_tool_calls", "prediction", "modalities", "audio"]}}, {"model_name": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:1", "litellm_params": {"aws_region_name": "us-west-2", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0", "thinking": {"type": "enabled", "budget_tokens": 4096}}, "model_info": {"id": "f3175849b5069a4d12e27291aa127aa4f2f2ed17b641268f1b631efe433233bd", "db_model": false, "supports_vision": true, "key": "anthropic.claude-3-7-sonnet-20250219-v1:0", "max_tokens": 8192, "max_input_tokens": 200000, "max_output_tokens": 8192, "input_cost_per_token": 3e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 1.5e-05, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "bedrock_converse", "mode": "chat", "supports_system_messages": null, "supports_response_schema": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": true, "supports_prompt_caching": true, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["max_tokens", "max_completion_tokens", "stream", "stream_options", "stop", "temperature", "top_p", "extra_headers", "response_format", "tools", "thinking", "reasoning_effort"]}}, {"model_name": "gemini-2.5-flash-preview", "litellm_params": {"vertex_project": "clacky-456208", "vertex_location": "us-central1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "vertex_ai/gemini-2.5-flash-preview-04-17"}, "model_info": {"id": "eef4e364e9576680436e7c8e6a4777431bb00ed6ef1025215e818686c6dd5381", "db_model": false, "key": "gemini-2.5-flash-preview-04-17", "max_tokens": 65536, "max_input_tokens": 1048576, "max_output_tokens": 65536, "input_cost_per_token": 1.5e-07, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": 1e-06, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 6e-07, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": 3.5e-06, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_response_schema": true, "supports_vision": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["temperature", "top_p", "max_tokens", "max_completion_tokens", "stream", "tools", "functions", "tool_choice", "response_format", "n", "stop", "frequency_penalty", "presence_penalty", "extra_headers", "seed", "logprobs", "top_logprobs", "modalities", "reasoning_effort", "thinking"]}}, {"model_name": "deepseek-v3", "litellm_params": {"api_base": "https://openrouter.ai/api/v1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "openrouter/deepseek/deepseek-chat-v3-0324"}, "model_info": {"id": "f9fa0f6b861e5fe1436fc66d7f99af2d0a35137ed215fbd9df9480bbc4c5b3bd", "db_model": false, "key": "openrouter/deepseek/deepseek-chat-v3-0324", "max_tokens": null, "max_input_tokens": null, "max_output_tokens": null, "input_cost_per_token": 0, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 0, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "openrouter", "mode": null, "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": false, "supports_tool_choice": false, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["frequency_penalty", "logit_bias", "logprobs", "top_logprobs", "max_tokens", "max_completion_tokens", "modalities", "prediction", "n", "presence_penalty", "seed", "stop", "stream", "stream_options", "temperature", "top_p", "tools", "tool_choice", "function_call", "functions", "max_retries", "extra_headers", "parallel_tool_calls", "audio", "response_format"]}}, {"model_name": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:2", "litellm_params": {"aws_region_name": "us-west-2", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0", "thinking": {"type": "enabled", "budget_tokens": 1024}}, "model_info": {"id": "cca597b1fa640e81c8657d69b132396aac7a272e62856ef511abfe7d98e4fa11", "db_model": false, "supports_vision": true, "key": "anthropic.claude-3-7-sonnet-20250219-v1:0", "max_tokens": 8192, "max_input_tokens": 200000, "max_output_tokens": 8192, "input_cost_per_token": 3e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 1.5e-05, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "bedrock_converse", "mode": "chat", "supports_system_messages": null, "supports_response_schema": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": true, "supports_prompt_caching": true, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["max_tokens", "max_completion_tokens", "stream", "stream_options", "stop", "temperature", "top_p", "extra_headers", "response_format", "tools", "thinking", "reasoning_effort"]}}, {"model_name": "claude-3.7-sonnet-think", "litellm_params": {"api_base": "https://openrouter.ai/api/v1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "openrouter/anthropic/claude-3.7-sonnet:thinking"}, "model_info": {"id": "e694cfa752fea0b910db41910f710ca7c30d39302d702ac46365f580117c7221", "db_model": false, "key": "openrouter/anthropic/claude-3.7-sonnet:thinking", "max_tokens": null, "max_input_tokens": null, "max_output_tokens": null, "input_cost_per_token": 0, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 0, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "openrouter", "mode": null, "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": false, "supports_tool_choice": false, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["frequency_penalty", "logit_bias", "logprobs", "top_logprobs", "max_tokens", "max_completion_tokens", "modalities", "prediction", "n", "presence_penalty", "seed", "stop", "stream", "stream_options", "temperature", "top_p", "tools", "tool_choice", "function_call", "functions", "max_retries", "extra_headers", "parallel_tool_calls", "audio", "response_format"]}}, {"model_name": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0", "litellm_params": {"aws_region_name": "us-west-2", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0"}, "model_info": {"id": "2fd74bdb01c9bce40197145af714bbd1bf9ef031a5065f2bedd331370991a2a6", "db_model": false, "supports_vision": true, "key": "anthropic.claude-3-7-sonnet-20250219-v1:0", "max_tokens": 8192, "max_input_tokens": 200000, "max_output_tokens": 8192, "input_cost_per_token": 3e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 1.5e-05, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "bedrock_converse", "mode": "chat", "supports_system_messages": null, "supports_response_schema": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": true, "supports_prompt_caching": true, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["max_tokens", "max_completion_tokens", "stream", "stream_options", "stop", "temperature", "top_p", "extra_headers", "response_format", "tools", "thinking", "reasoning_effort"]}}, {"model_name": "bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0", "litellm_params": {"aws_region_name": "us-west-2", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0"}, "model_info": {"id": "0156d882b084623b2a4ececf629894d1a7a14a3d856808c614d38fb87188534d", "db_model": false, "key": "anthropic.claude-3-5-sonnet-20241022-v2:0", "max_tokens": 8192, "max_input_tokens": 200000, "max_output_tokens": 8192, "input_cost_per_token": 3e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 1.5e-05, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "bedrock", "mode": "chat", "supports_system_messages": null, "supports_response_schema": true, "supports_vision": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": true, "supports_prompt_caching": true, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": true, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["max_tokens", "max_completion_tokens", "stream", "stream_options", "stop", "temperature", "top_p", "extra_headers", "response_format", "tools"]}}, {"model_name": "gemini-2.0-flash-001", "litellm_params": {"vertex_project": "clacky-456208", "vertex_location": "us-central1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "vertex_ai/gemini-2.0-flash-001"}, "model_info": {"id": "19c3cd0ef4b83a42874a515edea99fd40327e319ef911b485cbb5d29d2b64e21", "db_model": false, "key": "gemini-2.0-flash-001", "max_tokens": 8192, "max_input_tokens": 1048576, "max_output_tokens": 8192, "input_cost_per_token": 1.5e-07, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": 1e-06, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 6e-07, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_response_schema": true, "supports_vision": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": true, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["temperature", "top_p", "max_tokens", "max_completion_tokens", "stream", "tools", "functions", "tool_choice", "response_format", "n", "stop", "frequency_penalty", "presence_penalty", "extra_headers", "seed", "logprobs", "top_logprobs", "modalities"]}}, {"model_name": "gemini-2.5-pro-preview", "litellm_params": {"vertex_project": "clacky-456208", "vertex_location": "us-central1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "vertex_ai/gemini-2.5-pro-preview-03-25"}, "model_info": {"id": "f4ea4a73c8f2eabf8301664a0468925fc1afccd798537a82575558071c74bc45", "db_model": false, "key": "gemini-2.5-pro-preview-03-25", "max_tokens": 65536, "max_input_tokens": 1048576, "max_output_tokens": 65536, "input_cost_per_token": 1.25e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": 2.5e-06, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": 1.25e-06, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 1e-05, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": 1.5e-05, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_response_schema": true, "supports_vision": true, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["temperature", "top_p", "max_tokens", "max_completion_tokens", "stream", "tools", "functions", "tool_choice", "response_format", "n", "stop", "frequency_penalty", "presence_penalty", "extra_headers", "seed", "logprobs", "top_logprobs", "modalities", "reasoning_effort", "thinking"]}}, {"model_name": "deepseek-r1", "litellm_params": {"api_base": "https://openrouter.ai/api/v1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "openrouter/deepseek/deepseek-r1"}, "model_info": {"id": "e4f9c3c3361ec0daf1504c46d6677a4a9ef7cadaa9db9e62cd8201ea7750cd60", "db_model": false, "key": "openrouter/deepseek/deepseek-r1", "max_tokens": 8192, "max_input_tokens": 65336, "max_output_tokens": 8192, "input_cost_per_token": 5.5e-07, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 2.19e-06, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "openrouter", "mode": "chat", "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": true, "supports_prompt_caching": true, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["frequency_penalty", "logit_bias", "logprobs", "top_logprobs", "max_tokens", "max_completion_tokens", "modalities", "prediction", "n", "presence_penalty", "seed", "stop", "stream", "stream_options", "temperature", "top_p", "tools", "tool_choice", "function_call", "functions", "max_retries", "extra_headers", "parallel_tool_calls", "audio", "response_format"]}}, {"model_name": "gemini-2.0-flash-thinking-exp", "litellm_params": {"vertex_project": "clacky-456208", "vertex_location": "us-central1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "vertex_ai/gemini-2.0-flash-thinking-exp-01-21"}, "model_info": {"id": "0ae0535582113d5c7ae2079afaddb0d3151e42263efb0204d67fc18228459f49", "db_model": false, "key": "gemini-2.0-flash-thinking-exp-01-21", "max_tokens": 65536, "max_input_tokens": 1048576, "max_output_tokens": 65536, "input_cost_per_token": 0, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": 0, "input_cost_per_token_above_128k_tokens": 0, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 0, "output_cost_per_audio_token": null, "output_cost_per_character": 0, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": 0, "output_cost_per_character_above_128k_tokens": 0, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "vertex_ai-language-models", "mode": "chat", "supports_system_messages": true, "supports_response_schema": false, "supports_vision": true, "supports_function_calling": false, "supports_tool_choice": true, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["temperature", "top_p", "max_tokens", "max_completion_tokens", "stream", "tools", "functions", "tool_choice", "response_format", "n", "stop", "frequency_penalty", "presence_penalty", "extra_headers", "seed", "logprobs", "top_logprobs", "modalities"]}}, {"model_name": "gemini-2.5-pro-exp", "litellm_params": {"api_base": "https://openrouter.ai/api/v1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "openrouter/google/gemini-2.5-pro-exp-03-25:free"}, "model_info": {"id": "0ad2ce65f2e2ded4ad422b1d894a7efbf89772c9c66370f5e2639e1997c1e731", "db_model": false, "key": "openrouter/google/gemini-2.5-pro-exp-03-25:free", "max_tokens": null, "max_input_tokens": null, "max_output_tokens": null, "input_cost_per_token": 0, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 0, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "openrouter", "mode": null, "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": false, "supports_tool_choice": false, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": false, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["frequency_penalty", "logit_bias", "logprobs", "top_logprobs", "max_tokens", "max_completion_tokens", "modalities", "prediction", "n", "presence_penalty", "seed", "stop", "stream", "stream_options", "temperature", "top_p", "tools", "tool_choice", "function_call", "functions", "max_retries", "extra_headers", "parallel_tool_calls", "audio", "response_format"]}}, {"model_name": "o3-mini", "litellm_params": {"api_base": "https://openrouter.ai/api/v1", "use_in_pass_through": false, "merge_reasoning_content_in_choices": false, "model": "openrouter/openai/o3-mini"}, "model_info": {"id": "c14204d167f67e0491d5e3bd71b95dc9c7ec688ecb6063c38e1a98a538bcb378", "db_model": false, "key": "openrouter/openai/o3-mini", "max_tokens": 65536, "max_input_tokens": 128000, "max_output_tokens": 65536, "input_cost_per_token": 1.1e-06, "cache_creation_input_token_cost": null, "cache_read_input_token_cost": null, "input_cost_per_character": null, "input_cost_per_token_above_128k_tokens": null, "input_cost_per_token_above_200k_tokens": null, "input_cost_per_query": null, "input_cost_per_second": null, "input_cost_per_audio_token": null, "input_cost_per_token_batches": null, "output_cost_per_token_batches": null, "output_cost_per_token": 4.4e-06, "output_cost_per_audio_token": null, "output_cost_per_character": null, "output_cost_per_reasoning_token": null, "output_cost_per_token_above_128k_tokens": null, "output_cost_per_character_above_128k_tokens": null, "output_cost_per_token_above_200k_tokens": null, "output_cost_per_second": null, "output_cost_per_image": null, "output_vector_size": null, "litellm_provider": "openrouter", "mode": "chat", "supports_system_messages": null, "supports_response_schema": null, "supports_vision": false, "supports_function_calling": true, "supports_tool_choice": true, "supports_assistant_prefill": false, "supports_prompt_caching": false, "supports_audio_input": false, "supports_audio_output": false, "supports_pdf_input": false, "supports_embedding_image_input": false, "supports_native_streaming": null, "supports_web_search": false, "supports_reasoning": true, "search_context_cost_per_query": null, "tpm": null, "rpm": null, "supported_openai_params": ["frequency_penalty", "logit_bias", "logprobs", "top_logprobs", "max_tokens", "max_completion_tokens", "modalities", "prediction", "n", "presence_penalty", "seed", "stop", "stream", "stream_options", "temperature", "top_p", "tools", "tool_choice", "function_call", "functions", "max_retries", "extra_headers", "parallel_tool_calls", "audio", "response_format"]}}]}