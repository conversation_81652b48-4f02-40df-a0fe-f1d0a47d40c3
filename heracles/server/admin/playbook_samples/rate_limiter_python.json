{"id": "4d7a7a41-3737-46a1-ac9f-7ba07c579188", "title": "How to implement a rate limiter in python", "description": "4 methods to implement a rate limiter in python in plain code", "tags": [], "original_content": "# Implement a rate limiter in python\r\n\r\n## Overview\r\n\r\nAPI rate limiting is a technique used to control the number of requests that a client can make to an API within a specified period. It is an essential aspect of API management, ensuring the stability and availability of services by preventing excessive use that could lead to server overloads or abuse.\r\n\r\n## Common Rate Limiting Strategies\r\nToken Bucket Algorithm:\r\n— This algorithm allows a fixed number of tokens to be issued at a regular interval.\r\n— Each request consumes a token. When tokens are exhausted, requests are denied until more tokens are added.\r\n— It allows for a burst of traffic as long as tokens are available.\r\n\r\nLeaky Bucket Algorithm:\r\n— Requests are processed at a constant rate. Excess requests are queued and handled in order.\r\n— This approach smooths out traffic spikes but can introduce delays for queued requests.\r\n\r\nFixed Window:\r\n— Limits the number of requests in a fixed time window (e.g., 100 requests per minute).\r\n— Simple to implement but can allow bursts at the edge of windows.\r\n\r\nSliding Window:\r\n— Similar to the fixed window but tracks requests in a rolling window to prevent bursts at the edges.\r\n— More complex but provides smoother enforcement.\r\n\r\n## Sample Codes\r\n```\r\nimport time\r\nimport queue\r\n\r\nclass LeakyBucketLimitter:\r\n    def __init__(self) -> None:\r\n        # after reaching max_size, calls limit is limit_rate (unit: second)\r\n        self.max_size = 1\r\n        self.limit_rate = 1\r\n        self.cur_size = 0\r\n        self.last_time = time.time()\r\n\r\n    def can_pass(self):\r\n        self.cur_size = max(0, self.cur_size - (time.time() - self.last_time) * self.limit_rate)\r\n        self.last_time = time.time()\r\n        if self.cur_size + 1 <= self.max_size:\r\n            self.cur_size += 1\r\n            return True\r\n        else:\r\n            return False\r\n        \r\n\r\nclass TokenBucketLimitter:\r\n    def __init__(self) -> None:\r\n        # after reaching max_size, calls limit is limit_rate (unit: second)\r\n        self.max_size = 1\r\n        self.limit_rate = 1\r\n        self.cur_size = 0\r\n        self.last_time = time.time()\r\n\r\n    def can_pass(self):\r\n        self.cur_size = min(self.max_size, self.cur_size + (time.time() - self.last_time) * self.limit_rate)\r\n        self.last_time = time.time()\r\n        if self.cur_size > 0:\r\n            self.cur_size -= 1\r\n            return True\r\n        else:\r\n            return False\r\n        \r\n\r\nclass FixedWindowLimitter:\r\n    def __init__(self) -> None:\r\n        # in window_size, calls limit is limit_rate (unit: second)\r\n        self.window_size = 1\r\n        self.rate = 1\r\n        self.counter = 0\r\n        self.last_time = time.time()\r\n\r\n    def can_pass(self):\r\n        if time.time() - self.last_time > self.window_size:\r\n            self.last_time = time.time()\r\n            self.counter = 1\r\n        else:\r\n            self.counter += 1\r\n        return self.counter <= self.rate\r\n    \r\nclass SlidingWindowLimitter:\r\n    def __init__(self) -> None:\r\n        # in window_size, calls limit is limit_rate (unit: second)\r\n        self.window_size = 1\r\n        self.rate = 1\r\n        self.q = queue.Queue()\r\n\r\n    def can_pass(self):\r\n        cur_time = time.time()\r\n        while not self.q.empty() and (cur_time - self.q.queue[0]) > self.window_size:\r\n            self.q.get()\r\n        if self.q.qsize() < self.rate:\r\n            self.q.put(cur_time)\r\n            return True\r\n        else:\r\n            return False\r\n\r\nAbove classes has method can_pass which return True if calls rate under limit, otherwise return False if exceed limit\r\n```", "source": "user", "status": "ready"}