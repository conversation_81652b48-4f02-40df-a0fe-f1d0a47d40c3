{"id": "b2abfc69-12ad-4e0d-a96d-2a308e05e2c2", "title": "Find Building Where <PERSON> and <PERSON> Can Meet", "description": "Tutorial: Find Building Where <PERSON> and <PERSON>", "tags": [], "original_content": "# Solution: Find Building Where <PERSON> and <PERSON> Can Meet\r\n\r\n## Solution\r\n\r\n## Overview\r\nWe are given an integer array heights and an array of pairs queries, where each pair is of the form [a \r\ni,b i], representing the positions of <PERSON> and <PERSON> at indices i and j, respectively. For each query, the task is to find the closest value to the right in the heights array that is greater than the heights at both the given positions.\r\n\r\nIn other words, given indices i and j, we need to find the first value in the heights array that is greater than the values at heights[i] and heights[j]. If no such value exists, return -1.\r\n\r\n## Approach 1: Monotonic Stack\r\n\r\n### Intuition\r\nLet’s start by breaking down the problem into simpler terms. Suppose queries only contained single integer indices. The goal would then be to find, for each index, the first building to the right in the heights array that is taller than the building at that index. Instead of scanning the array repeatedly for each query, we can preprocess the heights array to store this \"next taller building\" information in advance.\r\n\r\nThe key insight here is that for each building, the next taller building to its right depends only on the heights of the buildings that come after it. Using a monotonic stack, we can compute this efficiently. By traversing the heights array from right to left, we maintain a stack of indices in decreasing order of heights. For the current building, any shorter or equal buildings already in the stack cannot be the answer, so we remove them. If the stack is not empty, the top element gives the position of the next taller building. If the stack is empty, it means no taller building exists to the right, so we store -1. This preprocessing step allows us to handle single queries in constant time. For a better understanding of this idea, you can refer to Next Greater Element - II, which applies a similar technique.\r\n\r\nNow, let’s extend this idea to handle queries that are pairs of values. In this scenario, the task is to find the first height to the right in the heights array that is greater than both values in each pair. Here the key realization is that the answer for a pair depends on the larger of the two values since a building must be taller than both. This simplifies the problem by reducing it to a comparison with a single threshold for each query.\r\n\r\nWhile traversing the heights array, we use a monotonic stack to maintain all elements greater than the current height, with the nearest greater height at the top of the stack. When processing a query, the stack already contains all elements greater than the current height.\r\n\r\nFor each query pair, we use binary search on the stack to quickly find the first element greater than the larger value in the pair. This ensures that each query is processed in O(logn) time.\r\n\r\n### Algorithm\r\n\r\nMain function - leftmostBuildingQueries(heights, queries)\r\n\r\n1 Create a list newQueries where each index stores the list of queries that require this index as the maximum index of the query pair. Each query is stored as a pair containing the required height (heights[a]) and the query index.\r\n2 Initialize a monotonic stack monoStack to keep track of building heights and their indices in decreasing order of height while iterating from right to left in the heights array.\r\n3 Initialize an array result to store the answers for each query, with all elements initially set to -1.\r\n4 Iterate over the queries:\r\n- For each query, extract the two indices a and b.\r\n- If a > b, swap the indices to ensure a <= b.\r\n- If heights[b] > heights[a] or a == b, set result[currQuery] = b.\r\n- Otherwise, add the query to newQueries[b] with its required height (heights[a]) and the query index.\r\n5 Iterate over the indices of the heights array from right to left:\r\n- For each query stored at the current index in newQueries, use binary search on the monoStack to find the first building with a height greater than the query's required height. If such a building exists, set the result for the query to the index of this building.\r\n- Remove all elements from the top of the monoStack where the height is less than or equal to the current height, as they are no longer relevant.\r\n- Push the current height and index onto the monoStack.\r\n6 Return the result array.\r\n\r\nHelper Binary Search function - search(height, monoStack)\r\n\r\n1 Initialize two pointers left = 0 and right = size of monoStack - 1. Set a variable ans = -1 to store the search result.\r\n2 Perform a binary search:\r\n- Calculate mid = (left + right) / 2.\r\n- If the height at monoStack[mid] is greater than the required height:\r\n\t- Update ans = max(ans, mid) and set left = mid + 1.\r\n3 Otherwise, set right = mid - 1.\r\nReturn ans, which will be the index of the first building with a height greater than the required height. If no such building exists, ans remains -1.\r\n\r\n### Implementation\r\n\r\n```\r\nclass Solution:\r\n    def leftmostBuildingQueries(self, heights, queries):\r\n        mono_stack = []\r\n        result = [-1 for _ in range(len(queries))]\r\n        new_queries = [[] for _ in range(len(heights))]\r\n        for i in range(len(queries)):\r\n            a = queries[i][0]\r\n            b = queries[i][1]\r\n            if a > b:\r\n                a, b = b, a\r\n            if heights[b] > heights[a] or a == b:\r\n                result[i] = b\r\n            else:\r\n                new_queries[b].append((heights[a], i))\r\n\r\n        for i in range(len(heights) - 1, -1, -1):\r\n            mono_stack_size = len(mono_stack)\r\n            for a, b in new_queries[i]:\r\n                position = self.search(a, mono_stack)\r\n                if position < mono_stack_size and position >= 0:\r\n                    result[b] = mono_stack[position][1]\r\n            while mono_stack and mono_stack[-1][0] <= heights[i]:\r\n                mono_stack.pop()\r\n            mono_stack.append((heights[i], i))\r\n        return result\r\n\r\n    def search(self, height, mono_stack):\r\n        left = 0\r\n        right = len(mono_stack) - 1\r\n        ans = -1\r\n        while left <= right:\r\n            mid = (left + right) // 2\r\n            if mono_stack[mid][0] > height:\r\n                ans = max(ans, mid)\r\n                left = mid + 1\r\n            else:\r\n                right = mid - 1\r\n        return ans\r\n```\r\n\r\n### Complexity Analysis\r\n\r\nLet n be the size of the array heights and q be the number of queries in the queries array.\r\n\r\nTime Complexity: O(q⋅logn+n)\r\n\r\nThe algorithm processes each query using binary search on the monotonic stack, which takes O(logn) per query. With q queries, the total query processing time is O(q⋅logn). Apart from this, we also iterate through the heights and queries arrays, that takes O(n) and O(q) time, respectively.\r\nTherefore, the overall time complexity is O(q⋅logn+n).\r\n\r\nSpace Complexity: O(n+q)\r\n\r\nThe algorithm uses a monotonic stack to store building indices, requiring O(n) space. It also stores queries in the newQueries array and results in the result array, each taking O(q) space.\r\nTherefore, the total space complexity is O(n+q).\r\n\r\n\r\n## Approach 2: Priority Queue\r\n\r\n### Intuition\r\n\r\nIn the previous approach, we calculated the answer using a monotonic stack. Each query asks for the closest index to the right with a value greater than both elements in the query pair. Instead of processing each query one at a time, we can optimize by checking, for each index in the heights array, if it can serve as the answer for any query.\r\n\r\nTo do this efficiently, we can iterate through the heights array from left to right. For each index, we look for query pairs where both indices are smaller than the current index, and both values in the pair are smaller than the value at the current index. To make this process faster, we prioritize assigning answers to the smallest query pairs first.\r\n\r\nBy maintaining the query pairs sorted based on their maximum value and index up to the current position, we can process them more efficiently.\r\n\r\nTo implement this idea, we process the heights array while managing the queries by storing them in a 2D array of arrays, where each subarray holds the queries for the corresponding building.\r\n\r\nWe begin by sorting and mapping the queries to track the index and values that we need. Using a priority queue, we store queries based on their maximum value and index. This helps us quickly retrieve the smallest index for processing.\r\n\r\nAs we move through the heights array, we pop the queries from the queue. For each query, if the current index is greater than both indices of the query, we assign the current index as the answer and store it. We also check if new queries, whose maximum index matches the current one, should be added to the queue for future processing.\r\n\r\nThis allows us to handle queries without reprocessing them repeatedly.\r\n\r\n### Algorithm\r\n\r\n- Initialize storeQueries as a 2D array of arrays to store queries for each building.\r\n\r\n- Initialize maxIndex as a priority queue to track the queries that need to be answered based on building heights.\r\n\r\n- Initialize result as an array of -1 to store the answers for each query.\r\n\r\n- Loop through each query:\r\n\r\n\t- For each query (a, b):\r\n\t\t- If the height of building a is less than building b and a is smaller than b, set result[currQuery] to b (building b is the answer).\r\n\t\t- If the height of building a is greater than building b and a is greater than b, set result[currQuery] to a (building a is the answer).\r\n\t\t- If a is equal to b, set result[currQuery] to a (both are the same building).\r\n\t\t- Otherwise, store the query in storeQueries[max(a, b)] for future processing.\r\n- Loop through each building index index:\r\n\r\n\t- While the priority queue maxIndex has elements and the minimum value in maxIndex is smaller than the current building height:\r\n\t\t- Set the corresponding query's result in result and pop the element from maxIndex (this query is answered).\r\n\t- Push new queries from storeQueries[index] into maxIndex, sorting them by height.\r\n- Return the result array containing the answers to all queries.\r\n\r\n### Implementation\r\n\r\n```\r\nclass Solution:\r\n    def leftmostBuildingQueries(self, heights, queries):\r\n        max_idx = []  # Min-heap to simulate priority queue\r\n        results = [-1] * len(queries)\r\n        store_queries = [[] for _ in heights]\r\n\r\n        # Store the mappings for all queries in store_queries.\r\n        for idx, query in enumerate(queries):\r\n            a, b = query\r\n            if a < b and heights[a] < heights[b]:\r\n                results[idx] = b\r\n            elif a > b and heights[a] > heights[b]:\r\n                results[idx] = a\r\n            elif a == b:\r\n                results[idx] = a\r\n            else:\r\n                store_queries[max(a, b)].append(\r\n                    (max(heights[a], heights[b]), idx)\r\n                )\r\n\r\n        for idx, height in enumerate(heights):\r\n            # If the heap's smallest value is less than the current height, it is an answer to the query.\r\n            while max_idx and max_idx[0][0] < height:\r\n                _, q_idx = heapq.heappop(max_idx)\r\n                results[q_idx] = idx\r\n            # Push the queries with their maximum index as the current index into the heap.\r\n            for element in store_queries[idx]:\r\n                heapq.heappush(max_idx, element)\r\n\r\n        return results\r\n```\r\n\r\n### Complexity Analysis\r\n\r\nLet n be the size of the array heights and q be the number of queries in the queries array.\r\n\r\nTime Complexity: O(q⋅logq+n)\r\n\r\nThe algorithm first iterates over the queries array to map the maximum indices and heights in storeQueries, taking O(q) time. It then processes each index in the heights array, updating results via a priority queue. Insertion and deletion operations in the priority queue take O(logq) each, with at most q queries processed. For each index, the algorithm checks and pushes relevant queries from storeQueries, resulting in an overall O(n) time for all iterations.\r\n\r\nThus, the overall time complexity is O(q⋅logq+n).\r\n\r\nSpace Complexity: O(n+q)\r\n\r\nThe algorithm uses a array storeQueries to store query mappings, which requires O(n) space, as each element corresponds to an index in heights. Additionally, a priority queue maxIndex is used to handle queries, which at most can store O(q) elements. The result array also requires O(q) space to store the answers.\r\n\r\nTherefore, the total space complexity is O(n+q).", "source": "user", "status": "ready"}