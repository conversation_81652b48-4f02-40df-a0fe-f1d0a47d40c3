{"id": "59c8f16f-0438-4a02-9e47-e70797c70426", "title": "Error : a bytes-like object is required, not 'MultiValue'", "description": "Error : a bytes-like object is required, not 'MultiValue' Hello,  I am getting following error while updating the tag LongTrianglePointIndexList (0066,0040), **TypeError: a bytes-like object is required, not 'MultiValue'**  I noticed that the error  gets produced only when the VR is given as \"OL\" , works fine with \"OB\", \"OF\" etc.  sample code (assume 'lineSeq' is the dicom dataset sequence): ```python import pydicom import array data=list(range(1,10)) data=array.array('H', indexData).tostring()  # to convert to unsigned short lineSeq.add_new(0x00660040, 'OL', data)    ds.save_as(\"mydicom\") ``` outcome: **TypeError: a bytes-like object is required, not 'MultiValue'**  using version - *******  Any help is appreciated.  Thank you", "tags": [], "original_content": "## Overview\r\nFix TypeError: a bytes-like object is required, not 'MultiValue'\r\nThis update addresses a TypeError encountered when updating a DICOM tag with the Value Representation (VR) of \"OL\" (Other Long). The solution ensures that backslash characters in byte and certain string VRs are properly handled during value assignment in DICOM datasets.\r\n\r\n## Procedure\r\n1. Identify VR Exclusions for Backslash Characters:\r\n   - Define a list of VRs where backslash characters should be ignored during value assignment. This includes 'LT', 'OB', 'OD', 'OF', 'OL', 'OV', 'OW', 'ST', 'UN', 'UT', and binary representations like 'OB/OW', 'OW/OB'.\r\n\r\n2. Update Value Assignment Logic in dataelem.py:\r\n   - Modify the value assignment logic to exclude the defined VRs from splitting values on backslashes. This prevents misinterpretation of binary data as multi-value strings for these VR types.\r\n\r\n3. Modify Value Setter Method:\r\n   - In `dataelem.py`, update the `value` setter method to incorporate the VR exclusions list. Ensure that the logic only splits strings on backslash characters for VRs not included in the exclusions.\r\n\r\nBy following these steps, the error related to byte-like objects during DICOM tag value assignment can be resolved, allowing the dataset to accommodate the \"OL\" VR properly.\r\n\r\n## Git Diff Messages\r\n```\r\ndiff --git a/pydicom/dataelem.py b/pydicom/dataelem.py\r\n--- a/pydicom/dataelem.py\r\n+++ b/pydicom/dataelem.py\r\n@@ -433,13 +433,24 @@ def value(self) -> Any:\r\n     @value.setter\r\n     def value(self, val: Any) -> None:\r\n         \"\"\"Convert (if necessary) and set the value of the element.\"\"\"\r\n+        # Ignore backslash characters in these VRs, based on:\r\n+        # * Which str VRs can have backslashes in Part 5, Section 6.2\r\n+        # * All byte VRs\r\n+        exclusions = [\r\n+            'LT', 'OB', 'OD', 'OF', 'OL', 'OV', 'OW', 'ST', 'UN', 'UT',\r\n+            'OB/OW', 'OW/OB', 'OB or OW', 'OW or OB',\r\n+            # Probably not needed\r\n+            'AT', 'FD', 'FL', 'SQ', 'SS', 'SL', 'UL',\r\n+        ]\r\n+\r\n         # Check if is a string with multiple values separated by '\\'\r\n         # If so, turn them into a list of separate strings\r\n         #  Last condition covers 'US or SS' etc\r\n-        if isinstance(val, (str, bytes)) and self.VR not in \\\r\n-                ['UT', 'ST', 'LT', 'FL', 'FD', 'AT', 'OB', 'OW', 'OF', 'SL',\r\n-                 'SQ', 'SS', 'UL', 'OB/OW', 'OW/OB', 'OB or OW',\r\n-                 'OW or OB', 'UN'] and 'US' not in self.VR:\r\n+        if (\r\n+            isinstance(val, (str, bytes))\r\n+            and self.VR not in exclusions\r\n+            and 'US' not in self.VR\r\n+        ):\r\n             try:\r\n                 if _backslash_str in val:\r\n                     val = cast(str, val).split(_backslash_str)\r\n```", "source": "user", "status": "ready"}