{"id": "212714c8-f707-441f-b227-603b0478b0af", "title": "Empty data elements with value representation SQ are set to None", "description": "Empty data elements with value representation SQ are set to None **Describe the bug** In the current `master`, empty data elements are not read correctly from files. The attribute value is set to `None` instead of `[]`.  **Expected behavior** Create empty list `[]` for empty sequence, i.e., a sequence with zero items.  **Steps To Reproduce** ```python import pydicom ds = pydicom.Dataset() ds.AcquisitionContextSequence = [] print(ds) ds.is_little_endian = True ds.is_implicit_VR = True ds.save_as('/tmp/test.dcm')  reloaded_ds = pydicom.dcmread('/tmp/test.dcm', force=True) print(reloaded_ds) ``` This prints: ``` (0040, 0555)  Acquisition Context Sequence   0 item(s) ---- ... TypeError: With tag (0040, 0555) got exception: object of type 'NoneType' has no len() Traceback (most recent call last):   File \"/private/tmp/pydicom/pydicom/tag.py\", line 30, in tag_in_exception     yield   File \"/private/tmp/pydicom/pydicom/dataset.py\", line 1599, in _pretty_str     len(data_element.value))) TypeError: object of type 'NoneType' has no len() ```  **Your environment** ``` Darwin-18.6.0-x86_64-i386-64bit Python  3.7.3 (default, Mar 27 2019, 09:23:15) [Clang 10.0.1 (clang-1001.0.46.3)] pydicom  1.4.0.dev0 ```", "tags": [], "original_content": "## Overview\r\nFix handling of empty data elements for VR 'SQ' in pydicom\r\nThis update addresses the issue where empty data elements with a value representation (VR) of 'SQ' in DICOM files were incorrectly set to `None` instead of an empty list `[]` upon reading. The solution modifies the behavior to ensure empty sequences are correctly represented as empty lists.\r\n\r\n## Procedure\r\n1. Update configuration explanation in config.py:\r\n   - Modify the documentation for `use_none_as_empty_text_VR_value` in `pydicom/config.py` to clarify behavior for various VRs, emphasizing that VR 'SQ' should always result in an empty list.\r\n\r\n2. Adjust the empty value logic in dataelem.py:\r\n   - In `pydicom/dataelem.py`, update the function `empty_value_for_VR` to return `[]` specifically for VR 'SQ', ensuring that empty sequences are represented correctly.\r\n\r\n3. Retain existing settings for other VRs:\r\n   - Ensure that the function `empty_value_for_VR` continues to respect the current setting of `config.use_none_as_empty_text_VR_value` for text VRs, and default behavior for other VRs remains unchanged, except for the specific handling of VR 'SQ'.\r\n\r\n## Git Diff Messages\r\n```\r\ndiff --git a/pydicom/config.py b/pydicom/config.py\r\n--- a/pydicom/config.py\r\n+++ b/pydicom/config.py\r\n@@ -87,9 +87,10 @@ def DS_decimal(use_Decimal_boolean=True):\r\n \"\"\"\r\n \r\n use_none_as_empty_text_VR_value = False\r\n-\"\"\" If ``True``, the value of decoded empty data element is always ``None``.\r\n-If ``False`` (the default), the value of an empty data element with\r\n-a text VR is an empty string, for all other VRs it is also ``None``.\r\n+\"\"\" If ``True``, the value of a decoded empty data element with\r\n+a text VR is ``None``, otherwise (the default), it is is an empty string.\r\n+For all other VRs the behavior does not change - the value is en empty\r\n+list for VR 'SQ' and ``None`` for all other VRs.\r\n Note that the default of this value will change to ``True`` in version 2.0.\r\n \"\"\"\r\n \r\ndiff --git a/pydicom/dataelem.py b/pydicom/dataelem.py\r\n--- a/pydicom/dataelem.py\r\n+++ b/pydicom/dataelem.py\r\n@@ -48,10 +48,12 @@ def empty_value_for_VR(VR, raw=False):\r\n \r\n     The behavior of this property depends on the setting of\r\n     :attr:`config.use_none_as_empty_value`. If that is set to ``True``,\r\n-    an empty value is always represented by ``None``, otherwise it depends\r\n-    on `VR`. For text VRs (this includes 'AE', 'AS', 'CS', 'DA', 'DT', 'LO',\r\n-    'LT', 'PN', 'SH', 'ST', 'TM', 'UC', 'UI', 'UR' and 'UT') an empty string\r\n-    is used as empty value representation, for all other VRs, ``None``.\r\n+    an empty value is represented by ``None`` (except for VR 'SQ'), otherwise\r\n+    it depends on `VR`. For text VRs (this includes 'AE', 'AS', 'CS', 'DA',\r\n+    'DT', 'LO', 'LT', 'PN', 'SH', 'ST', 'TM', 'UC', 'UI', 'UR' and 'UT') an\r\n+    empty string is used as empty value representation, for all other VRs\r\n+    except 'SQ', ``None``. For empty sequence values (VR 'SQ') an empty list\r\n+    is used in all cases.\r\n     Note that this is used only if decoding the element - it is always\r\n     possible to set the value to another empty value representation,\r\n     which will be preserved during the element object lifetime.\r\n@@ -67,10 +69,12 @@ def empty_value_for_VR(VR, raw=False):\r\n \r\n     Returns\r\n     -------\r\n-    str or bytes or None\r\n+    str or bytes or None or list\r\n         The value a data element with `VR` is assigned on decoding\r\n         if it is empty.\r\n     \"\"\"\r\n+    if VR == 'SQ':\r\n+        return []\r\n     if config.use_none_as_empty_text_VR_value:\r\n         return None\r\n     if VR in ('AE', 'AS', 'CS', 'DA', 'DT', 'LO', 'LT',\r\n```", "source": "user", "status": "ready"}