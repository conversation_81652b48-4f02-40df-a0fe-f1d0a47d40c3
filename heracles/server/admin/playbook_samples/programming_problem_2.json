{"id": "3de75ba9-3219-479a-872b-95b0673ec61c", "title": "Find Minimum Diameter After Merging Two Trees", "description": "Solution: Find Minimum Diameter After Merging Two Trees", "tags": [], "original_content": "# Find Minimum Diameter After Merging Two Trees\r\n\r\n## Solution\r\n\r\n## Overview\r\n\r\nWe are given two trees: one with n nodes and the other with m nodes. Our goal is to add an edge between a node from the first tree and a node from the second tree, in such a way that the diameter of the resulting tree is minimized.\r\n\r\n> The diameter of a tree is the longest path between any two nodes in the tree.\r\n\r\nLet us consider the two ways that the longest path can be formed:\r\n\r\n1 The path starts and ends at nodes within the same tree.\r\n\r\nIn this case, the problem reduces to finding the maximum diameter of the two original trees.\r\n\r\n2 The path starts at a node in the first tree and ends at a node in the second.\r\n\r\nIn this case, the selection of the nodes to connect is crucial for minimizing the overall diameter. Intuitively, we aim to select these nodes so that, if chosen as roots, the heights of their respective trees are minimized. In practice, this often involves selecting nodes near the \"center\" of each tree, ensuring their subtrees are as balanced as possible. This property is associated with the centroid(s) of a tree:\r\n\r\n> Centroid of a tree is a node which if removed from the tree would split it into a ‘forest’, such that any tree in the forest would have at most half the number of vertices in the original tree.\r\n\r\nBy adding an edge between two centroids of the trees, the maximum distance between each of them and a node within the same tree is at most \r\nfloor(diameter/2). Thus, the combined diameter of the tree is the sum of the halves of the original diameters plus one for the extra edge:\r\n\r\nfloor(diameter_1 / 2) + floor(diameter _2 / 2) + 1\r\n\r\nTherefore, the problem simplifies to returning the maximum among the diameter of each tree and the above value.\r\n\r\nFeel free to try solving these problems first as great prerequisites to this one:\r\n1. Minimum Height Trees.\r\n2. Tree Diameter\r\n\r\n## Approach 1: Farthest of Farthest (BFS)\r\n\r\n### Intuition\r\n\r\nLet's break down the problem of calculating the diameter of a tree. First of all, we observe that any tree can be seen as:\r\n\r\n- The sequence of nodes on the diameter itself, plus\r\n- Additional subtrees branching out from nodes along the diameter.\r\n\r\nFor any node in the tree, its minimum distance to one of the diameter's endpoints (say a and b) is always less than or equal to the diameter. This can be proven via contradiction. If one endpoint of the diameter (a) is known, the other endpoint (b) is simply the farthest node from a.\r\n\r\nBased on that, one naive way to find the diameter is:\r\n\r\n1 Assume each node is one endpoint of the diameter.\r\n2 Calculate the farthest node from it.\r\n3 Record the longest path found.\r\nHowever, this approach involves computing the farthest node for all nodes, leading to a time complexity of O(n^2)\r\n, which will result in a TLE (Time Limit Exceeded) for the given constraints.\r\n\r\nFor the optimized approach, we observe that we only need to find the farthest node of a single arbitrary node u and that node would be one of the endpoints of the diameter. Why does this work? Let's consider the following cases:\r\n\r\nCase 1: u lies on the diameter\r\nRunning a BFS for the longest path from u will find an endpoint of the diameter.\r\n\r\nCase 2: u does not lie on the diameter\r\nThe path from u to the farthest node passes through the diameter so the problem reduces to Case 1.\r\n\r\nTherefore, to calculate the diameter of a tree, only two BFS calls are needed:\r\n\r\n1 First BFS starting from any arbitrary node to find the farthest node from it, which is also an endpoint of the diameter.\r\n2 Second BFS starting from this farthest node to find the farthest node from it, which is equal to the second endpoint of the diameter.\r\n> Breadth-First Search (BFS): For a more comprehensive understanding of breadth-first search, check out the BFS Explore Card. This resource provides an in-depth look at BFS, explaining its key concepts and applications with a variety of problems to solidify understanding of the pattern.\r\n\r\n### Algorithm\r\n\r\nMain Function: minimumDiameterAfterMerge\r\n- Calculate the number of nodes for each tree:\r\n\r\n\t- n is the number of nodes in Tree 1.\r\n\t- m is the number of nodes in Tree 2.\r\n- Build adjacency lists for both trees:\r\n\r\n\t- Call buildAdjList(n, edges1) to construct the adjacency list for the first tree.\r\n\t- Call buildAdjList(m, edges2) to construct the adjacency list for the second tree.\r\n- Calculate the diameters of both trees:\r\n\r\n\t- Call findDiameter(n, adjList1) to find the diameter of the first tree.\r\n\t- Call findDiameter(m, adjList2) to find the diameter of the second tree.\r\n- Calculate the longest path that spans across both trees:\r\n\r\n\t- Calculate combinedDiameter as the sum of half the diameters of both trees, plus 1 (rounded up).\r\n- Return the maximum of the three possibilities:\r\n\r\nReturn the maximum of diameter1, diameter2, and combinedDiameter.\r\n\r\nbuildAdjList function:\r\n- Create an adjacency list of size size.\r\n- For each edge in edges, add the nodes to each other's adjacency list.\r\n\r\nfindDiameter function:\r\n- Call findFarthestNode(n, adjList, 0) to find the farthest node from an arbitrary starting node (e.g., node 0).\r\n- Call findFarthestNode(n, adjList, farthestNode) from the previously found farthest node to determine the tree diameter.\r\n\r\nfindFarthestNode function:\r\n- Initialize a queue and a visited array to perform BFS starting from sourceNode.\r\n- Traverse the graph, updating the farthest node each time a node is dequeued.\r\n- Return the farthest node and the distance (diameter).\r\n\r\n### Implementation\r\n\r\n```\r\nclass Solution:\r\n    def minimumDiameterAfterMerge(self, edges1, edges2):\r\n        # Calculate the number of nodes for each tree\r\n        n = len(edges1) + 1\r\n        m = len(edges2) + 1\r\n\r\n        # Build adjacency lists for both trees\r\n        adj_list1 = self.build_adj_list(n, edges1)\r\n        adj_list2 = self.build_adj_list(m, edges2)\r\n\r\n        # Calculate the diameters of both trees\r\n        diameter1 = self.find_diameter(n, adj_list1)\r\n        diameter2 = self.find_diameter(m, adj_list2)\r\n\r\n        # Calculate the longest path that spans across both trees\r\n        combined_diameter = ceil(diameter1 / 2) + ceil(diameter2 / 2) + 1\r\n\r\n        # Return the maximum of the three possibilities\r\n        return max(diameter1, diameter2, combined_diameter)\r\n\r\n    def build_adj_list(self, size, edges):\r\n        adj_list = [[] for _ in range(size)]\r\n        for edge in edges:\r\n            adj_list[edge[0]].append(edge[1])\r\n            adj_list[edge[1]].append(edge[0])\r\n        return adj_list\r\n\r\n    def find_diameter(self, n, adj_list):\r\n        # First BFS to find the farthest node from an arbitrary node (e.g., 0)\r\n        farthest_node, _ = self.find_farthest_node(n, adj_list, 0)\r\n\r\n        # Second BFS to find the diameter starting from the farthest node\r\n        _, diameter = self.find_farthest_node(n, adj_list, farthest_node)\r\n        return diameter\r\n\r\n    def find_farthest_node(self, n, adj_list, source_node):\r\n        queue = deque([source_node])\r\n        visited = [False] * n\r\n        visited[source_node] = True\r\n\r\n        maximum_distance = 0\r\n        farthest_node = source_node\r\n\r\n        while queue:\r\n            for _ in range(len(queue)):\r\n                current_node = queue.popleft()\r\n                farthest_node = current_node\r\n\r\n                for neighbor in adj_list[current_node]:\r\n                    if not visited[neighbor]:\r\n                        visited[neighbor] = True\r\n                        queue.append(neighbor)\r\n\r\n            if queue:\r\n                maximum_distance += 1\r\n\r\n        return farthest_node, maximum_distance\r\n```\r\n\r\n### Complexity Analysis\r\n\r\nLet n be the number of nodes in the first tree and m the number of nodes in the second tree.\r\n\r\nTime complexity: O(n+m)\r\n\r\nTo calculate the diameter of a tree, we perform two BFS calls using the findFarthestNode function. Each BFS visits every node and edge exactly once, and since the number of edges is k−1=O(k) for a tree of size k, the time complexity of one BFS is O(k). Thus, finding the diameter of the first tree takes O(n), and for the second tree, it takes O(m), as each involves two BFS calls.\r\n\r\nThe combined diameter of the tree is calculated using constant-time operations like addition and comparison, contributing O(1) to the overall time complexity of O(n+m).\r\n\r\nSpace complexity: O(n+m)\r\n\r\nAll the data structures used in the algorithm, including the adjacency lists, the visited array, and the nodesQueue, have linear space complexity in terms of the size of the tree being processed. Therefore, the total space complexity is O(n+m).\r\n\r\n## Approach 2: Depth First Search\r\n\r\n### Intuition\r\n\r\nLet’s start with a simple observation based on the definition of the diameter:\r\n\r\n- For each node in the tree, we calculate the length of the longest path passing through it. The longest of these paths represents the diameter of the tree.\r\nTo determine the longest path that passes through a node u, we perform a DFS to calculate the two longest distances from u to any leaf nodes in the tree. The sum of these two distances gives the length of the longest path through u.\r\n\r\nDuring the recursive calls, each node returns two values:\r\n\r\n1 The diameter of its subtree.\r\n2 The longest path to a leaf in its subtree, or its depth. This avoids redundant calculations, reusing previously computed values.\r\n> Depth-First Search (DFS): For a more comprehensive understanding of depth-first search, check out the DFS Explore Card. This resource provides an in-depth look at DFS, explaining its key concepts and applications with a variety of problems to solidify understanding of the pattern.\r\n\r\nAlgorithm\r\nMain Function: minimumDiameterAfterMerge\r\n- Calculate the number of nodes for each tree:\r\n\t- n is the number of nodes in Tree 1.\r\n\t- m is the number of nodes in Tree 2.\r\n- Build adjacency lists for both trees:\r\n\t- Use the buildAdjList function to construct the adjacency list for both trees (adjList1 and adjList2).\r\n- Find the diameter of Tree 1:\r\n\t- Call findDiameter(adjList1, 0, -1) to start a DFS from node 0 in Tree 1.\r\n\t- Store the diameter of Tree 1 in diameter1.\r\n- Find the diameter of Tree 2:\r\n\t- Call findDiameter(adjList2, 0, -1) to start a DFS from node 0 in Tree 2.\r\n\t- Store the diameter of Tree 2 in diameter2.\r\n- Calculate the diameter of the combined tree:\r\n\t- The combined diameter accounts for the longest path spanning both trees.\r\n\t- It is calculated as ceil(diameter1 / 2.0) + ceil(diameter2 / 2.0) + 1.\r\n- Return the maximum diameter:\r\n\t- Return the maximum of the three values: diameter1, diameter2, and combinedDiameter.\r\nHelper Function: buildAdjList\r\n- Given the number of nodes size and an edge list edges, build an adjacency list (adjList):\r\n\t- Iterate through each edge and add the corresponding nodes to the adjacency list.\r\nHelper Function: findDiameter\r\n- Given the adjacency list adjList, the current node, and its parent, calculate the diameter of the tree:\r\n\t- Initialize two variables maxDepth1 and maxDepth2 to track the two largest depths from the current node.\r\n\t- Initialize diameter to track the diameter of the subtree.\r\nFor each neighbor of the current node:\r\n\r\n\t- Skip the parent node to avoid cycles.\r\n\t- Recursively calculate the diameter and depth of the neighbor’s subtree.\r\n\t- Update diameter with the maximum of the current diameter and the child’s diameter.\r\n\t- Increment the depth and update the two largest depths (maxDepth1 and maxDepth2).\r\n- The diameter of the current node is updated as maxDepth1 + maxDepth2.\r\n\r\n- Return the diameter and maxDepth1 (to be used by the parent).\r\n\r\n### Implementation\r\n\r\n```\r\nclass Solution:\r\n    def minimumDiameterAfterMerge(\r\n        self, edges1: list[list[int]], edges2: list[list[int]]\r\n    ) -> int:\r\n        # Calculate the number of nodes for each tree (number of edges + 1)\r\n        n = len(edges1) + 1\r\n        m = len(edges2) + 1\r\n\r\n        # Build adjacency lists for both trees\r\n        adj_list1 = self.build_adj_list(n, edges1)\r\n        adj_list2 = self.build_adj_list(m, edges2)\r\n\r\n        # Calculate the diameter of both trees\r\n        diameter1, _ = self.find_diameter(\r\n            adj_list1, 0, -1\r\n        )  # Start DFS for Tree 1\r\n        diameter2, _ = self.find_diameter(\r\n            adj_list2, 0, -1\r\n        )  # Start DFS for Tree 2\r\n\r\n        # Calculate the diameter of the combined tree\r\n        # This accounts for the longest path spanning both trees\r\n        combined_diameter = ceil(diameter1 / 2) + ceil(diameter2 / 2) + 1\r\n\r\n        # Return the maximum diameter among the two trees and the combined tree\r\n        return max(diameter1, diameter2, combined_diameter)\r\n\r\n    # Helper function to build an adjacency list from an edge list\r\n    def build_adj_list(\r\n        self, size: int, edges: list[list[int]]\r\n    ) -> list[list[int]]:\r\n        adj_list = [[] for _ in range(size)]\r\n        for edge in edges:\r\n            adj_list[edge[0]].append(edge[1])\r\n            adj_list[edge[1]].append(edge[0])\r\n        return adj_list\r\n\r\n    # Helper function to find the diameter of a tree\r\n    # Returns the diameter and the depth of the node's subtree\r\n    def find_diameter(\r\n        self, adj_list: list[list[int]], node: int, parent: int\r\n    ) -> tuple[int, int]:\r\n        max_depth1 = max_depth2 = (\r\n            0  # Tracks the two largest depths from the current node\r\n        )\r\n        diameter = 0  # Tracks the maximum diameter of the subtree\r\n\r\n        for neighbor in adj_list[node]:\r\n            if neighbor == parent:\r\n                continue  # Skip the parent to avoid cycles\r\n\r\n            # Recursively calculate the diameter and depth of the neighbor's subtree\r\n            child_diameter, depth = self.find_diameter(adj_list, neighbor, node)\r\n            depth += 1  # Increment depth to include edge to neighbor\r\n\r\n            # Update the maximum diameter of the subtree\r\n            diameter = max(diameter, child_diameter)\r\n\r\n            # Update the two largest depths from the current node\r\n            if depth > max_depth1:\r\n                max_depth2 = max_depth1\r\n                max_depth1 = depth\r\n            elif depth > max_depth2:\r\n                max_depth2 = depth\r\n\r\n        # Update the diameter to include the path through the current node\r\n        diameter = max(diameter, max_depth1 + max_depth2)\r\n\r\n        # Return the diameter and the longest depth\r\n        return diameter, max_depth1\r\n```\r\n\r\n### Complexity Analysis\r\n\r\nLet n be the number of nodes in the first tree and m the number of nodes in the second tree.\r\n\r\nTime complexity: O(n+m)\r\n\r\nThe findDiameter function uses Depth-First Search (DFS) on the tree, with a time complexity of O(k), where k is the tree's size. The diameter calculation itself takes O(n+m) time. Since combining the diameters involves only constant-time operations, the overall time complexity is O(n+m).\r\n\r\nSpace complexity: O(n+m)\r\n\r\nThe space complexity depends on the size of the data structures and the recursion depth. Using an adjacency list representation of the trees requires O(n+m) space. Additionally, the recursion depth can reach O(k), where k is the number of nodes in the processed tree. Thus, the total space complexity is O(n+m).", "source": "user", "status": "ready"}