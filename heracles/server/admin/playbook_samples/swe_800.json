{"id": "cd3870d8-a3d4-4e61-beb6-60878e06f3fb", "title": "The function generate_uid() generates non-conforming “2.25 .” DICOM UIDs", "description": "The function generate_uid() generates non-conforming “2.25 .” DICOM UIDs <!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->  #### Description It seems there was already a discussion about this function in the past (#125), but the current implementation generates non-conforming DICOM UIDs when called with prefix ‘none’ to trigger that the function generate_uid() should generate a UUID derived UID.  The DICOM Standard requires (see DICOM PS 3.5, B.2 that when a UUID derived UID is constructed it should be in the format “2.25.” + uuid(in its decimal representation string representation) For example a UUID of f81d4fae-7dec-11d0-a765-00a0c91e6bf6 should become 2.25.329800735698586629295641978511506172918  The current implementation extends the uuid part to the remaining 59 characters. By not following the DICOM formatting rule, receiving systems that are processing DICOM instances created with this library are not capable of converting the generated “2.25” UID back to a UUID. Due to the extra sha512 operation on the UUID, the variant and version info of the UUID are also lost.  #### Steps/Code to Reproduce - call generate_uid() to generate a \"2.25.\" DICOM UID  #### Expected Results A conforming unique DICOM UID is returned.  #### Actual Results Non conforming UID is returned.", "tags": [], "original_content": "## Overview\r\nThis update resolves the issue of generating non-conforming DICOM UIDs in `pydicom` when using the `generate_uid()` function with a `None` prefix. The update ensures compliance with the DICOM Standard by using the UUID4 algorithm to generate a correctly formatted \"2.25\" UID, which conforms to the \"2.25.\" + integer form of UUID as required by the standard. \r\n\r\n## Procedure\r\n1. **Update `generate_uid` function signature:** \r\n   - Adjust the documentation in `pydicom/uid.py` to clarify that when `prefix` is `None`, a UUID4-derived integer will be used with a \"2.25.\" prefix.\r\n\r\n2. **Implement UUID4-based UID generation:**\r\n   - Modify the `generate_uid` function in `pydicom/uid.py` to return a UID by formatting it as \"2.25.\" followed by the integer representation of a newly generated UUID v4 using `uuid4().int`.\r\n\r\n3. **Remove incorrect UID length handling:**\r\n   - Remove any logic that incorrectly adjusts the length of the UUID-derived UIDs to the remaining 59 characters, ensuring compliance with the standard.\r\n\r\n4. **Retain the existing logic for non-None prefixes:**\r\n   - Preserve the SHA512-based UID generation process for cases where the `prefix` is not `None`. This ensures deterministic UID generation for custom prefixes.\r\n\r\n5. **Update tests and documentation:**\r\n   - Verify that updated examples in the documentation reflect the correct usage of `generate_uid()` with the expected results.\r\n   - Ensure any relevant tests check for compliance with DICOM's UID formatting rules.\r\n\r\n## Git Diff Messages\r\n```\r\ndiff --git a/pydicom/uid.py b/pydicom/uid.py\r\n--- a/pydicom/uid.py\r\n+++ b/pydicom/uid.py\r\n@@ -250,19 +250,19 @@ def generate_uid(prefix=PYDICOM_ROOT_UID, entropy_srcs=None):\r\n     ----------\r\n     prefix : str or None\r\n         The UID prefix to use when creating the UID. Default is the pydicom\r\n-        root UID '1.2.826.0.1.3680043.8.498.'. If None then a value of '2.25.'\r\n-        will be used (as described on `David Clunie's website\r\n+        root UID '1.2.826.0.1.3680043.8.498.'. If None then a prefix of '2.25.'\r\n+        will be used with the integer form of a UUID generated using the\r\n+        UUID4 algorithm.\r\n     entropy_srcs : list of str or None\r\n-        If a list of str, the prefix will be appended with a SHA512 hash of the\r\n-        list which means the result is deterministic and should make the\r\n-        original data unrecoverable. If None random data will be used\r\n-        (default).\r\n+        If `prefix` is not None, then the prefix will be appended with a\r\n+        SHA512 hash of the list which means the result is deterministic and\r\n+        should make the original data unrecoverable. If None random data will\r\n+        be used (default).\r\n \r\n     Returns\r\n     -------\r\n     pydicom.uid.UID\r\n-        A 64 character DICOM UID.\r\n+        A DICOM UID of up to 64 characters.\r\n \r\n     Raises\r\n     ------\r\n@@ -275,17 +275,17 @@ def generate_uid(prefix=PYDICOM_ROOT_UID, entropy_srcs=None):\r\n     >>> generate_uid()\r\n     1.2.826.0.1.3680043.8.498.22463838056059845879389038257786771680\r\n     >>> generate_uid(prefix=None)\r\n-    2.25.12586835699909622925962004639368649121731805922235633382942\r\n+    2.25.167161297070865690102504091919570542144\r\n     >>> generate_uid(entropy_srcs=['lorem', 'ipsum'])\r\n     1.2.826.0.1.3680043.8.498.87507166259346337659265156363895084463\r\n     >>> generate_uid(entropy_srcs=['lorem', 'ipsum'])\r\n     1.2.826.0.1.3680043.8.498.87507166259346337659265156363895084463\r\n     \"\"\"\r\n-    max_uid_len = 64\r\n-\r\n     if prefix is None:\r\n-        prefix = '2.25.'\r\n+        # UUID -> as 128-bit int -> max 39 characters long\r\n+        return UID('2.25.{}'.format(uuid.uuid4().int))\r\n \r\n+    max_uid_len = 64\r\n     if len(prefix) > max_uid_len - 1:\r\n         raise ValueError(\"The prefix must be less than 63 chars\")\r\n     if not re.match(RE_VALID_UID_PREFIX, prefix):\r\n```", "source": "user", "status": "ready"}