import re
import traceback
import inspect
import asyncio
from typing import Optional


from heracles.core.logger import get_playground_logger
from heracles.core.exceptions import Agent<PERSON>unException, IDEServerFunCallException
from heracles.core.schema import ErrorHandlerMessage, ErrorHandlerMessageType

from .playground import Playground
from .playground_channel_mixins import UserEventsMixin, GitEventsMixin

class PlaygroundChannel(GitEventsMixin, UserEventsMixin):
    def __init__(self, sid, socketio_server, playground_manager, playground_id, project_id=None, is_root=False):
        self.sid = sid
        self.socketio_server = socketio_server
        self.playground_manager = playground_manager
        self.playground_id = playground_id
        self.project_id = project_id
        self.is_root = is_root
        self.logger = get_playground_logger(playground_id)
        self._current_playground: Optional[Playground] = None
        self._asyncio_task = None
        self._asyncio_cmd_k = None
        self._is_enter_room = False

    @property
    def current_playground(self) -> Playground:
        if self._current_playground is None:
            raise AgentRunException("current_playground is not inited")
        return self._current_playground

    async def start(self):
        playground = await self.playground_manager.find_or_load_cache_by(self.playground_id, self.socketio_server)
        self.logger.debug(f'start call: id={self.playground_id} sid={self.sid} playground={playground}')
        if not playground:  # pragma: no cover
            playground = Playground(self.playground_id, self.socketio_server, self.is_root, self.project_id)
            self.playground_manager.add_playground(playground)
        else:
            if playground.is_root != self.is_root:  # pragma no cover
                self.logger.debug(f'is_root changed from {playground.is_root} to {self.is_root}')
                playground.is_root = self.is_root
            self.logger.debug('already exist')

        # 兼容缓存中没有project_id的记录
        playground.project_id = self.project_id

        self.logger.debug(f'set current_playground: {playground}')
        self._current_playground = playground

        await playground.start()

        # 出现异常也不处理离开房间的操作
        await self.socketio_server.enter_room(self.sid, playground.room_name)
        self._is_enter_room = True

        await self.sync()

        playground.add_channel_online_count()

    async def stop(self):
        if self._current_playground is not None:
            playground = self._current_playground

            if self._is_enter_room:
                await self.socketio_server.leave_room(self.sid, playground.room_name)

            playground.reduce_channel_online_count()

    # event
    async def on_message(self, message: str, memory_regenerate=False, need_consider_task=False):
        """ 接受并处理 message
        :param message: 字符串消息
        Return True 接受消息并开始处理, False 状态不对拒绝处理

        注: 后续处理的结果分两种情况, 一种是用 chunkMessage 分段发送, 另一种是 message 事件进行回复
        """
        # 内部 func_call 测试入口
        if message.startswith('clacky'):
            if self._asyncio_task:  # pragma: no cover
                self._asyncio_task.cancel()
                self._asyncio_task = None
            self._asyncio_task = asyncio.create_task(self.clacky_internal_func_call_test(message))
            return True

        if not self.current_playground.is_accepted_message():
            self.logger.debug(f'User message ignore, received message is: {message}')
            return False

        # 进来的格式可能会是一个非 bool 的
        if memory_regenerate:
            memory_regenerate = True
        else:
            memory_regenerate = False

        if need_consider_task:
            need_consider_task = True
        else:
            need_consider_task = False

        try:
            # 系统已经成功接受消息, 则将原始消息同步给其他用户
            # FIXME 需要区分不同用户的聊天行为
            await self.broadcast_others('message', message)
            return await self.current_playground.on_user_event(
                'message',
                message,
                memory_regenerate=memory_regenerate,
                need_consider_task=need_consider_task
            )
        except Exception as e:  # pragma: no cover
            self.logger.error('Traceback: %s', traceback.format_exc())
            error_message_content = f'on_message error: {e}'
            error_message = ErrorHandlerMessage(error_type=ErrorHandlerMessageType.MESSAGE, content=error_message_content)
            await self.error_handler(error_message)

    async def on_any(self, event_name, *args):
        """ event 避免未处理的事件, 全部捕捉并打印
        """
        self.logger.warning(f'Received unhandled event: event={event_name}, args={args}')
        raise AgentRunException(f'Received unhandled event: event={event_name}, args={args}')

    # trigger event will send back to user
    # 任务有更新
    # async def task_updated(self):
    # 任务完成
    # async def task_completed(self):

    # playground 主动向用户侧推送的事件列表
    # 命名规范: sync 就是 sync
    # 除 sync 化, 其他均以 xx_动作完成时定义, 以代表已经触发完毕的事件
    # 例如 task_updated, 代表任务动作有更新

    async def sync(self):
        """ 首次连接完毕, 先进行全量同步
        """
        await self.transmit('sync', self.current_playground.get_playground_info())

    async def error_handler(self, error_message: ErrorHandlerMessage):
        """ 一些错误处理事件
        """
        await self.transmit('errorHandler', error_message.dict())

    async def clacky_internal_func_call_test(self, data):
        """ 这里实现了一个便于测试 IDEServer 侧 func_call 的接口

        如有多个参数用 | 隔开

        使用方法, 在聊天框输入 clacky func_call_name argument1|argument2

        注意: func_call_name 不需要再加上前缀agent

        例如:
        clacky read_file index.html => IdeServerClient 实现的 agent_read_file 接口
        clacky write_file index.html|file_content => agent_write_file('index.html', 'file_content')

        特别增加了 manager 相关的操作:
        clacky manager true # 列出全部待清理的 playground
        clacky manager # 列出所有 playground
        clacky manager clear # 清除所有 cache, 请评估风险后非常小心使用
        """
        try:
            match = re.match(r'^clacky\s+(\S+)\s*(.*)$', data)
            if not match:
                raise AgentRunException(f'wrong clacky func_call command: {data}')

            origin_func_call_name = match.group(1)
            left_string = match.group(2)

            if origin_func_call_name == 'manager':
                if left_string == 'true':
                    res = self.playground_manager.to_dict(clear_flag=True)
                elif left_string == 'clear':
                    res = self.playground_manager.clear_all_playgrounds()
                else:
                    res = self.playground_manager.to_dict()
                await self.transmit('message', str(res))
                return

            if origin_func_call_name == 'cache':
                playground_id = left_string
                res = self.playground_manager.show_playground_cache(playground_id)
                await self.transmit('message', str(res))
                return

            if origin_func_call_name == 'rag':
                res = await self.current_playground.agent_controller.workspace.rag_searcher.search(left_string)
                await self.transmit('message', str(res))
                return

            if origin_func_call_name == 'ai':  # pragma: no cover
                await self.transmit('message', 'Please click ClackyAI avatar to follow me, I will show some cool thing for you')
                await asyncio.sleep(5)
                # TODO: 等 IDEServer 增加请求follow能力后，实现之
                wait_time = 3
                await self.transmit('message', 'I will using file_xx create a file...')
                await asyncio.sleep(wait_time)
                await self.current_playground.ide_server_client.agent_create_file('test.txt')
                await asyncio.sleep(wait_time)
                await self.transmit('message', 'I will write some text to file...')
                await self.current_playground.ide_server_client.agent_write_file('test.txt', 'Hello, ClackyAI is cool')
                await asyncio.sleep(wait_time)
                await self.transmit('message', 'I will create a directory...')
                await self.current_playground.ide_server_client.agent_create_directory('abc')
                await asyncio.sleep(wait_time)
                await self.current_playground.ide_server_client.agent_create_file('abc/test.c')
                await asyncio.sleep(wait_time)
                await self.current_playground.ide_server_client.agent_write_file('abc/test.c', 'int main() { return 0 }')
                await asyncio.sleep(wait_time)
                await self.transmit('message', 'I will execute a command in terminal...')
                result = await self.current_playground.ide_server_client.agent_terminal_with_result('clear')
                await asyncio.sleep(wait_time)
                result = await self.current_playground.ide_server_client.agent_terminal_with_result('pwd')
                await self.transmit('message', f'Cool, the result is {result}')
                await asyncio.sleep(wait_time)
                await self.transmit('message', 'I will try to check a command is or not available...')
                try:
                    result = await self.current_playground.ide_server_client.agent_terminal_with_result('pnpm -v')
                    await self.transmit('message', f'Found it, version is {result}')
                except IDEServerFunCallException as e:
                    await self.transmit('message', str(e))
                await asyncio.sleep(wait_time)
                await self.transmit('message', 'Showtime end, Bye bye')
                await self.current_playground.ide_server_client.agent_delete_file('test.txt')
                await self.current_playground.ide_server_client.agent_delete_directory('abc')
                await self.current_playground.ide_server_client.agent_terminal_with_result('clear')
                await self.current_playground.ide_server_client.agent_terminal_with_result("echo I will always be here for you")
                return

            func_call_name = f'agent_{origin_func_call_name}'
            target_method = getattr(self.current_playground.ide_server_client, func_call_name, None)
            if not target_method:
                await self.transmit('message', f'clacky func_call not found: {func_call_name}')
                return
            # 计算 func_call 的参数数量, 以便调用
            func_call_arguments_count = len(inspect.signature(target_method).parameters)
            if func_call_arguments_count == 0:
                arguments = None
            else:
                arguments = left_string.split('|')
            self.logger.info(f'clacky func_call: {func_call_name}, {func_call_arguments_count}, {arguments}')
            if arguments is None:
                res = await self.current_playground.func_call(func_call_name)
            else:
                res = await self.current_playground.func_call(func_call_name, *arguments)
        except Exception as e:
            self.logger.warning('clacky_internal_func_call_test error found: \nTraceback: %s', traceback.format_exc())
            res = str(e)
        await self.transmit('message', str(res))

    async def trigger_cmd_k(self, message):
        """ cmd_k 事件触发 """
        await self.transmit('chunkCmdK', message)

    # send to me
    async def transmit(self, event_name, data):
        self.logger.debug(f'u<<: {event_name}, {data}')
        await self.socketio_server.emit(event_name, data, to=self.sid)

    # send to all
    async def broadcast_all(self, event_name, data):
        await self.current_playground.broadcast_all(event_name, data)

    # send to others
    async def broadcast_others(self, event_name, data):
        await self.current_playground.broadcast_all(event_name, data, skip_sid=self.sid)
