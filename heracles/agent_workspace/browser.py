from heracles.core.exceptions import BrowserException, PaasAgentException
from heracles.core.schema import FocusComponentType
from heracles.core.utils.paas_agent import paas_request
from heracles.server.clacky.ide_server_client_mixins.focus_components import FollowFocusInterface, FocusAreaMixin


class Browser(FocusAreaMixin):
    def __init__(self, url: str, follow_focus: FollowFocusInterface):
        super().__init__()

        self.url = url + '/browser'
        self.follow_focus = follow_focus

    async def following_focus_component(self, area: FocusComponentType):
        await self.follow_focus.following_focus_component(area)

    # 打开新标签页
    @FocusAreaMixin.focus(FocusComponentType.REMOTE_ACCESS)
    async def goto(self, target_url: str):
        data = {'url': target_url}
        try:
            await paas_request('post', self.url + '/openTab', data)
        except PaasAgentException as e:
            raise BrowserException(e) from e

    # 对当前标签页进行截图
    @FocusAreaMixin.focus(FocusComponentType.REMOTE_ACCESS)
    async def screenshot(self) -> str | None:
        data = {'image_type': 'base64', 'image_format': 'jpeg'}
        head = 'data:image/jpeg;base64,'

        url = self.url + '/screenshot'
        try:
            response = await paas_request('post', url, data)
            if response and 'data' in response:
                return head + response['data']
            else:
                return None

        except PaasAgentException as e:
            raise BrowserException(e) from e

    # 获取当前标签页的consol 日志
    @FocusAreaMixin.focus(FocusComponentType.REMOTE_ACCESS)
    async def get_console_logs(self) -> list[str]:
        url = self.url + '/getConsoleLogs'

        try:
            response = await paas_request('post', url)

            return response['list'] if response and 'list' in response else []
        except PaasAgentException as e:
            raise BrowserException(e) from e
