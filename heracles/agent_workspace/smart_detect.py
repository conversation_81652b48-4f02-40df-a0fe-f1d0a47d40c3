from typing import Literal

from heracles.core.exceptions import AgentRunException
from heracles.core.schema import ProjectErrorMessage

class SmartDetect:
    def __init__(self, workspace):
        self.workspace = workspace
        self.errors: list[ProjectErrorMessage] = []
        self.status: Literal['init', 'task_checking', 'monitoring_errors', 'stopped'] = 'init'

    async def set_status(self, status: Literal['task_checking', 'monitoring_errors', 'stopped']):
        if status != 'stopped' and status == self.status:
            raise AgentRunException(f'Smart detect status {status} is already running')
        self.status = status
