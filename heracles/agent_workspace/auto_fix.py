from typing import Literal
from heracles.core.schema import FocusComponentType


class AutoFix:
    def __init__(self, workspace):
        self.workspace = workspace
        self.enable = True
        self.fix_turn = 0
        self.max_fix_turn = 2
        self.status: Literal['init', 'start', 'check_errors', 'fix_errors', 'fix_completed', 'fix_stopped'] = 'init'
        self.start_auto_fix_hint_message = 'Code writing is complete, and I am actively running the project. I will analyze terminal logs, console outputs, and lint checks to identify all critical errors and automatically create additional steps to fix them.'  # noqa: E501
        self.checking_error_hint_message = "I'm now running the project to verify the effectiveness of the fixes. "
        self.detect_no_error_hint_message = 'The project appears to be running well. Further testing is recommended to confirm everything works as expected. Let me know if you need any assistance.'  # noqa: E501
        self.additional_step_hint_message = 'I will create additional steps to fix the errors.'
        self.fix_done_hint_message = "I've applied the fixes, and the project appears to be running well. Further testing is recommended to confirm everything works as expected. Let me know if you need any assistance."  # noqa: E501

    async def update_status_and_event(
        self, status: Literal['init', 'start', 'check_errors', 'fix_errors', 'fix_completed', 'fix_stopped'], data=None
    ):
        if status == self.status:
            return
        self.status = status
        if status == 'init':
            self.fix_turn = 0
        elif status == 'start':
            self.fix_turn = 0
            await self.trigger_auto_fix_start('')
            await self.workspace.trigger_general_loading(tool_name='Task', status='start')
        elif status == 'check_errors':
            await self.workspace.playground.trigger_message(self.start_message())
            await self.workspace.trigger_general_loading(tool_name='Task', status='Clacky is checking errors')
        elif status == 'fix_errors':
            await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)
            await self.workspace.playground.trigger_message(self.add_step_message())
            await self.trigger_auto_fix_add_task_step(data)
            await self.workspace.trigger_general_loading(tool_name='Task', status='Clacky is fixing errors')
        elif status == 'fix_completed':
            await self.trigger_auto_fix_completed(self.completed_message())
            await self.workspace.trigger_general_loading(tool_name='Task', status='end')
        elif status == 'fix_stopped':
            await self.trigger_auto_fix_stopped(data)
            await self.workspace.trigger_general_loading(tool_name='Task', status='end')

    def start_message(self):
        if self.fix_turn == 0:
            return self.start_auto_fix_hint_message
        else:
            return self.checking_error_hint_message

    def add_step_message(self):
        return self.additional_step_hint_message

    def completed_message(self):
        if self.fix_turn == 0:
            return self.detect_no_error_hint_message
        else:
            return self.fix_done_hint_message

    async def set_auto_fix_enable(self, enable: bool = False):
        self.enable = enable

    async def trigger_auto_fix_start(self, message: str):
        data = {
            'status': 'start',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)

    async def trigger_auto_fix_stopped(self, message: str):
        data = {
            'status': 'stopped',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)

    async def trigger_auto_fix_completed(self, message: str):
        data = {
            'status': 'completed',
            'message': message,
        }
        await self.workspace.trigger('auto_fix_status_updated', data)

    async def trigger_auto_fix_errors_updated(self, data):
        """
        :param data: {
            'id': '',
            'message': '',
            'error_list': [
                {
                    'content': '',
                    'title': '',
                },
            ]
        }
        """
        await self.workspace.trigger('auto_fix_errors_updated', data)

    async def trigger_auto_fix_add_task_step(self, data):
        await self.workspace.trigger('auto_fix_add_task_step', data)
