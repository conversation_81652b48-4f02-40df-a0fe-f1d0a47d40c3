---
title: New Project Instruction
description: 'Everything you need to know for creating and configuring new projects in Clacky environment.'
tags: ['new_project',]
---
## Overview

When creating a new project, you must follow the following principles:

## Important information
- `.1024` and `.gitignore` file need to be created for project
  - refer to 'Handling .1024 file' and 'Handling .gitignore file' playbook for more details.

## Procedure

Plan should be generated in ONE SHOT with following concerns.
1. Init Project Structure in root directory
   - When using project creation tools (like `npx`, `create-*` commands), avoid and NEVER create nested directories
   - Use `.` to create the project in the current directory (project root)
     - For repository cloning, you should also use `.` to create the project in the current directory
   - This ensures a clean and consistent project structure

   Examples:
   ```bash
   npx create-next-app/create-react-app . # ✅ Correct - Create project in current directory
   npx create vite . # ✅ Correct - Create project in current directory
   npx create-next-app/create-react-app new-project # ❌ Wrong, Creates nested directory is forbidden
   npx create vite new-project # ❌ Wrong, Creates nested directory is forbidden
   ```
2. Write code based on requirements
3. Install required package manager and dependencies using appropriate command if needed, otherwise skip.
  - Use package manager commands to add/install dependencies instead of manually editing dependency files
  - Use the latest version of dependencies unless specific version requirements are mentioned
  - Do not forget to install dependencies before running the project
  - If package manager not installed, install it first via default package manager provided by runtime(e.g. npm, pip, etc.).
4. Add `.1024` file to configure how to run in development environment
  - `.1024` support these case-sensitive keys: `compile_command` and `run_command`, `dependency_command`, `linter_config`.
  - If the project require compilation before running, `compile_command` should be provided as well.
5. Add `.gitignore` file if not exists and configure it
  - Add `.1024*`, `!.1024` and `.breakpoints` to `.gitignore`
  - Carefully analyze files and directories that do not need to be tracked by git based on the project's language and framework
6. If dotenv is planned to be used, add `.env` file to write necessary environment variables for used middlewares.
  - refer to 'Instructions for databases and other middlewares usage' playbook for more details.

## Additional Tips For Frontend Project

### TypeScript Usage

Unless mentioned otherwise, always prefer TypeScript (`.tsx`) over JavaScript (`.jsx`) for better type safety and developer experience, `jsx` may not work correctly in clacky environment.

## Core Dependencies

### Tailwind CSS
1. Only tailwindcss@3 is supported in clacky environment, other versions including tailwindcss@4 or tailwindcss@latest may not work correctly.
2. ⚠️ Installation: Always use `tailwindcss@3` - DO NOT install without version specification
   ```bash
   npm/yarn/pnpm install tailwindcss@3   # ✅ Correct
   npm/yarn/pnpm install tailwindcss     # ❌ Wrong, Only v3 is supported
   npm/yarn/pnpm install tailwindcss@latest # ❌ Wrong, Only v3 is supported
   npm/yarn/pnpm install tailwindcss@4  # ❌ Wrong, Only v3 is supported
   ```

## Cross Origin Request for development environments
explicitly configure "allowedDevOrigins" in next.config or other config files to allow cross origin request from development environments:

- Common development origins to allow: use '*' wildcard to allow all origins
- For Next.js: Add `allowedDevOrigins` configuration in `next.config.js/mjs`
- For Vite: Configure CORS settings in `vite.config.js/ts`
- For Express/Node.js: Set up CORS middleware to allow development origins
- ...
