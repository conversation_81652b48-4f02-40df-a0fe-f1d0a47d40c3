import os
from functools import partial
from typing import Union, overload, Optional, Any
import requests

import litellm
import traceback
from tenacity import retry, stop_after_attempt, wait_exponential
from heracles.core.config import get_env_var
from heracles.core.exceptions import (
    AgentLLMCallParamsException,
    <PERSON>LLMConfigException,
    AgentLLMCallResultException,
    AgentRunException
)
from heracles.core.logger import heracles_logger as logger
from heracles.core.schema import (
    LangfuseTraceOption,
    LLMAbilityType,
    SubBaseModelOrIterable,
    ToolCalls,
)
from heracles.core.utils.merge_dict import merge_dict
from heracles.core.utils.string import truncate
from heracles.core.utils.trim_messages import trim_messages


def load_langfuse():
    os.environ['LANGFUSE_PUBLIC_KEY'] = get_env_var('LANGFUSE_PUBLIC_KEY_OPTIONAL')
    os.environ['LANGFUSE_SECRET_KEY'] = get_env_var('LANGFUSE_SECRET_KEY_OPTIONAL')
    os.environ['LANGFUSE_HOST'] = get_env_var('LANGFUSE_HOST_OPTIONAL')
    if debug := get_env_var('LANGFUSE_DEBUG_OPTIONAL'):
        os.environ['LANGFUSE_DEBUG'] = debug
    litellm.success_callback = ['langfuse']
    litellm.failure_callback = ['langfuse']


if get_env_var('LANGFUSE_HOST_OPTIONAL'):
    logger.info("langfuse is enabled, load config")
    load_langfuse()
else:
    logger.warning("langfuse is not enabled, no langfuse config found")


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=15), retry_error_callback=lambda retry_state: {})
def _fetch_real_name_from_proxy(proxy_base_url: str, proxy_secret: str):
    """从代理中获取模型真实名称"""
    if not proxy_base_url:
        return {}
    headers = {'Authorization': f'Bearer {proxy_secret}'}

    response = requests.get(f'{proxy_base_url}/v1/model/info', headers=headers)
    if response.status_code == 200:
        resp = response.json()
        name_dict = {}
        for item in resp['data']:
            name_dict[item['model_name']] = item['model_info']['key']
        return name_dict
    else:
        msg = f'Failed to fetch model info, status code: {response.status_code}'
        logger.warning(msg)
        raise AgentRunException(msg)


MODEL_NAME_DICT = _fetch_real_name_from_proxy(get_env_var('PROXY_BASE_URL_OPTIONAL'), get_env_var('PROXY_SECRET_OPTIONAL')) # noqa
logger.info(f"Fetch model name dict from proxy: {MODEL_NAME_DICT}")
# avoid UnsupportedParamsError
litellm.drop_params = True


class LLM:
    def __init__(self, ability_type: LLMAbilityType = LLMAbilityType.NORMAL):
        """初始化 LLM 能力 weak, normal, strong, 默认为 normal"""
        self.ability_type = ability_type
        self.langfuse_option = LangfuseTraceOption()
        self.model_real_name = self._get_real_name()  # 由于proxy的存在，此处需要将 model_name 转换为litellm官方json中维护的名称

    def _get_real_name(self):
        """获取模型真实名称"""
        if not self.model.startswith('litellm_proxy/'):
            return self.model
        global MODEL_NAME_DICT
        name = MODEL_NAME_DICT.get(self.model.replace('litellm_proxy/', ''), None)
        if not name:
            logger.warning(f'Failed to get real name for model: {self.model}')
            return self.model
        return name

    def set_llm_metadata(self, langfuse_option: LangfuseTraceOption):
        self.langfuse_option = langfuse_option

    def update_llm_metadata(self, k, v):
        setattr(self.langfuse_option, k, v)

    def _get_llm_config(self):
        """根据能力类型获取配置"""
        default_config = {
            'model': get_env_var('LLM_MODEL'),
            'api_key': get_env_var('LLM_API_KEY'),
            'base_url': get_env_var('LLM_BASE_URL'),
        }
        normal_config = {
            'model': get_env_var('LLM_NORMAL_MODEL_OPTIONAL'),
            'api_key': get_env_var('LLM_NORMAL_API_KEY_OPTIONAL'),
            'base_url': get_env_var('LLM_NORMAL_BASE_URL_OPTIONAL'),
        }
        reasoning_config = {
            'model': get_env_var('LLM_REASONING_MODEL_OPTIONAL'),
            'api_key': get_env_var('LLM_REASONING_API_KEY_OPTIONAL'),
            'base_url': get_env_var('LLM_REASONING_BASE_URL_OPTIONAL'),
        }
        fast_config = {
            'model': get_env_var('LLM_FAST_MODEL_OPTIONAL'),
            'api_key': get_env_var('LLM_FAST_API_KEY_OPTIONAL'),
            'base_url': get_env_var('LLM_FAST_BASE_URL_OPTIONAL'),
        }
        config = {}
        if self.ability_type == 'normal':
            config = merge_dict(default_config, normal_config)
        elif self.ability_type == 'reasoning':
            config = merge_dict(default_config, reasoning_config)
        elif self.ability_type == 'fast':
            config = merge_dict(default_config, fast_config)
        elif self.ability_type == 'strong':
            config = default_config
        else:
            raise AgentLLMConfigException(
                f'llm config error, found not supported type: {self.ability_type}'
            )
        return config

    @overload
    async def async_send(
        self,
        user_message=None,
        system_message=None,
        origin_messages=None,
        chunk_callback=None,
        tools: None = None,
        tools_callback=None,
        tool_choice=None,
        reasoning_effort: Optional[str] = None,
    ) -> str:
        ...

    @overload
    async def async_send(
        self,
        user_message=None,
        system_message=None,
        origin_messages=None,
        chunk_callback=None,
        tools: list[Any] = ...,
        tools_callback=None,
        tool_choice=None,
        reasoning_effort: Optional[str] = None,
    ) -> ToolCalls:
        ...

    @overload
    async def async_send(
        self,
        user_message=None,
        system_message=None,
        origin_messages=None,
        chunk_callback=None,
        tools: Optional[list[Any]] = None,
        tools_callback=None,
        tool_choice=None,
        reasoning_effort: Optional[str] = None,
    ) -> SubBaseModelOrIterable:
        ...

    async def async_send(
        self,
        user_message=None,
        system_message=None,
        origin_messages=None,
        chunk_callback=None,
        tools: Optional[list[Any]] = None,
        tools_callback=None,
        tool_choice=None,
        reasoning_effort: Optional[str] = None,
    ) -> Union[str, ToolCalls, SubBaseModelOrIterable]:
        """大模型统一发送与接收接口
        方式一(常用方式): 单一发送 user_message 并返回数据
        :param user_message: string, 要发送的消息
        :param system_message: string 可选, 附带角色定义, 例如: "你是一个工程师"
        :param tools: 数组 可选, 格式与原 openai 接口一致
        :param tool_choice: 'auto', 'required', 'none' 可选, 与原 openai 接口一致, 只有 tools 有值时才生效
        :param chunk_callback: 流式返回时, 每个 token 的回调处理函数, 支持一个string类型的参数
            chunk_callback(word)

        方式二: 发送原始 messages
        :param origin_messages: 数组, 格式与原 openai 接口一致 [{
            [{ 'role': 'system', content: 'xxx'}, { 'role': 'user', content: 'yyy' }, { 'role': 'assistant', content: 'xxx'}]
        :param chunk_callback: 同上
        :param tools: 同上
        :param tool_choice: 同上

        方式三: 在一二基础上组装返回对象
        :param origin_messages/user_message, 同上
        :param response_model: BaseModel 对象, 通常定义在 heracles.core.schema.models

        Return:
            未启用 func call 时, 大模型返回的响应数据, string
            启用 func call 时, 优先是 func call 函数调用信息( ToolCalls 对象 ), 数组( 与原 始 openai 接口一致 ), 如果无调用信息, 则返回正常响应数据
            response_model 有值的话, 返回 response_model 指定类的一个实例

        func call 的示例: [ChatCompletionMessageToolCall(function=Function(arguments='{"location": "Beijing", "unit": "celsius"}', name='get_weather'), id='call_833491af-f95b-4453-b71c-59345747e9b4', type='function')]

        """  # noqa
        stream = False
        messages = []
        res_tool_calls = []

        if user_message:
            if origin_messages:
                raise AgentLLMCallParamsException(
                    'user_message and origin_messages can not exist together'
                )
            if system_message:
                messages = [
                    {'role': 'system', 'content': system_message},
                    {'role': 'user', 'content': user_message},
                ]
            else:
                messages = [{'role': 'user', 'content': user_message}]
        elif origin_messages:
            messages = origin_messages
        else:
            raise AgentLLMCallParamsException(
                'user_message and origin_messages can not exist together or all is null'
            )

        if chunk_callback:
            stream = True

        if not messages:
            raise AgentLLMCallParamsException('messages can not be null')

        self._log_input(
            (
                f'last message: {truncate(str(messages[-1]), 500)},'
                f'stream: {stream},'
                f'tools: {tools}'
            )
        )
        res: Union[str, ToolCalls, SubBaseModelOrIterable] = ''

        _acompletion = self._acompletion()

        # ensure messages does not exceed a model's token limit
        messages = trim_messages(messages, model=self.model_real_name)

        if stream:
            if tools:
                chunk_response = await self.call_acompletion(
                    messages=messages, tools=tools, tool_choice=tool_choice, stream=True,
                    reasoning_effort=reasoning_effort, stream_options={'include_usage': True}
                )  # noqa
            else:
                chunk_response = await self.call_acompletion(messages=messages, stream=True,
                reasoning_effort=reasoning_effort, stream_options={"include_usage": True})

            res = ''
            # TODO: 处理 finish_reason, https://platform.openai.com/docs/guides/chat-completions/response-format
            async for chunk in chunk_response:
                if tool_calls := chunk.choices[0].delta.tool_calls:
                    for tool_call in tool_calls:
                        if tool_call.id:
                            res_tool_calls.append(tool_call)
                        if tool_call.function.name and tools_callback:
                            await tools_callback(tool_call, "start")
                        else:
                            res_tool_calls[-1].function.arguments += tool_call.function.arguments
                else:
                    word = chunk.choices[0].delta.content
                    if not word:
                        continue
                    res += word
                    if chunk_callback:
                        await chunk_callback(word)
            if res_tool_calls:
                res = ToolCalls(functions=res_tool_calls, message=res)
                if tools_callback:
                    for tool_call in res_tool_calls:
                        await tools_callback(tool_call, "end")
        else:
            full_response = ''
            if tools:
                response = await self.call_acompletion(
                    messages=messages, tools=tools, tool_choice=tool_choice, reasoning_effort=reasoning_effort
                )
            else:
                MAX_CONCATENATE_LOOP = 10
                concatenate_loop = 0
                while concatenate_loop < MAX_CONCATENATE_LOOP:
                    response = await self.call_acompletion(messages=messages, reasoning_effort=reasoning_effort)
                    chunk = str(response['choices'][0]['message']['content'])
                    # 当中间出现```时，要在输出过程中去掉
                    if concatenate_loop > 0 and chunk.startswith('```'):
                        chunk = '\n'.join(chunk.split('\n')[1:])
                    full_response += chunk
                    if response['choices'][0]['finish_reason'] != 'length':
                        logger.warning(f"finish reason: {response['choices'][0]['finish_reason']}")
                        break
                    concatenate_loop += 1
                    logger.warning(f"llm output is too long, try to concatenate: {concatenate_loop}")
                    messages.append({'role': 'assistant', 'content': chunk})
                    messages.append({'role': 'user', 'content': 'continue (without additional "```"(code block format) and new line)'})

            if res := response['choices'][0]['message']['tool_calls']:
                res = ToolCalls(
                    functions=res, message=response['choices'][0]['message']['content']
                )
                if tools_callback:
                    for tool_call in res.functions:
                        await tools_callback(tool_call, "end")
            else:
                res = full_response or response['choices'][0]['message']['content']
        self._log_output(res)
        return res

    def get_metadata(self):
        if get_env_var('LANGFUSE_HOST_OPTIONAL'):
            return self.langfuse_option.dict()
        else:
            return {}

    def call_acompletion(self, *args, **kwargs):
        _acompletion = self._acompletion()
        if not kwargs.get('tools'):
            # 没有tools, 只在系统提示词加缓存点
            kwargs['messages'] = self.add_cache_point(kwargs['messages'], system_only=True)
        else:
            kwargs['messages'] = self.add_cache_point(kwargs['messages'])
        try:
            response = _acompletion(*args, **kwargs)
            return response
        except Exception as e:
            self._log_input(f'Call llm error, {e}')
            logger.warning("Traceback: %s", traceback.format_exc())
            raise AgentLLMCallResultException('Call llm error, {e}') from None

    @property
    def model(self):
        return self._get_llm_config()['model']

    def _acompletion(self):
        config = self._get_llm_config()
        _acompletion = partial(
            litellm.acompletion,
            model=config['model'],
            api_key=config['api_key'],
            base_url=config['base_url'],
            metadata=self.get_metadata(),
            timeout=None
        )
        return _acompletion

    def _log_input(self, message):
        config = self._get_llm_config()
        model = config['model']
        if trace_id := self.langfuse_option.trace_id:
            logger.info(f'>>[{trace_id}][{self.langfuse_option.generation_name}]LLM({model}): {message}')
        else:
            logger.info(f'>>[unset]LLM({model}): {message}')

    def _log_output(self, message):
        config = self._get_llm_config()
        model = config['model']
        if trace_id := self.langfuse_option.trace_id:
            logger.info(f'<<[{trace_id}][{self.langfuse_option.generation_name}]LLM({model}): {message}')
        else:
            logger.info(f'<<[unset]LLM({model}): {message}')

    def add_cache_point(self, messages, system_only=False):
        """
        只考虑只有一个system role, 且没有图片的情况, 示例:
        {'role': 'system','content': ''}
        或
        {
            'role': 'system',
            'content': [{'type': 'text','text': ''},]
        }
        """
        system_prompt_dividing_line = '<---------------------------above_system_prompt_cached------------------------>'
        # tool call时会引用同一个messages导致重复打缓存点,先清除
        for msg in messages:
            if isinstance(msg['content'], str):
                msg.pop('cache_control', None)
            elif isinstance(msg['content'], list):
                for item in msg['content']:
                    if isinstance(item, dict):
                        item.pop('cache_control', None)

        # 统一转换成system和user消息成字典格式
        system_role_count = 0
        for msg in messages:
            if msg['role'] not in ('system', 'user'):
                continue
            if msg['role'] == 'system':
                system_role_count += 1
                if system_role_count > 1:
                    raise AgentRunException('Found multiple system role in message.')
            if isinstance(msg['content'], str):
                msg['content'] = [{'type': 'text', 'text': msg['content']}]

        # 用system文本块的分割线把提示词拆成多块
        total_cache_points = 4
        for msg in messages:
            if msg['role'] != 'system':
                continue
            processed_content = []
            for item in msg['content']:
                if item['type'] != 'text':
                    raise AgentRunException('System prompt contains non-text message.')
                for chunk in item['text'].split(system_prompt_dividing_line):
                    if not chunk.strip():
                        continue
                    processed_content.append({'type': 'text', 'text': chunk})
            msg['content'] = processed_content
            if len(msg['content']) == 1:
                msg['content'][0]['cache_control'] = {'type': 'ephemeral'}
                total_cache_points -= 1
            elif len(msg['content']) > 1:
                msg['content'][-2]['cache_control'] = {'type': 'ephemeral'}
                total_cache_points -= 1
        if system_only:
            return messages
        if total_cache_points < 2:
            logger.warning(f'Maybe not enought total_cache_points for user messages, count {total_cache_points}')
        # 读取时需要有和写入时相同位置的缓存标记才能触发, 需要把上次写入的位置也做标记
        for msg in reversed(messages):
            if total_cache_points < 1:
                break
            if not msg['content']:
                continue
            if msg['role'] == 'user':
                msg['content'][-1]['cache_control'] = {'type': 'ephemeral'}
                total_cache_points -= 1
            elif msg['role'] == 'tool':
                msg['cache_control'] = {'type': 'ephemeral'}
                total_cache_points -= 1
        return messages
