import asyncio
from typing import Any, Dict
from functools import partial

from heracles.core.exceptions import AgentRunException

# 这里存储的只是 tests/xxx, llm_tests/ 所使用到的 "系统级" 类型, 不要在这里放其他无关内容
class Context:
    """ 测试用例上下文结构, 用于 conftest 中清理数据
    """
    def __init__(self):
        self._data: Dict[str, Any] = {}

    def __getattr__(self, name: str) -> Any:
        return self._data.get(name)

    def __setattr__(self, name: str, value: Any) -> None:
        if name == '_data':
            super().__setattr__(name, value)
        else:
            self._data[name] = value

class MockSocketIOServer:
    """ SocketIO 的 Mocker 对象
    """
    def __init__(self):
        self.session = {}

    async def enter_room(self, sid, room_name):
        print(f"I enter room: {sid}, {room_name}")

    async def leave_room(self, sid, room_name):
        print(f"I leave room: {sid}, {room_name}")


    async def emit(self, event_name, data=None, to=None, skip_sid=None):
        print(f"emit called: {event_name}, {data}, {to}")
        if event_name == 'errorHandler':
            raise AgentRunException(f'errorHandler found: {data}')

    async def save_session(self, sid, session_dict):
        self.session = session_dict

    async def get_session(self, sid):
        return self.session

class MockSocketIOClient:
    """ SocketIO 的 Mocker Client 对象
    """
    def __init__(self, *args, **kwargs):
        self.handlers: dict[str, Any] = {'/': {}}
        self.connected = False
        self.future = None

    async def set_wait_condition(self, event_name, data):
        print(f'set_wait_condition: {event_name}, {data}')
        loop = asyncio.get_running_loop()
        self.future = loop.create_future()
        self.wait_event_name = event_name
        self.wait_data = data
        print("wait_condition")
        # 超时报错
        await asyncio.wait_for(self.future, 60)
        print("wait_condition ok")

    def clear_wait_condition(self):  # pragma: no cover
        self.future = None
        self.wait_event_name = None
        self.wait_data = None

    async def connect(self, *args, **kwargs):
        print(f"I connect to {args}")
        self.connected = True
        await asyncio.create_task(self.send_back_success_events())

    async def disconnect(self):
        self.connected = False
        print("disconnect called")

    async def shutdown(self):
        self.connected = False
        print("shutdown called")

    async def dispatch(self, event_name, data=None):
        print(f"dispatch called: {event_name}, {data}")
        event_callback = self.handlers['/'].get(event_name)
        if not event_callback:  # pragma: no cover
            # 找到 any 事件
            event_callback = self.handlers['/'].get('*')
            event_callback = partial(event_callback, event_name)
        # 还是没有, 就报错
        if not event_callback:  # pragma: no cover
            raise ValueError('must need an event callback')
        if asyncio.iscoroutinefunction(event_callback):
            if event_name in ['connect', 'disconnect']:
                await event_callback()
            else:
                await event_callback(data)
        else:
            if event_name in ['connect', 'disconnect']:
                await event_callback()
            else:
                event_callback(data)

    async def emit(self, event_name, data=None, callback=None):
        print(f"emit called: {event_name}, {data}")
        await asyncio.sleep(1)

        ret: Any = True
        if event_name == '_fail':
            ret = {'status': 'fail', 'data': '_fail'}
        elif event_name == '_ok':
            ret = {'status': 'ok', 'data': '_ok'}

        if callback:
            await callback(ret)

        if self.future and event_name == self.wait_event_name and data == self.wait_data:
            if not self.future.done():
                self.future.set_result(True)

    def on(self, event_name: str, event_callback):
        print(f'on event: {event_name}')
        self.handlers['/'][event_name] = event_callback

    async def send_back_success_events(self):
        await asyncio.sleep(1)
        await self.dispatch('authAck', {'result': True})
        await asyncio.sleep(1)
        await self.dispatch(
            'syncPlaygroundInfo',
            {
                'playgroundIDEServerStatus': 'SYNCED',
                'status': 'ACTIVE',
                'runStatus': 'RUNNING',
                'ragStatus': 'RagIndexFinish',
                'language': 'java',
                'openedTerminalList': [{'terminalId': 'test', 'terminalType': 'aiAgent', 'value': 'open'}],
                'multiTerminalStatus': [{'terminalId': 'test', 'terminalType': 'aiAgent', 'value': 'running'}],
                'multiTerminalHistory': [{'terminalId': 'test', 'terminalType': 'aiAgent', 'value': ''}]
            },
        )
        await asyncio.sleep(1)
        await self.dispatch('active', {'success': True})
        await asyncio.sleep(1)
        await self.dispatch(
            'multiTerminalCmdReply', {'terminalId': 'test', 'cmd': 'open', 'terminalType': 'aiAgent'}
        )
