import asyncio
import json

from statemachine import StateMachine, State  # type: ignore

from heracles.core.exceptions import AgentRunException
from heracles.core.utils.redis_cache import redis_cache
from heracles.core.logger import heracles_logger as logger

class TaskState(StateMachine):
    inited = State(initial=True)
    planning = State()
    working = State()
    done = State()
    pausing = State()
    canceled = State()
    appended = State()

    plan_task = inited.to(planning)
    start_task = planning.to(working) | pausing.to(working) | appended.to(working)
    end_task = working.to(done)
    pause_task = working.to(pausing)
    resume_task = pausing.to(working)
    append_task = done.to(appended) | canceled.to(appended) | pausing.to(appended)
    append_back_task = appended.to(done)
    cancel_task = pausing.to(canceled) | working.to(canceled)
    rerun_task_action = done.to(working)

    cycle = (
        inited.to(planning) |
        planning.to(working) |
        working.to(done) |
        done.to(appended)
    )

    def __init__(self, trigger_event=None):
        super().__init__()
        self.trigger_event = trigger_event
        self.appended_count = 0
        self.appended_expansion_turn = 1 # 追加步骤达到上限时, 用户付费解除限制，轮次加一
        self.appended_limit = 10
        self.playground_id = None

    @property
    def state(self) -> str:
        return self.current_state.id

    @append_task.on
    def add_appended_count(self):
        self.appended_count += 1

    @append_task.validators
    def validate_appended_limit(self):
        if self.appended_count >= self.appended_limit:
            raise AgentRunException(f"Failed to append more task steps, reached maximum limit of {self.appended_limit} times")

    @append_back_task.on
    def reduce_appended_count(self):  # pragma: no cover
        self.appended_count -= 1

    def can_add_step(self):
        return any([
            self.done.is_active,
            self.canceled.is_active,
            self.planning.is_active,
            self.appended.is_active,
            self.pausing.is_active
        ])

    def can_add_action(self):
        return self.can_add_step()

    def can_rerun_action(self):
        return self.done.is_active or self.canceled.is_active

    def can_modify_step(self):
        return self.planning.is_active or self.appended.is_active

    def can_modify_action(self):
        return not self.inited.is_active and not self.working.is_active

    def after_transition(self, state):
        if self.is_enable_save():
            self.save()

        if self.trigger_event:
            if asyncio.iscoroutinefunction(self.trigger_event):
                asyncio.create_task(self.trigger_event(state.id))
            else:
                self.trigger_event(state.id)

    def enable_autosave(self, playground_id: str):
        logger.debug(f"enable_autosave task_state: {playground_id}")
        self.playground_id = playground_id
        # 只在其他特殊状态初始化状保存
        if self.current_state != self.inited:
            self.save()

    def is_enable_save(self):
        return self.playground_id is not None

    def save(self):
        redis_key = self._generate_redis_key()
        value = json.dumps(self.dict())
        logger.debug(f'[cache] save task_state key={redis_key} value={value}')
        redis_cache.set(redis_key, value)

    def delete(self):
        redis_key = self._generate_redis_key()
        redis_cache.delete(redis_key)

    def set_state(self, state):
        """ 设定状态
        用法一: 传状态机，task_state.set_state(task_state.planning)
        用法二: 传字符串，task_state.set_state('planning')
        """
        if isinstance(state, str):
            state = getattr(self, state)
        self.current_state = state

    def set_appended_count(self, count: int):  # pragma: no cover
        self.appended_count = count

    def set_appended_expansion_turn(self, appended_expansion_turn: int):
        self.appended_expansion_turn = appended_expansion_turn

    def reset(self):
        """重置当前agent和action, 包括任务状态"""
        self.current_state = self.inited
        self.appended_count = 0

    def dict(self):
        return {
            'state': self.current_state.id,
            'appended_count': self.appended_count,
            'appended_expansion_turn': self.appended_expansion_turn
        }

    def _generate_redis_key(self):
        if not self.playground_id:  # pragma: no cover
            raise AgentRunException('Generate redis key failed, playground_id is null')
        return self._generate_redis_key_by_id(self.playground_id)

    @classmethod
    def load_dict_from_redis(cls, playground_id: str):
        redis_key = cls._generate_redis_key_by_id(playground_id)
        data = redis_cache.get(redis_key)
        logger.debug(f'[cache] load task_state: key={redis_key} data={data}')
        if not data:
            return None
        data = json.loads(data)
        return data

    @classmethod
    def _generate_redis_key_by_id(cls, playground_id: str):
        return f'task_state:{playground_id}'

    @classmethod
    def _generate_task_state_delete_all_key(cls):
        return 'task_state:*'
