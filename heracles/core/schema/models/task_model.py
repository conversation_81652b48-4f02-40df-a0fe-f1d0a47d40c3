from typing import List, Callable, Awaitable

from pydantic import BaseModel, Field, root_validator, validator
from heracles.core.schema.task import ActionType, CommandLifetimeType
from heracles.core.exceptions import IDEServerFileNotFoundError, IDEServerFileBinaryError

class TaskActionModel(BaseModel):
    """
    The action to be performed, including the path of the file to be performed, the command to be executed, etc.
    IMPORTANT: All action paths and command working directories are relative to the project root directory. This is a fixed behavior and cannot be modified by any directory-changing operations in other actions.
    """  # noqa
    action: ActionType = Field(description="MUST use value provided in `ActionType` strEnum (MUST not be Number), eg. 'add_file'")  # noqa
    path: str = Field(default='', description=(
        "the relative path of the file to be performed, always relative to the project root,"
        "available only when the action is file-related."
    ))
    target: str = Field(default='', description=(
        "the destination file/directory path, available only when the action is `move_file`/`move_directory`"
    ))
    command: str = Field(default='', description=(
        "the shell command to be executed, available only when the action is `run_command`\n"
        "IMPORTANT: prefer using command-line arguments and flags to minimize user interaction (e.g. use --yes, --force, --auto-confirm flags instead of interactive prompts)" # noqa
    ))
    lifetime: CommandLifetimeType = Field(default=CommandLifetimeType.NA, description=(
        "the time-consuming type of `command`, "
        "available only when the action is `run_command`"
    ))
    detailed_requirement: str = Field(default='', description=(
        "the detailed requirement and instructions of the action."
        "available ONLY when action is `modify_file` or `add_file`"
    ))
    references: list[str] = Field(default=[], description=(
        "relevant information of 'FileSnippets', 'KeyInformation', 'WebPages', 'Images' and 'Playbooks'."
        "available ONLY when action is `modify_file` or `add_file`"
    ))

    @validator('detailed_requirement')
    def validate_detailed_requirement_length(cls, v):
        if len(v) > 5000:
            raise ValueError("detailed_requirement cannot exceed 5000 characters")
        return v

    @validator('references')
    def validate_references_length(cls, v):
        for ref in v:
            if len(ref) > 1000:
                raise ValueError("each reference item cannot exceed 1000 characters")
        return v

    @root_validator(pre=True)
    def check_path(cls, values):
        if not isinstance(values, dict):
            raise ValueError("values must be a dict")
        action = values.get('action')
        path = values.get('path')
        if action in {ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE} and not path:
            raise ValueError("path cannot be empty when action is add/modify/delete")
        if path and path.startswith('/'):
            raise ValueError("path cannot start with '/'")
        return values

    @root_validator(pre=True)
    def check_command(cls, values):
        if not isinstance(values, dict):
            raise ValueError("values must be a dict")
        action = values.get('action')
        command = values.get('command')
        if action == ActionType.RUN_COMMAND and not command:
            raise ValueError("command cannot be empty when action is run_command")
        return values

    async def safe_duplicate(self, read_file: Callable[[str], Awaitable[str]], log_warn: Callable[[str], None]):
        if self.action == 'add_file':
            try:
                await read_file(self.path)
                log_warn(f"[TaskModel] 'add_file' {self.path} already exists, will change to 'modify_file'")
                self.action = ActionType.MODIFY_FILE
            except IDEServerFileNotFoundError:
                pass
        elif self.action == 'modify_file':
            try:
                await read_file(self.path)
            except IDEServerFileNotFoundError:
                log_warn(f"[TaskModel] 'modify_file' {self.path} not exists, will change to 'add_file'")
                self.action = ActionType.ADD_FILE
            except IDEServerFileBinaryError:
                log_warn(f"[TaskModel] 'modify_file' {self.path} is binary or too big, will remove this action")
                return None
        elif self.action == 'delete_file':
            try:
                await read_file(self.path)
            except IDEServerFileNotFoundError:
                log_warn(f"[TaskModel] 'delete_file' {self.path} not exists, will remove this action")
                return None
            except IDEServerFileBinaryError:
                log_warn(f"[TaskModel] 'delete_file' {self.path} is binary or too big, will skip validation and keep this action")
                pass
        elif self.action == 'run_command':
            if self.lifetime == 'persistent':
                log_warn(f"[TaskModel] Command {self.command} is a persistent command, but do not support")
                return None

        # 如果 reference 包含自身 path, 则跳过
        valid_references = []
        for reference in self.references:
            if self.path in reference:
                continue
            valid_references.append(reference)
        self.references = valid_references

        return TaskActionModel(
            action=self.action,
            path=self.path,
            target=self.target,
            command=self.command.strip(),
            lifetime=self.lifetime,
            detailed_requirement=self.detailed_requirement,
            references=self.references
        )

    def to_dict(self):
        """转成 Task 兼容结构的字典"""
        if self.action == 'run_command':
            return {
                'action': self.action,
                'action_object': {
                    'command': self.command,
                    'lifetime': self.lifetime
                }
            }
        else:
            return {
                'action': self.action,
                'action_object': {
                    'path': self.path,
                    'target': self.target,
                    'detailed_requirement': self.detailed_requirement,
                    'references': self.references
                }
            }

class TaskStepModel(BaseModel):
    title: str = Field(
        default='',
        description="summary of the actions to be performed, DON'T IGNORE, REQUIRED."
        "Use a complete sentence that briefly describes the action and specifies the context,"
        "e.g., 'Handle missing field validation error in user registration.'"
    )
    task_actions: List[TaskActionModel]

    @validator('task_actions')
    def check_task_actions(cls, task_actions):
        if not task_actions:
            raise ValueError("task_actions cannot be empty")
        file_path_modify_count_map: dict = {}
        for action in task_actions:
            if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE]:
                if action.path not in file_path_modify_count_map:
                    file_path_modify_count_map[action.path] = []
                file_path_modify_count_map[action.path].append(action)

        for path, actions in file_path_modify_count_map.items():
            if len(actions) > 1:
                for action in actions:
                    if action.action == ActionType.DELETE_FILE:
                        raise ValueError(f"File {path} cannot be deleted and modified by multiple actions")
        return task_actions

    async def safe_duplicate(self, read_file: Callable[[str], Awaitable[str]], log_warn: Callable[[str], None]):
        file_path_modify_count_map: dict = {}
        actions = []
        for action in self.task_actions:
            new_action = await action.safe_duplicate(read_file, log_warn)
            if new_action is None:
                continue
            if new_action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE]:
                if new_action.path not in file_path_modify_count_map:
                    file_path_modify_count_map[new_action.path] = []
                file_path_modify_count_map[action.path].append(new_action)
            actions.append(new_action)

        new_actions = []
        for action in actions:
            if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE] and len(file_path_modify_count_map[action.path]) > 1:
                if action == file_path_modify_count_map[action.path][-1]:
                    log_warn(f"File {action.path} is modified by multiple actions, will merge to the last one")
                    detailed_requirements = []
                    references = []
                    for dup_action in file_path_modify_count_map[action.path]:
                        detailed_requirements.append(dup_action.detailed_requirement)
                        references.extend(dup_action.references)
                    action.detailed_requirement = '\n'.join(detailed_requirements)
                    action.references = references
                    new_actions.append(action)
            else:
                new_actions.append(action)

        if not new_actions:
            return None
        return TaskStepModel(
            title=self.title,
            task_actions=new_actions
        )

    def to_dict(self):
        return {
            'title': self.title,
            'task_actions': [action.to_dict() for action in self.task_actions]
        }

class TaskModel(BaseModel):
    title: str = Field(description='summary title of the task, REQUIRED')
    description: str = Field(description='detail of the task to be performed')
    task_steps: List[TaskStepModel] = []

    @validator('task_steps')
    def steps_cannot_be_empty(cls, v):
        if not v:
            raise ValueError('steps cannot be empty')
        file_path_modify_count_map: dict = {}
        for step in v:
            for action in step.task_actions:
                if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE]:
                    if action.path not in file_path_modify_count_map:
                        file_path_modify_count_map[action.path] = []
                    file_path_modify_count_map[action.path].append(action)

        for path, actions in file_path_modify_count_map.items():
            if len(actions) > 1:
                for action in actions:
                    if action.action == ActionType.DELETE_FILE:
                        raise ValueError(f"File {path} cannot be deleted and modified by multiple actions")
        return v

    async def safe_duplicate(self, read_file: Callable[[str], Awaitable[str]], log_warn: Callable[[str], None]):
        steps = []
        file_path_modify_count_map: dict = {}
        for step in self.task_steps:
            new_step = await step.safe_duplicate(read_file, log_warn)
            if not new_step:
                continue
            for action in new_step.task_actions:
                if action.action in [ActionType.ADD_FILE, ActionType.MODIFY_FILE, ActionType.DELETE_FILE]:
                    if action.path not in file_path_modify_count_map:
                        file_path_modify_count_map[action.path] = []
                    file_path_modify_count_map[action.path].append(action)

            if not new_step.title:
                log_warn("Step has no title, will use task title as step title")
                new_step.title = self.title
            if new_step.task_actions:
                steps.append(new_step)

        # keep the last one if multiple actions are modified in the same file
        for path, actions in file_path_modify_count_map.items():
            if len(actions) > 1:
                log_warn(f"File {path} is modified by multiple actions across steps, will merge to the last one")
                detailed_requirements = []
                references = []
                for action in actions:
                    if action.action == ActionType.DELETE_FILE:
                        raise ValueError(f"File {path} cannot be deleted and modified by multiple actions")
                    detailed_requirements.append(action.detailed_requirement)
                    # unique references
                    references.extend([ref for ref in action.references if ref not in references])
                last_action = actions[-1]
                last_action.detailed_requirement = '\n'.join(detailed_requirements)
                last_action.references = references
                # remove actions other than the last one
                for action in actions[:-1]:
                    step_index = next((i for i, step in enumerate(steps) if action in step.task_actions), None)
                    if step_index is not None:
                        steps[step_index].task_actions.remove(action)
        # remove steps with no actions
        new_steps = []
        for step in steps:
            if step.task_actions:
                new_steps.append(step)
        if not new_steps:
            return None
        return TaskModel(
            title=self.title,
            description=self.description,
            task_steps=new_steps
        )

    def to_dict(self):
        return {
            'title': self.title,
            'description': self.description,
            'task_steps': [step.to_dict() for step in self.task_steps]
        }
