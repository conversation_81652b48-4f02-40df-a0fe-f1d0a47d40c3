from typing import Dict, Any
import asyncio
from jinja2 import Environment, Template
from .prompt_builder_templates import (
    FILE_TREE,
    PROJECT_KNOWLEDGE,
    FILE_SNIPPETS,
    PLAYBOOKS,
    PLAYBOOK_LIST,
    ERRORS,
    TASK,
    IN_PROGRESS_TASK_ACTION,
    RECENT_FILE_CHANGES,
    REFERENCES,
    WEB_PAGES,
    CLACKY_RULES,
    IMAGES,
    ERROR_REPORT,
    ENVIRONMENT_LOG,
    UI_REPORT,
)


class AsyncTemplate:
    def __init__(self, template: Template):
        self.template = template

    async def render(self, *args: Any, **kwargs: Any) -> str:
        return await asyncio.to_thread(self.template.render, *args, **kwargs)


class TemplateRegistry:
    """模板注册器，用于管理所有模板"""

    def __init__(self):
        self._env = Environment()
        self._env.globals['isinstance'] = isinstance
        self._templates: Dict[str, AsyncTemplate] = {}

    def register(self, name: str, template_str: str) -> None:
        """注册模板字符串并创建AsyncTemplate对象"""
        self._templates[name] = AsyncTemplate(self._env.from_string(template_str))

    def get(self, name: str) -> AsyncTemplate:
        template = self._templates.get(name)
        if template is None:
            raise ValueError(f"AsyncTemplate '{name}' not found")

        return template


# 创建全局模板注册器实例
GLOBAL_PRE_PROMPT = TemplateRegistry()

# 注册所有模板
GLOBAL_PRE_PROMPT.register('FILE_TREE', FILE_TREE)
GLOBAL_PRE_PROMPT.register('PROJECT_KNOWLEDGE', PROJECT_KNOWLEDGE)
GLOBAL_PRE_PROMPT.register('FILE_SNIPPETS', FILE_SNIPPETS)
GLOBAL_PRE_PROMPT.register('PLAYBOOKS', PLAYBOOKS)
GLOBAL_PRE_PROMPT.register('PLAYBOOK_LIST', PLAYBOOK_LIST)
GLOBAL_PRE_PROMPT.register('ERRORS', ERRORS)
GLOBAL_PRE_PROMPT.register('TASK', TASK)
GLOBAL_PRE_PROMPT.register('IN_PROGRESS_TASK_ACTION', IN_PROGRESS_TASK_ACTION)
GLOBAL_PRE_PROMPT.register('RECENT_FILE_CHANGES', RECENT_FILE_CHANGES)
GLOBAL_PRE_PROMPT.register('REFERENCES', REFERENCES)
GLOBAL_PRE_PROMPT.register('WEB_PAGES', WEB_PAGES)
GLOBAL_PRE_PROMPT.register('CLACKY_RULES', CLACKY_RULES)
GLOBAL_PRE_PROMPT.register('IMAGES', IMAGES)
GLOBAL_PRE_PROMPT.register('ERROR_REPORT', ERROR_REPORT)
GLOBAL_PRE_PROMPT.register('ENVIRONMENT_LOG', ENVIRONMENT_LOG)
GLOBAL_PRE_PROMPT.register('UI_REPORT', UI_REPORT)
