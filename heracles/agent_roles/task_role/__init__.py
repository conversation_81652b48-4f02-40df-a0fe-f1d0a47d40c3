import traceback
import async<PERSON>
from typing import Callable

from heracles.core.schema import LLMAbilityType
from heracles.core.schema.task import Task, TaskStep, TaskAction
from heracles.core.schema.task_state import TaskState
from heracles.core.exceptions import AgentRunException

from heracles.agent_roles.role_base import RoleBase
from heracles.agent_roles.role_action import AgentRoleTaskActionFinishAction, AgentRoleTaskActionAbandonAction

class TaskRole(RoleBase):
    """ 计划执行者, 这个 Role 不需要真实的 LLM
    """
    def __init__(self, workspace):
        super().__init__(workspace)
        self.task_state = TaskState(self.trigger_task_state_updated)
        self.logger.debug(f"enable_autosave task_state: {self.workspace.playground.playground_id}")
        self.task_state.enable_autosave(self.workspace.playground.playground_id)

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

    async def run(
        self,
        action_executor: Callable,
        delay_time: int = 3,
        has_task_to_continue = False
    ):
        self.logger.info("begin run task")
        if not self.workspace.task:
            raise AgentRunException("task_role run error: no task here")
        if not self.task_state.working.is_active:
            raise AgentRunException("task_role run error: task_state must be working")
        while True:
            task_action = self.workspace.task.get_next_runnable_action()
            if not task_action:
                self.logger.info("There are no more task actions to execute.")
                break
            if self.task_state.pausing.is_active:
                self.logger.info("Task paused by user, stopping execution")
                break
            if self.task_state.canceled.is_active:
                self.logger.info("Task canceled by user, stopping execution")
                break

            await self.run_action(action_executor, task_action)
            if delay_time > 0:
                await asyncio.sleep(delay_time)
        # run finished, send task updated message
        if not has_task_to_continue:
            self.task_state.end_task()
        await self.trigger_task_updated(self.workspace.task)

    async def run_action(self, action_executor: Callable, task_action: TaskAction, is_rerun: bool = False):
        try:
            task_action.set_status_doing()
            await self.trigger_task_action_updated(task_action)
            res = await action_executor(task_action=task_action, is_rerun=is_rerun)
            if isinstance(res, AgentRoleTaskActionFinishAction):
                self.logger.info(f"run task_action {task_action.id} completed")
                task_action.set_status_completed(res.result)
            elif isinstance(res, AgentRoleTaskActionAbandonAction):
                self.logger.warning(f"run task_action {task_action.id} abandoned, reason: {res.result}")
                task_action.set_status_abandoned(res.result)
            else:
                self.logger.warning("Warning: run task_action {task_action.id} return non-support action")
                task_action.set_status_completed(str(res))
        except Exception as e:
            self.logger.warning(f"run task_action {task_action.id} exception \nTraceback:{traceback.format_exc()}")
            task_action.set_status_abandoned(f"error: {str(e)}")
        # send message to user
        await self.trigger_task_action_updated(task_action)

    async def trigger_task_planned(self, task: Task):
        await self.workspace.trigger('task_planned', task.dict())

    async def trigger_task_action_updated(self, task_action: TaskAction):
        await self.workspace.trigger('task_action_updated', task_action.dict())

    async def trigger_task_updated(self, task: Task):
        await self.workspace.trigger('task_updated', task.dict())

    async def trigger_task_state_updated(self, task_state_type):
        await self.workspace.trigger('task_state_updated', task_state_type)

    def reset_state(self):
        self.task_state.reset()

    def reset_all(self):
        self.task_state.reset()
        self.workspace.task = None


__all__ = ['TaskRole', 'Task', 'TaskStep', 'TaskAction']
