{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["6239195"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import base64\n", "import sys\n", "\n", "\n", "# 读取 GIF 文件并转换为 base64\n", "# img_path = '/Users/<USER>/Downloads/large_image.gif'\n", "img_url = 'https://staging.app.clackyai.com/_next/image?url=https%3A%2F%2Fbackend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com%2Fimages%2F2025%2F05%2F30%2F0191e936-83d6-7908-b2f6-dfbc38a06664%2F%25E4%25BE%25BF%25E7%25AC%25BA.gif1748574523886&w=640&q=75'\n", "with open(img_path, \"rb\") as image_file:\n", "    encoded_string = base64.standard_b64encode(image_file.read()).decode('utf-8')\n", "\n", "# 如果需要在 HTML 中使用\n", "html_img_src = f\"data:image/gif;base64,{encoded_string}\"\n", "\n", "sys.getsizeof(html_img_src)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['http_proxy'] = 'http://127.0.0.1:1080'\n", "os.environ['https_proxy'] = 'http://127.0.0.1:1080'"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["6239173"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["import base64\n", "import httpx\n", "import sys\n", "\n", "# For base64-encoded images\n", "image1_url = \"https://staging.app.clackyai.com/_next/image?url=https%3A%2F%2Fbackend-staging-qwertyuiopas3456.s3-accelerate.amazonaws.com%2Fimages%2F2025%2F05%2F30%2F0191e936-83d6-7908-b2f6-dfbc38a06664%2F%25E4%25BE%25BF%25E7%25AC%25BA.gif1748574523886&w=640&q=75\"\n", "image1_media_type = \"image/gif\"\n", "image1_data = base64.standard_b64encode(httpx.get(image1_url).content).decode(\"utf-8\")\n", "sys.getsizeof(image1_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}