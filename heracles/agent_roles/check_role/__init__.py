import re
import time
import uuid
import asyncio
import yaml
from typing import Literal, Optional, Any

from heracles.core.utils import delayed_debouncer
from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema import LLMAbilityType, ProjectErrorMessage, LimitedStack
from heracles.core.schema.models import (
    TestCaseRuleModels,
    ErrorFoundModel,
    ErrorReportModel
)
from heracles.core.exceptions import AgentRunException
from .prompts import (
    SYSTEM_PROMPT,
    USER_PROMPT,
    ANALYZE_ERROR_PROMPT,
    ERROR_SUMMARY_SYSTEM_PROMPT,
    ERROR_SUMMARY_USER_PROMPT,
    REPORT_TEMPLATE
)
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.code_role.utils import precise_ratio
from heracles.core.utils import wait_for
from heracles.core.utils.llm_function import tool_call_to_argument_pair
from heracles.core.schema import FocusComponentType
from heracles.agent_roles.utils import async_sanitize_generated_json
from heracles.core.utils.string import remove_ansi_escape_sequences
from heracles.core.utils.template_registry import GLOBAL_PRE_PROMPT
from pydantic import ValidationError


class ObserverLog():
    type: Literal['terminal']
    lines: LimitedStack
    last_line_timestamp: float

    def __init__(self, type, max_lines: int = 5000):
        self.type = type
        self.lines = LimitedStack(max_lines)
        self.last_line_timestamp = time.time()

class CheckRole(RoleBase):
    """ 检查工作成果质量
    """
    # 匹配错误信息的正则表达式
    REGEX_ERR_STRING = r'error|not found|unexpected|undefined|invalid|unable|fail|denied|timeout|fatal|critical|exception|exit|trouble'

    def __init__(self, workspace):
        super().__init__(workspace)
        self.check_role_task_handle = None
        # 存放每个terminal对应日志的字典对象, key是terminal_id, value是log对象
        self.terminal_log_dict: dict[str, ObserverLog] = {}
        self.browser_screenshot: Optional[str] = None
        self.partial_report_str = ''
        self.error_report_id = None

    def get_llm_ability(self):
        return LLMAbilityType.NORMAL

    async def run(self):
        pass

    async def monitor_terminal_log(self, raw_value, terminal_id, terminal_type=None):
        """监控终端日志"""
        if terminal_type != 'goAgent':
            return
        if terminal_id not in self.terminal_log_dict:
            self.terminal_log_dict[terminal_id] = ObserverLog('terminal')
        lines = raw_value.split('\n')
        self.terminal_log_dict[terminal_id].lines.append_to_last_one(lines[0])
        for line in lines[1:]:
            self.terminal_log_dict[terminal_id].lines.push(line)
        self.terminal_log_dict[terminal_id].last_line_timestamp = time.time()

    async def _prepare_environment(self):
        """准备监听终端日志"""
        run_status = self.workspace.tools.run_status()
        if run_status == 'RUNNING':
            await self.workspace.tools.stop_project()
        self.workspace.observations.on_terminal(self.monitor_terminal_log)
        await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)

    async def _run_and_monitor_project(self):
        """运行并监控项目"""

        await self.workspace.trigger_general_loading(tool_name='Task', status='start')

        def check_run_finished_condition():
            has_http_ports = self.workspace.playground.ide_server_client.http_ports
            if has_http_ports:
                return True
            current_time = time.time()
            if not self.terminal_log_dict:
                return False
            for terminal_id in self.terminal_log_dict:
                # 前端项目第一次运行ready后需要编译, 最多等待10秒
                if current_time - self.terminal_log_dict[terminal_id].last_line_timestamp < 10:
                    return False
            return True

        await self.workspace.trigger_general_loading(tool_name='Task', status='Run project and monitor logs')
        self.terminal_log_dict.clear()
        await self.workspace.tools.run_project()

        try:
            # TODO: 后端给的建议是30秒太长了，需要优化
            await wait_for(check_run_finished_condition, timeout=30)
            # 如果是web项目, 端口开启后还要等前端加载完
            if self.workspace.playground.ide_server_client.http_ports:
                url = await self.workspace.tools.local_url()
                if url:
                    await self.workspace.tools.browser_goto(url)
        except Exception as e:
            self.logger.warning(f'wait_for run_project timeout, but we continue to check: {e}')

        await self.workspace.trigger_general_loading(tool_name='Task', status='end')

    async def get_lint_log(self, changed_files) -> str:
        try:
            lint_data: dict = await self.workspace.tools.lint_diagnostic(changed_files)
        except Exception as e:
            self.logger.warning(f"Failed to get lint data, using empty dict: {e}")
            lint_data = {}
        if lint_data.get('diagnostics'):
            return str(lint_data)
        else:
            return ''

    async def get_terminal_log(self) -> str:
        terminal_logs = []
        for terminal_id, log in self.terminal_log_dict.items():
            log_str = log.lines.to_string().strip()
            # 去掉颜色字符, 特殊字符串PaasNewLineSign, 统一换行符
            log_str = remove_ansi_escape_sequences(log_str).removeprefix('PaasNewLineSign').replace('\r\n', '\n').replace('\r', '\n')
            terminal_logs.append(f'terminal log of {terminal_id}:\n' + log_str + '\n')
        terminal_log = '\n'.join(terminal_logs)
        return terminal_log.strip()

    async def get_browser_log(self) -> str:
        if not self.workspace.playground.ide_server_client.http_ports:
            return ''
        try:
            browser_logs = await self.workspace.tools.browser_console_logs()
        except Exception as e:
            self.logger.warning(f"Failed to get browser logs, using empty list: {e}")
            browser_logs = []
        browser_log = '\n'.join(browser_logs)
        return browser_log.strip()

    async def get_browser_screenshot(self) -> str | None:
        if not self.workspace.playground.ide_server_client.http_ports:
            return None
        try:
            browser_screenshot = await self.workspace.tools.browser_screenshot()
        except Exception as e:
            self.logger.warning(f"Failed to get browser screenshot, using None: {e}")
            browser_screenshot = None
        return browser_screenshot

    async def _perform_checks(self):
        """执行检查"""
        await self.workspace.trigger_general_loading(tool_name='Task', status='start')
        await self.workspace.trigger_general_loading(tool_name='Task', status='Checking the logs for any error messagess')
        await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)

        try:
            changed_files = await self.workspace.tools.list_changed_files()
        except Exception as e:
            self.logger.warning(f"Failed to get changed files, using empty list: {e}")
            changed_files = []
        filtered_changed_files = []
        for file in changed_files:
            if file.endswith(('.ts', '.tsx', '.js', '.jsx', 'py', 'go')):
                filtered_changed_files.append(file)

        lint_log_task = asyncio.create_task(self.get_lint_log(filtered_changed_files))
        terminal_log_task = asyncio.create_task(self.get_terminal_log())
        browser_log_task = asyncio.create_task(self.get_browser_log())
        browser_screenshot_task = asyncio.create_task(self.get_browser_screenshot())

        lint_log, terminal_log, browser_log, browser_screenshot = await asyncio.gather(
            lint_log_task,
            terminal_log_task,
            browser_log_task,
            browser_screenshot_task,
            return_exceptions=True,
        )
        await self.workspace.trigger_general_loading(tool_name='Task', status='end')

        return {
            'lint_log': lint_log if isinstance(lint_log, str) else '',
            'terminal_log': terminal_log if isinstance(terminal_log, str) else '',
            'browser_log': browser_log if isinstance(browser_log, str) else '',
            'browser_screenshot': browser_screenshot if isinstance(browser_screenshot, str) else None,
        }

    async def partial_report_callback(self, report_str: str):
        self.partial_report_str += report_str
        report_json = await async_sanitize_generated_json(self.partial_report_str)
        if report_json:
            try:
                result: dict[str, Any] = {}
                report_model = ErrorReportModel(**report_json)
                result['id'] = self.error_report_id
                result['message'] = report_model.summary
                result['error_list'] = []
                for error in report_model.errors:
                    error_dict = error.model_dump(exclude={'title'})
                    result['error_list'].append(
                        {
                            'title': error.title,
                            'content': yaml.dump(error_dict, default_flow_style=False, indent=2, width=80, allow_unicode=True),
                        }
                    )
                await self.workspace.auto_fix.trigger_auto_fix_errors_updated(result)
            except (ValidationError, TypeError):
                pass

    async def _generate_report(self, check_data):
        """生成报告"""

        await self.workspace.trigger_general_loading(tool_name='Task', status='start')
        await self.workspace.trigger_general_loading(tool_name='Task', status='Generating report')
        await self.workspace.playground.ide_server_client.following_focus_component(FocusComponentType.TERMINAL)
        browser_screenshot = check_data['browser_screenshot']

        env_log = await GLOBAL_PRE_PROMPT.get('ENVIRONMENT_LOG').render(environment_log=check_data)
        self.system_prompt = await PromptBuilder(ERROR_SUMMARY_SYSTEM_PROMPT, self.workspace).format()
        user_prompt = await PromptBuilder(ERROR_SUMMARY_USER_PROMPT, self.workspace).format(env_log=env_log)

        self.partial_report_str = ''
        self.error_report_id = str(uuid.uuid4())
        error_report = await self.aask(
            user_prompt,
            images=[browser_screenshot] if browser_screenshot else None,
            tools=['read_playbook', 'read_file', 'search_codebase'],
            chunk_callback=self.partial_report_callback,
            post_text_validate_model=ErrorReportModel
        )

        issues = []
        if error_report.errors:
            error_report_text = await GLOBAL_PRE_PROMPT.get('ERROR_REPORT').render(error_report=error_report)
            issues.append(error_report_text)

        report = '\n\n'.join(issues)
        await self.workspace.trigger_general_loading(tool_name='Task', status='end')
        return {
            'text': REPORT_TEMPLATE.format(report=report) if report else '',
            'image': browser_screenshot,
            'error_report': error_report.model_dump(),
        }

    async def check_errors(self):
        """
        1. 准备环境
        2. 运行并监控项目
        3. 执行检查
        4. 生成错误报告
        """
        await self._prepare_environment()
        await self._run_and_monitor_project()
        check_data = await self._perform_checks()
        self.logger.info(f'check role check_data: {str(check_data)}')
        report = await self._generate_report(check_data)
        self.logger.info(f'check role report: {str(report)}')
        return report

    async def clear_logs(self):
        for _, log in self.terminal_log_dict.items():
            log.lines.clear()

    async def check_task(self):
        if not self.workspace.task:
            raise AgentRunException("check task failed, reason: no task here")
        await self.workspace.trigger('message', "I will check the task result now, including code changes and command execution.\r\n")
        self.workspace.smart_detect.errors = []
        self.system_prompt = SYSTEM_PROMPT
        # TODO: 需要更可靠的获取 changed_files
        # changed_files = await self.workspace.tools.make_commit_file_list()
        user_message_builder = PromptBuilder(USER_PROMPT, self.workspace)
        message = await user_message_builder.format(CHANGED_FILES="")
        res = await self.aask(
            message,
            tools=["read_file", "run_cmd", "check_path_exists"],
            response_model=TestCaseRuleModels,
            chunk_callback=self.trigger_chunk_message,
            tools_callback=self.trigger_tool_callback
        )
        if isinstance(res, TestCaseRuleModels) and res.rules:
            errors = []
            for rule in res.rules:
                await self.workspace.trigger('chunk_message', f"- **{rule.title}**: {'✅ Pass' if rule.score == 1 else '❌ Fail'}\n")
                if rule.score == 0:
                    error = ProjectErrorMessage(title=rule.ref_id, content=f"{rule.title} failed", ref_id=rule.ref_id)
                    errors.append(error)
                    await self.workspace.trigger('message_suggestion', error.generate_suggestion())
            if res.suggestions:
                await self.workspace.trigger('chunk_message', f"\nSuggestions: {res.suggestions}\n")
            if errors:
                self.workspace.smart_detect.errors.extend(errors)
            await self.workspace.trigger('chunk_message', "\r\n")
            return errors
        else:
            raise AgentRunException(f"check task failed, reason: {res}")

    async def check_task_with_rules(self):
        if not self.workspace.task:
            raise AgentRunException("check task failed, reason: no task here")

        self.system_prompt = SYSTEM_PROMPT
        # TODO: 需要更可靠的获取 changed_files
        # changed_files = await self.workspace.git_operator._make_commit_file_list_message()
        user_message_builder = PromptBuilder(USER_PROMPT, self.workspace)
        message = await user_message_builder.format(CHANGED_FILES="")
        res = await self.aask(message, tools=["read_file", "run_cmd", "check_path_exists"], response_model=TestCaseRuleModels)
        return res

    async def run_and_check_project(self):
        run_status = self.workspace.tools.run_status()
        if run_status == 'RUNNING':
            await self.workspace.trigger('message', (
                "I am monitoring terminal output. If there are any errors, I will notify you."
            ))
        else:
            await self.workspace.trigger('message', (
                "I am monitoring terminal output. I will start the project first."
            ))
            # FIXME: 这里需要优化，run_project 需要等待 prompt 可用
            await self.workspace.tools.run_project()

        wait_seconds = 0

        def clacky_terminal_log_has_output():
            nonlocal wait_seconds
            wait_seconds += 1
            for _, log in self.terminal_log_dict.items():
                if len(log.lines.items) > 0:
                    return True
            return False

        self.logger.warning('-> wait for console log appeared...')
        await wait_for(clacky_terminal_log_has_output, interval=1)
        self.logger.warning(f'-> clacky terminal log has output (waited {wait_seconds} seconds), start analysis...')
        for _, log in self.terminal_log_dict.items():
            await self.analyze_log(log)

    async def on_terminal(self, line, terminal_id):
        if terminal_id not in self.terminal_log_dict:
            self.terminal_log_dict[terminal_id] = ObserverLog('terminal')
        await self._process_line(line, self.terminal_log_dict[terminal_id])

    async def _process_line(self, line, log: ObserverLog):
        current_time = time.time()
        # 如果没有检测到错误，且距离上次输出超过 10 秒就清空缓存：用于分割正常的命令输出
        if current_time - log.last_line_timestamp > 10:
            log.lines.clear()

        log.lines.push(line)
        log.last_line_timestamp = current_time

        # 首次检测到错误匹配，会触发启动处理错误的防抖延时任务
        matched = re.search(self.REGEX_ERR_STRING, line, re.IGNORECASE)
        if matched:
            await self.handle_error(log)

    async def analyze_log(self, log: ObserverLog):
        """ 直接分析 log 是否包含错误信息 """

        for line in log.lines.items:
            matched = re.search(self.REGEX_ERR_STRING, line, re.IGNORECASE)
            if matched:
                self.logger.warning(f'-> error found: \n{line}')
                await self.handle_error(log)
                break

    @delayed_debouncer(delay=2, task_handle_name="check_role_task_handle", error_callback_name="trigger_error_handler")
    async def handle_error(self, log):
        error_result = await self.check_if_new_error(log)
        if not error_result.is_new_error:
            return
        self.logger.warning(f'-> LLM handle_error: {error_result.title}')
        error_message_obj = ProjectErrorMessage(
            title=error_result.title,
            ref_id=f"{log.type}/{uuid.uuid4().hex[:8]}",
            content=log.lines.to_string()
        )
        if len(self.workspace.smart_detect.errors) >= 3:
            self.workspace.smart_detect.errors.pop(0)
        self.workspace.smart_detect.errors.append(error_message_obj)
        log.lines.clear()

        if self.workspace.smart_detect.status == 'monitoring_errors':
            await self.workspace.trigger('message_suggestion', error_message_obj.generate_suggestion())

    async def check_if_new_error(self, log):
        content = log.lines.to_string()
        for error in self.workspace.smart_detect.errors:
            similarity = precise_ratio(content, error.content)
            if similarity > 0.8:
                self.logger.warning(f"new error is duplicate with history error: {error.title}")
                return ErrorFoundModel(is_new_error=False, title=error.title)
        message_builder = PromptBuilder(ANALYZE_ERROR_PROMPT, self.workspace)
        message_builder.errors = self.workspace.smart_detect.errors
        message = await message_builder.format(content=content)
        result = await self.aask(message, response_model=ErrorFoundModel)
        return result

    async def trigger_error_handler(self, message):
        await self.workspace.playground.trigger_error_handler(message)

    async def trigger_tool_callback(self, tool_call, status):
        try:
            if status == 'end':
                tool_name, value = tool_call_to_argument_pair(tool_call)
                await self.workspace.trigger('chunk_message', f"\n> {tool_name}: `{value}`\n")
        except Exception as e:
            self.logger.error(f"Error in trigger_tool_callback: {e}, tool_call: {tool_call}")

    async def trigger_chunk_message(self, word: str):
        await self.workspace.trigger('chunk_message', word)
