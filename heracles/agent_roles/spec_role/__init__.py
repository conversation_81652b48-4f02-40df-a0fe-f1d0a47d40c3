from heracles.agent_roles.role_base import RoleBase
from heracles.core.schema import LLMAbilityType
from heracles.core.schema.spec import Spec
from heracles.core.utils.context_prompt_builder import PromptBuilder
from heracles.agent_roles.utils import extract_user_message, llm_ability
from heracles.core.exceptions import AgentRunException

from .create_test_spec import create_test_spec
from .prompts import SPEC_USER_PROMPT, SYSTEM_PROMPT, REFINE_PROMPT_SYS__NEW_PROJECT, REFINE_PROMPT_USER__NEW_PROJECT


class SpecRole(RoleBase):
    """澄清需求，制定 Spec"""

    def __init__(self, workspace):
        super().__init__(workspace)

    def get_llm_ability(self):
        return LLMAbilityType.FAST

    async def run(self, goal: str, goal_detail: str):
        try:
            await self.trigger_spec_generating("start")
            if goal.startswith("test:"):
                res = create_test_spec(goal, goal_detail)
            else:
                res = await self.make_spec(goal, goal_detail)
        finally:
            await self.trigger_spec_generating("end")
        return res

    async def make_spec(self, goal, goal_detail):
        if goal == "Initialize the Development Environment":
            return Spec(
                goal="Initialize the Development Environment",
                goal_detail="Follow instructions provided to initialize the development environment in Clacky",
                task_scale="micro",
                current_list=[
                    "The current project development environment has not yet completed the initialization steps",
                    "not yet installing project dependencies",
                    "not yet configuring project run_command and parameters",
                    "missing environment variable configuration files",
                    "missing .gitignore file and configuring the files to be ignored"
                ],
                proposed_list=[
                    'If package manager used in project, install it and use it to install dependencies for development. ',
                    'Modify the `.1024` file to configure the project and set the run_command, dependency_command and linter_config. ',
                    'Always use global effective version of command, e.g. `npm install -g`, `npm alias`. ',
                    'Configure .gitignore file, excluding specified files and directories. ',
                    'If database, redis used and `*.example` file exists, create env configuration files to add provided variables. '  # noqa
                ],
                proposed_filechange_list=['.1024', '.gitignore'],
                suggested_branch="chore/init-clacky-env",
                suggested_name="init-env"
            )
        system_prompt_builder = PromptBuilder(SYSTEM_PROMPT, self.workspace)
        self.system_prompt = await system_prompt_builder.format()
        user_prompt_builder = PromptBuilder(SPEC_USER_PROMPT, self.workspace)
        message = await user_prompt_builder.format(GOAL=goal, GOAL_DETAIL=goal_detail)
        res = await self.aask(message, response_model=Spec)

        # 保留原有的 goal_detail 值，如果存在且非空
        if goal_detail:
            res.goal_detail = goal_detail

        if len(res.proposed_filechange_list) <= 3:
            res.task_scale = "micro"
        elif len(res.proposed_filechange_list) <= 10:
            res.task_scale = "small"
        elif len(res.proposed_filechange_list) <= 20:
            res.task_scale = "medium"
        return res

    async def trigger_spec_generating(self, status):
        await self.workspace.trigger(
            "tool_call_status_updated",
            {'tool_id': "generate-spec", 'tool_name': "Spec", 'status': status}
        )

    @llm_ability(LLMAbilityType.FAST)
    async def refine_prompt(self, user_input: str, scene: str):
        """润色提示词"""
        if scene == 'new_project':
            system_prompt = REFINE_PROMPT_SYS__NEW_PROJECT
            user_prompt = REFINE_PROMPT_USER__NEW_PROJECT
        else:
            raise AgentRunException(f'refine_prompt: Invalid scene: {scene}')

        self.system_prompt = await PromptBuilder(system_prompt, self.workspace).format()

        user_prompt_builder = PromptBuilder(user_prompt, self.workspace)
        user_message_cleaned, content_dict = await extract_user_message(user_input, self.workspace)

        if content_dict['images']:
            user_prompt_builder.images = content_dict['images']
        if content_dict['webpages']:
            user_prompt_builder.webpages = content_dict['webpages']
        user_message = await user_prompt_builder.format(USER_MESSAGE=user_message_cleaned)

        res = await self.aask(user_message)
        return res
