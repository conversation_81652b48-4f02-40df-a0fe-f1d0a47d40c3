import re
import difflib
from typing import List, TypedDict, Literal
from Levenshtein import distance  # type: ignore
from heracles.core.exceptions import AgentRunException
from heracles.core.schema import FileSnippet
from heracles.core.logger import heracles_logger as logger
import tempfile
import os
from pathlib import Path
import json
import asyncio

class CodeSegmentPart(TypedDict):
    content: str
    row_start: int
    row_end: int
    similarity: float

def precise_ratio(s1: str, s2: str) -> float:
    max_len = max(len(s1), len(s2))
    if max_len == 0:  # 防止除以 0 的情况
        return 1.0 if s1 == s2 else 0.0
    return (1 - distance(s1, s2) / max_len)

def generate_file_diff(original_file_content: str, new_file_content: str) -> str:
    return '\n'.join(
        list(
            difflib.unified_diff(
                original_file_content.splitlines(),
                new_file_content.splitlines(),
                fromfile='original',
                tofile='modified',
                lineterm=''
            )
        )
    )

def preprocess_code_segment(segment: str) -> List[CodeSegmentPart]:
    processed_parts: List[CodeSegmentPart] = []
    parts = re.split(r'[ \t]*\.\.\.[ \t]*', segment)
    for part in parts:
        part = part.strip('\n')
        if part == '':
            continue
        processed_parts.append({
            'content': part,
            'row_start': -1,
            'row_end': -1,
            'similarity': 0.00
        })
    return processed_parts

def match_best_code_segment(file_content: str, code_segment: str, threshold: float = 70) -> FileSnippet:
    """
    在文件内容中匹配最佳代码段。行号从 0 开始。

    :param file_content: 文件内容
    :param code_segment: 代码段
    :param threshold: 相似度阈值
    :return: 匹配结果
    """
    # 读取文件的所有行
    lines = file_content.split('\n')
    # 预处理代码片段，获取去掉 '...' 后的段落
    segment_parts = preprocess_code_segment(code_segment)
    # 初始化匹配结果
    best_match_start = -1
    best_match_end = -1
    # 遍历文件行数
    for part in segment_parts:
        chunk_lines = part['content'].split('\n')
        # 计算匹配起止行数
        row_start, row_end = calculate_match(lines, chunk_lines, threshold)
        if best_match_start == -1 or row_start < best_match_start:
            best_match_start = row_start
        if row_end > best_match_end:
            best_match_end = row_end

    # 二次验证，如果首末两行的匹配度低于阈值，则各抽取 5 行重新匹配校准
    if best_match_start != -1 and best_match_end != -1:
        original_start_chunk = '\n'.join(lines[best_match_start:best_match_start + 5])
        original_end_chunk = '\n'.join(lines[best_match_end - 4:best_match_end + 1])

        start_part = segment_parts[0]['content'].split('\n')[:5]
        end_part = segment_parts[-1]['content'].split('\n')[-5:]
        start_similarity = precise_ratio(original_start_chunk, '\n'.join(start_part)) * 100
        end_similarity = precise_ratio(original_end_chunk, '\n'.join(end_part)) * 100

        if start_similarity < threshold:
            # print(f"start_similarity < {threshold}: {start_similarity}")
            row_start, row_end = calculate_match(lines, start_part, threshold)
            if row_start != -1 and row_start < best_match_start:
                best_match_start = row_start

        if end_similarity < threshold:
            # print(f"end_similarity < {threshold}: {end_similarity}")
            row_start, row_end = calculate_match(lines, end_part, threshold)
            if row_end > best_match_end:
                best_match_end = row_end

    return FileSnippet(
        content='\n'.join(lines[best_match_start:best_match_end + 1]),
        row_start=best_match_start,
        row_end=best_match_end
    )

def calculate_match(lines: List[str], chunk: List[str], threshold: float) -> tuple[int, int]:
    row_start = -1
    row_end = -1
    highest_similarity = 0.00
    part_length = len(chunk)
    selected_chunk = '\n'.join(chunk)
    for i in range(len(lines) - part_length + 1):
        current_chunk = '\n'.join(lines[i:i + part_length])
        # 计算相似度
        similarity = precise_ratio(current_chunk, selected_chunk) * 100
        # 如果相似度大于阈值，并且相似度是当前最高的，更新匹配结果
        # if similarity > 50:
        #     print(f"row_start: {i}, row_end: {i + part_length - 1}, similarity: {similarity}")
        if similarity > threshold and similarity > highest_similarity:
            row_start = i
            row_end = i + part_length - 1
            highest_similarity = similarity
            # 如果相似度达到 100，直接返回结果
            if similarity == 100:
                break
    return row_start, row_end

def apply_edited_snippets(original_file_content: str, edited_file_snippets: List[FileSnippet]) -> str:
    original_lines = original_file_content.split('\n')
    for snippet in sorted(edited_file_snippets, key=lambda x: -x.row_start):
        if snippet.row_start == -1 or snippet.row_end == -1:
            raise AgentRunException('Snippet `row_start` or `row_end` is not set.')
        selected_lines = original_lines[snippet.row_start:snippet.row_end + 1]
        edited_lines = snippet.content.split('\n')
        edited_lines = fix_code_indention(selected_lines, edited_lines)

        # 直接替换行数组中的内容
        original_lines[snippet.row_start:snippet.row_end + 1] = edited_lines

    return '\n'.join(original_lines)

async def async_fix_code_indention(selected_lines: List[str], edited_lines: List[str]) -> List[str]:
    return await asyncio.to_thread(fix_code_indention, selected_lines, edited_lines)

def fix_code_indention(selected_lines: List[str], edited_lines: List[str]) -> List[str]:
    # 获取基准缩进（首行非空缩进）
    base_indent = ''
    for line in selected_lines:
        if line.strip():
            match = re.match(r'^\s*', line)
            if match:
                base_indent = match.group()
            break
    base_indent_len = len(base_indent)

    # 确定缩进字符类型（空格或制表符）
    indent_char = ' '
    if '\t' in base_indent:  # 优先使用制表符
        indent_char = '\t'

    # 计算每行的相对缩进差异
    diffs = []
    for orig, edit in zip(selected_lines, edited_lines):
        if not orig.strip() or not edit.strip():
            continue

        # 原始行相对基准缩进的偏移
        orig_offset = len(orig) - len(orig.lstrip()) - base_indent_len
        # 编辑行当前缩进
        edit_indent = len(edit) - len(edit.lstrip())
        # 期望缩进 = 基准缩进 + 原始相对偏移
        expected_indent = base_indent_len + orig_offset
        # 差异 = 期望缩进 - 当前缩进
        diffs.append(expected_indent - edit_indent)

    # 确定调整量（优先最小非负值，无则使用基准缩进）
    diff = 0
    if diffs:
        valid_diffs = [d for d in diffs if d >= 0]
        diff = min(valid_diffs) if valid_diffs else base_indent_len
    else:
        diff = base_indent_len  # 处理全空行的情况

    adjusted = []
    for i, line in enumerate(edited_lines):
        if not line.strip():
            # 空行处理保持不变
            if i < len(selected_lines) and not selected_lines[i].strip():
                adjusted.append(selected_lines[i])
            else:
                adjusted.append('')
            continue

        line_indent = len(line) - len(line.lstrip())
        new_indent = line_indent + diff

        # 最终缩进只需确保不小于0
        final_indent = max(new_indent, 0)
        adjusted.append(f"{indent_char * final_indent}{line.lstrip()}")

    return adjusted

def add_line_numbers(content: str, start=0) -> str:
    """为代码添加行号注释"""
    lines = content.split('\n')
    return '\n'.join(f'{i + 1 + start:>4}: {line}' for i, line in enumerate(lines))


class LinterConfig(TypedDict):
    command: list[str]
    stdin_support: bool
    parser: str


# 新增类型别名和常量
SupportedExtension = Literal['py', 'js', 'ts', 'tsx']
SUPPORTED_EXTENSIONS = ('py', 'js', 'ts', 'tsx')

ruff_config: LinterConfig = {
    "command": ["ruff", "check", "--select=E9,F821", "--output-format=json"],
    "stdin_support": False,
    "parser": "ruff"
}
eslint_config: LinterConfig = {
    "command": [
        "eslint", "--config", "heracles/agent_roles/code_role/eslint.config.mjs", "--format=json", "--stdin", "--stdin-filename=temp.js"
    ],
    "stdin_support": True,
    "parser": "eslint"
}
# 修改语言配置键名（去掉前导点）
LINTER_CONFIG: dict[SupportedExtension, LinterConfig] = {
    "py": ruff_config,
    "js": eslint_config,
    "ts": eslint_config,
    "tsx": eslint_config,
}

async def check_syntax(code: str, extension: SupportedExtension) -> list:
    """多语言语法检查入口"""
    # 直接使用传入的扩展名（不再添加前导点）
    config = LINTER_CONFIG.get(extension)

    if not config:
        logger.warning(f"Unsupported language extension: {extension}")
        return []

    try:
        if config["stdin_support"]:
            return await _check_via_stdin(code, config["command"], config["parser"])
        else:
            return await _check_via_tempfile(code, extension, config["command"], config["parser"])
    except Exception as e:
        logger.warning(f"Syntax check failed for {extension}: {str(e)}")
        return []

async def _check_via_stdin(code: str, cmd: list, parser: str) -> list:
    """通过stdin进行语法检查"""
    from asyncio.subprocess import create_subprocess_exec

    proc = await create_subprocess_exec(
        *cmd,
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )

    stdout, stderr = await proc.communicate(input=code.encode())
    raw_output = stdout.decode()

    logger.debug(f"[{cmd[0]}] stdout: {raw_output}")
    logger.debug(f"[{cmd[0]}] stderr: {stderr.decode()}")

    return _parse_linter_output(raw_output, parser)

async def _check_via_tempfile(code: str, extension: SupportedExtension, cmd: list, parser: str) -> list:
    """通过临时文件进行语法检查"""
    temp_dir = Path(tempfile.gettempdir()) / "heracles_checks"
    temp_dir.mkdir(exist_ok=True)

    temp_path = temp_dir / f"temp_{os.urandom(4).hex()}.{extension}"
    try:
        temp_path.write_text(code, encoding="utf-8")
        full_cmd = cmd + [str(temp_path)]

        from asyncio.subprocess import create_subprocess_exec
        proc = await create_subprocess_exec(
            *full_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await proc.communicate()
        raw_output = stdout.decode()

        logger.debug(f"[{cmd[0]}] stdout: {raw_output}")
        logger.debug(f"[{cmd[0]}] stderr: {stderr.decode()}")

        return _parse_linter_output(raw_output, parser)
    finally:
        if temp_path.exists():
            temp_path.unlink()

def _parse_linter_output(raw: str, tool: str) -> list:
    """统一解析不同linter的输出"""
    try:
        if not raw.strip():
            return []

        data = json.loads(raw)
        if tool == "ruff":
            return [{
                "line": item["location"]["row"],
                "code": item["code"],
                "message": item["message"]
            } for item in data]

        elif tool == "eslint":
            return [{
                "line": msg["line"],
                "code": msg["ruleId"],
                "message": f"{msg['message']} ({msg.get('ruleId', 'unknown')})"
            } for report in data for msg in report["messages"]]

    except (json.JSONDecodeError, KeyError) as e:
        logger.error(f"Failed to parse {tool} output: {str(e)}")
    return []
