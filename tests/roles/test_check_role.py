import pytest
import asyncio
from heracles.core.schema.task import Task, TaskStep, TaskAction, FileActionObject, ActionType
from heracles.core.schema.models import (
    TestCaseRuleModels,
    TestCaseRuleModel,
    ErrorFoundModel,
    ErrorReportModel,
    ProjectErrorMessageModel,
)
from heracles.core.exceptions import AgentRunException
from heracles.agent_roles.check_role import CheckRole, ObserverLog
from typing import Any

def abnormal_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=0.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

def failed_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=1.0,
                ref_id='TaskAction/1-1'
            ),
            TestCaseRuleModel(
                title='Test Rule 2',
                score=0,
                weight=1.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

def success_rule_model(*args, **kwargs):
    return TestCaseRuleModels(
        rules=[
            TestCaseRuleModel(
                title='Test Rule 1',
                score=1,
                weight=1.0,
                ref_id='TaskAction/1-1'
            )
        ]
    )

@pytest.mark.asyncio
async def test_check_role_check_task(create_workspace, mocker):
    workspace = await create_workspace
    check_role = CheckRole(workspace)
    mocker.patch.object(check_role, 'aask', side_effect=success_rule_model)

    workspace.set_task(Task(title='Test Task', description='Test Description', task_steps=[
        TaskStep(title='Test Step', task_actions=[
            TaskAction(title='Test Action 1', action=ActionType.ADD_FILE,
            action_object=FileActionObject(path='test/path1', detailed_requirement='Test requirement 1')
            )]
        )
    ]))

    errors = await check_role.check_task()
    assert len(errors) == 0

    mocker.patch.object(check_role, 'aask', side_effect=failed_rule_model)
    errors = await check_role.check_task()
    assert len(errors) == 1

@pytest.mark.asyncio
async def test_check_role_monitor_errors(create_workspace, mocker):
    workspace = await create_workspace
    emitter = workspace.playground.ide_server_client.event_emitter
    workspace.observations.on_terminal(workspace.playground.agent_controller.check_role.on_terminal)

    assert emitter.has_listener('terminal')

    await workspace.smart_detect.set_status('monitoring_errors')
    terminal_id = 'test'
    check_role = workspace.playground.agent_controller.check_role
    mocker.patch.object(check_role, 'aask', return_value=ErrorFoundModel(is_new_error=True, title='Test Error'))
    await emitter.emit('terminal', 'error console line 1', terminal_id)
    # 在 Python 中，range(2, 10) 会生成从 2 到 9 的序列（不包含 10）
    for i in range(2, 10):
        await emitter.emit('terminal', f'console line {i}', terminal_id)
        await asyncio.sleep(1)
    await asyncio.sleep(5)
    assert len(workspace.smart_detect.errors) == 1
    # TODO: asyncio.sleep 有误差。虽然有 9 行输出，但因为延迟防抖 5 秒，只有第一行有 error，所以只有前 6 行会被记录
    # assert len(workspace.smart_detect.errors[0].content.split('\n')) == 6

    for i in range(1, 10):
        await emitter.emit('terminal', f'error console line {i}', terminal_id)
        await asyncio.sleep(1)
    await asyncio.sleep(5)
    assert len(workspace.smart_detect.errors) == 2
    # TODO: asyncio.sleep 有误差。前面剩余的 7-9 行共 3 行 + 后边增加的 9 行，有 12 行会被记录
    # assert len(workspace.smart_detect.errors[1].content.split('\n')) == 12

@pytest.mark.asyncio
async def test_check_role_no_task_exception(create_workspace):
    """测试没有任务时的异常处理（43, 47行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 测试没有任务时的check_task
    with pytest.raises(Exception, match="check task failed, reason: no task here"):
        await check_role.check_task()

    # 测试没有任务时的check_task_with_rules
    with pytest.raises(Exception, match="check task failed, reason: no task here"):
        await check_role.check_task_with_rules()

@pytest.mark.asyncio
async def test_check_role_abnormal_response(create_workspace, mocker):
    """测试异常响应处理（71行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 设置任务
    workspace.set_task(Task(title='Test Task', description='Test Description', task_steps=[
        TaskStep(title='Test Step', task_actions=[
            TaskAction(title='Test Action 1', action=ActionType.ADD_FILE,
            action_object=FileActionObject(path='test/path1', detailed_requirement='Test requirement 1')
            )]
        )
    ]))

    # 模拟返回非TestCaseRuleModels的响应
    mocker.patch.object(check_role, 'aask', return_value="Invalid response")

    with pytest.raises(Exception, match="check task failed, reason: Invalid response"):
        await check_role.check_task()

@pytest.mark.asyncio
async def test_check_role_run_and_check_project_scenarios(create_workspace, mocker):
    """测试运行和检查项目的不同场景（77, 80-89行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 场景1：项目已经在运行
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.tools, 'run_project')
    mocker.patch.object(check_role, 'analyze_log')

    await check_role.run_and_check_project()

    # 验证不会调用run_project
    workspace.tools.run_project.assert_not_called()

    # 场景2：项目未运行，需要启动
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')

    await check_role.run_and_check_project()

    # 验证会调用run_project
    workspace.tools.run_project.assert_called_once()

@pytest.mark.asyncio
async def test_check_role_terminal_processing_edge_cases(create_workspace, mocker):
    """测试终端处理的边界情况（94, 103行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 测试新终端的处理
    terminal_id = 'new_terminal'
    assert terminal_id not in check_role.terminal_log_dict

    await check_role.on_terminal('test line', terminal_id)

    # 验证创建了新的日志对象
    assert terminal_id in check_role.terminal_log_dict
    assert check_role.terminal_log_dict[terminal_id].type == 'terminal'

@pytest.mark.asyncio
async def test_check_role_process_line_timing_logic(create_workspace, mocker):
    """测试处理行的时间逻辑（115, 125行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 设置监控状态
    await workspace.smart_detect.set_status('monitoring_errors')

    # 创建日志对象
    from heracles.agent_roles.check_role import ObserverLog
    log = ObserverLog('terminal')

    # 模拟时间超过10秒的情况
    import time
    original_time = time.time

    def mock_time():
        return original_time() + 20  # 模拟20秒后

    mocker.patch('time.time', side_effect=mock_time)

    # 添加一些数据到日志
    log.lines.push('old line')
    log.last_line_timestamp = original_time() - 15  # 15秒前

    # 处理新行
    await check_role._process_line('new line without error', log)

    # 验证日志被清空并添加了新行
    assert 'old line' not in [item for item in log.lines.items]

@pytest.mark.asyncio
async def test_check_role_error_handling_monitoring(create_workspace, mocker):
    """测试错误处理监控（130-134行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 设置监控状态
    await workspace.smart_detect.set_status('monitoring_errors')

    from heracles.agent_roles.check_role import ObserverLog
    log = ObserverLog('terminal')

    # 模拟handle_error方法
    mocker.patch.object(check_role, 'handle_error')

    # 处理包含错误关键字的行
    await check_role._process_line('Error: something went wrong', log)

    # 验证handle_error被调用
    check_role.handle_error.assert_called_once()

@pytest.mark.asyncio
async def test_check_role_analyze_log_direct(create_workspace, mocker):
    """测试直接分析日志（140行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    from heracles.agent_roles.check_role import ObserverLog
    log = ObserverLog('terminal')

    # 添加包含错误的日志行
    log.lines.push('normal line')
    log.lines.push('Error: critical failure')
    log.lines.push('another normal line')

    # 模拟handle_error方法
    mocker.patch.object(check_role, 'handle_error')

    await check_role.analyze_log(log)

    # 验证找到错误并调用handle_error
    check_role.handle_error.assert_called_once()

@pytest.mark.asyncio
async def test_check_role_check_if_new_error_duplicate(create_workspace, mocker):
    """测试检查重复错误（147行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    from heracles.agent_roles.check_role import ObserverLog
    from heracles.core.schema import ProjectErrorMessage

    log = ObserverLog('terminal')
    log.lines.push('Error: duplicate error message')

    # 添加现有错误到workspace
    existing_error = ProjectErrorMessage(
        title='Existing Error',
        ref_id='test/1234',
        content='Error: duplicate error message'
    )
    workspace.smart_detect.errors = [existing_error]

    # 模拟precise_ratio返回高相似度
    mocker.patch('heracles.agent_roles.code_role.utils.precise_ratio', return_value=0.9)

    result = await check_role.check_if_new_error(log)

    # 验证返回不是新错误
    assert not result.is_new_error

@pytest.mark.asyncio
async def test_check_role_trigger_callbacks_error_handling(create_workspace, mocker):
    """测试触发回调的错误处理（166, 169-174, 177行）"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # 测试trigger_error_handler
    mocker.patch.object(workspace.playground, 'trigger_error_handler')
    await check_role.trigger_error_handler('test error message')
    workspace.playground.trigger_error_handler.assert_called_once_with('test error message')

    # 测试trigger_tool_callback异常处理 - 只验证不会抛出异常
    invalid_tool_call = {'invalid': 'format'}

    # 不应该抛出异常，而是记录错误日志
    try:
        await check_role.trigger_tool_callback(invalid_tool_call, 'end')
        # 验证没有抛出异常
        assert True
    except Exception as e:
        # 如果抛出异常，测试失败
        raise AssertionError("trigger_tool_callback should not raise exception for invalid tool_call") from e

@pytest.mark.asyncio
async def test_monitor_terminal_log(create_workspace):
    """Test monitor_terminal_log function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Test with non-existing terminal (should return early)
    await check_role.monitor_terminal_log('ignored line', 'non_existing_terminal')
    assert 'non_existing_terminal' not in check_role.terminal_log_dict

    # Setup terminal log
    check_role.terminal_log_dict['terminal1'] = ObserverLog('terminal')

    # Test non-goAgent terminal
    await check_role.monitor_terminal_log('ignored line', 'terminal1', 'normal')
    assert 'ignored line' not in check_role.terminal_log_dict['terminal1'].lines.to_string()

    # Test goAgent terminal
    await check_role.monitor_terminal_log('ignored line', 'terminal2', 'goAgent')
    assert 'ignored line'  in check_role.terminal_log_dict['terminal2'].lines.to_string()

    # Test with single line
    await check_role.monitor_terminal_log('single line', 'terminal1', 'goAgent')
    assert 'single line' in check_role.terminal_log_dict['terminal1'].lines.to_string()

    # Test with multi-line input
    await check_role.monitor_terminal_log('line1\nline2\nline3', 'terminal1', 'goAgent')
    terminal_log = check_role.terminal_log_dict['terminal1'].lines.to_string()
    assert 'line2' in terminal_log and 'line3' in terminal_log


@pytest.mark.asyncio
async def test_prepare_environment(create_workspace, mocker):
    """Test _prepare_environment function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Mock dependencies
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.tools, 'stop_project')
    mocker.patch.object(workspace.observations, 'on_terminal')
    mocker.patch.object(workspace.playground.ide_server_client, 'following_focus_component')
    mocker.patch.object(
        workspace.playground.ide_server_client, 'opened_terminal_info', {'terminal1': {'type': 'goAgent'}, 'terminal2': {'type': 'other'}}
    )

    await check_role._prepare_environment()

    # Verify project was stopped when running
    workspace.tools.stop_project.assert_called_once()

    # Verify terminal monitoring was set up
    workspace.observations.on_terminal.assert_called_once()

    # Test when project is not running
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')
    workspace.tools.stop_project.reset_mock()

    await check_role._prepare_environment()

    # Verify stop_project was not called
    workspace.tools.stop_project.assert_not_called()


@pytest.mark.asyncio
async def test_run_and_monitor_project(create_workspace, mocker):
    """Test _run_and_monitor_project function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', ['8080'])

    await check_role._run_and_monitor_project()

    # Test timeout exception handling
    mocker.patch('heracles.agent_roles.check_role.wait_for', side_effect=AgentRunException('timeout'))
    logger_mock = mocker.patch.object(check_role, 'logger')

    await check_role._run_and_monitor_project()

    logger_mock.warning.assert_called_with('wait_for run_project timeout, but we continue to check')


@pytest.mark.asyncio
async def test_get_lint_log(create_workspace, mocker):
    """Test get_lint_log function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Test with lint data
    lint_data = {'data': {'file1.py': [{'message': 'error', 'line': 10}]}}
    mocker.patch.object(workspace.tools, 'lint_diagnostic', return_value=lint_data)

    result = await check_role.get_lint_log(['file1.py'])

    assert 'file1.py' in result
    workspace.tools.lint_diagnostic.assert_called_once_with(['file1.py'])


@pytest.mark.asyncio
async def test_get_terminal_log(create_workspace):
    """Test get_terminal_log function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Setup terminal logs
    check_role.terminal_log_dict['terminal1'] = ObserverLog('terminal')
    check_role.terminal_log_dict['terminal1'].lines.push('line 1')
    check_role.terminal_log_dict['terminal1'].lines.push('line 2')

    check_role.terminal_log_dict['terminal2'] = ObserverLog('terminal')
    check_role.terminal_log_dict['terminal2'].lines.push('line 3')

    result = await check_role.get_terminal_log()

    # The actual format uses the terminal_id directly: 'terminal terminal1 log:'
    assert 'terminal terminal1 log:' in result
    assert 'terminal terminal2 log:' in result
    assert 'line 1' in result
    assert 'line 2' in result
    assert 'line 3' in result

    # Test with empty terminal logs
    check_role.terminal_log_dict.clear()

    result = await check_role.get_terminal_log()

    assert result == ''


@pytest.mark.asyncio
async def test_get_browser_log(create_workspace, mocker):
    """Test get_browser_log function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Test when project is running and has ports
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', ['8080'])
    mocker.patch.object(workspace.tools, 'browser_console_logs', return_value=['error 1', 'warning 2'])

    result = await check_role.get_browser_log()

    assert 'error 1' in result
    assert 'warning 2' in result
    workspace.tools.browser_console_logs.assert_called_once()

    # Test when project is not running
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')

    result = await check_role.get_browser_log()

    assert result == ''

    # Test when no http ports
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', [])

    result = await check_role.get_browser_log()

    assert result == ''


@pytest.mark.asyncio
async def test_get_browser_screenshot(create_workspace, mocker):
    """Test get_browser_screenshot function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Test when project is running and has ports
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', ['8080'])
    mocker.patch.object(workspace.tools, 'browser_screenshot', return_value='screenshot_data')

    result = await check_role.get_browser_screenshot()

    assert result == 'screenshot_data'
    workspace.tools.browser_screenshot.assert_called_once()

    # Test when project is not running
    mocker.patch.object(workspace.tools, 'run_status', return_value='STOPPED')

    result = await check_role.get_browser_screenshot()

    assert result is None

    # Test when no http ports
    mocker.patch.object(workspace.tools, 'run_status', return_value='RUNNING')
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', [])

    result = await check_role.get_browser_screenshot()

    assert result is None


@pytest.mark.asyncio
async def test_perform_checks(create_workspace, mocker):
    """Test _perform_checks function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    mocker.patch.object(workspace.tools, 'list_changed_files', return_value=['test.py', 'test.js', 'readme.md'])

    # Mock the individual log methods
    mocker.patch.object(check_role, 'get_lint_log', return_value='lint log data')
    mocker.patch.object(check_role, 'get_terminal_log', return_value='terminal log data')
    mocker.patch.object(check_role, 'get_browser_log', return_value='browser log data')
    mocker.patch.object(check_role, 'get_browser_screenshot', return_value='screenshot_data')

    result = await check_role._perform_checks()

    # Verify result structure
    assert result['lint_log'] == 'lint log data'
    assert result['terminal_log'] == 'terminal log data'
    assert result['browser_log'] == 'browser log data'
    assert result['browser_screenshot'] == 'screenshot_data'

    # Test exception handling
    mocker.patch.object(check_role, 'get_lint_log', side_effect=Exception('lint error'))

    result = await check_role._perform_checks()

    # Should handle exceptions gracefully
    assert result['lint_log'] == ''
    assert result['terminal_log'] == 'terminal log data'


@pytest.mark.asyncio
async def test_partial_report_callback(create_workspace, mocker):
    """Test partial_report_callback function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Setup
    check_role.error_report_id = 'test-id'
    check_role.partial_report_str = ''
    mocker.patch.object(workspace.auto_fix, 'trigger_auto_fix_errors_updated')

    # Test with valid JSON
    valid_json = (
        '{"errors": [{"title": "test error", "content": "test content", "severity": "high", '
        '"root_cause": "test cause", "impact": "test impact", "suggested_fix": "test fix"}], '
        '"summary": "test summary"}'
    )

    await check_role.partial_report_callback(valid_json)

    # Verify auto_fix was triggered with correct data
    workspace.auto_fix.trigger_auto_fix_errors_updated.assert_called_once()
    call_args = workspace.auto_fix.trigger_auto_fix_errors_updated.call_args[0][0]
    assert call_args['id'] == 'test-id'
    assert call_args['message'] == 'test summary'
    assert len(call_args['error_list']) == 1
    assert call_args['error_list'][0]['title'] == 'test error'

    # Test with invalid JSON (should not raise exception)
    workspace.auto_fix.trigger_auto_fix_errors_updated.reset_mock()
    check_role.partial_report_str = ''

    await check_role.partial_report_callback('invalid json')

    # Should not trigger auto_fix for invalid JSON
    workspace.auto_fix.trigger_auto_fix_errors_updated.assert_not_called()

    # Test ValidationError exception handling (covers except ValidationError line)
    workspace.auto_fix.trigger_auto_fix_errors_updated.reset_mock()
    check_role.partial_report_str = ''

    # Mock ErrorReportModel to raise TypeError
    with mocker.patch('heracles.agent_roles.check_role.ErrorReportModel', side_effect=TypeError('type error')):
        valid_json_but_type_error = '{"errors": [], "summary": "test"}'

        # Should not raise exception due to try-except block
        await check_role.partial_report_callback(valid_json_but_type_error)

        # Should not trigger auto_fix due to TypeError
        workspace.auto_fix.trigger_auto_fix_errors_updated.assert_not_called()


@pytest.mark.asyncio
async def test_generate_report(create_workspace, mocker):
    """Test _generate_report function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Test with error report
    mock_error_report = ErrorReportModel(
        errors=[
            ProjectErrorMessageModel(
                title='error', content='content', severity='high', root_cause='cause', impact='impact', suggested_fix='fix'
            )
        ],
        summary='found errors',
    )
    mocker.patch.object(check_role, 'aask', return_value=mock_error_report)

    check_data = {
        'lint_log': 'lint data',
        'terminal_log': 'terminal data',
        'browser_log': 'browser data',
        'browser_screenshot': 'screenshot_data',
    }

    result = await check_role._generate_report(check_data)

    # Verify result structure
    assert 'text' in result
    assert 'image' in result
    assert 'error_report' in result
    assert result['image'] == 'screenshot_data'
    assert result['error_report']['summary'] == 'found errors'

    # Test with no errors and UI report generation
    mock_error_report_no_errors = ErrorReportModel(errors=[], summary='no errors')

    def mock_aask(*args, **kwargs):
        return mock_error_report_no_errors

    mocker.patch.object(check_role, 'aask', side_effect=mock_aask)

    result = await check_role._generate_report(check_data)

    # Test with no screenshot
    check_data_no_screenshot: dict[str, Any] = check_data.copy()
    check_data_no_screenshot['browser_screenshot'] = None

    result = await check_role._generate_report(check_data_no_screenshot)

    assert result['image'] is None


@pytest.mark.asyncio
async def test_check_run_finished_condition(create_workspace, mocker):
    """Test check_run_finished_condition function coverage in _run_and_monitor_project"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Capture the condition function from wait_for
    captured_condition = None

    def mock_wait_for(condition_func, _timeout=None):
        nonlocal captured_condition
        captured_condition = condition_func
        return None

    mocker.patch('heracles.agent_roles.check_role.wait_for', side_effect=mock_wait_for)

    # Call _run_and_monitor_project to capture the condition
    await check_role._run_and_monitor_project()

    # Test condition scenarios
    assert captured_condition is not None

    # Test with http_ports available
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', ['8080'])
    assert captured_condition() is True

    # Test with no http_ports but terminal prompts
    mocker.patch.object(workspace.playground.ide_server_client, 'http_ports', [])
    check_role.terminal_log_dict['terminal1'] = ObserverLog('terminal')
    assert captured_condition() is False  # Empty terminal

    check_role.terminal_log_dict['terminal1'].lines.push('npm start')
    assert captured_condition() is False  # No prompt

    check_role.terminal_log_dict['terminal1'].lines.push('➜ project $ ')
    assert captured_condition() is True  # Has prompt

    # Test multiple terminals
    check_role.terminal_log_dict['terminal2'] = ObserverLog('terminal')
    check_role.terminal_log_dict['terminal2'].lines.push('running...')
    assert captured_condition() is False  # One terminal without prompt

    check_role.terminal_log_dict['terminal2'].lines.push('➜ project $ ')
    assert captured_condition() is True  # All terminals have prompts


@pytest.mark.asyncio
async def test_check_errors(create_workspace, mocker):
    """Test check_errors function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Mock all the internal methods
    mocker.patch.object(check_role, '_prepare_environment')
    mocker.patch.object(check_role, '_run_and_monitor_project')
    mocker.patch.object(check_role, '_perform_checks', return_value={
        'lint_log': 'test lint',
        'terminal_log': 'test terminal',
        'browser_log': 'test browser',
        'browser_screenshot': 'test_screenshot'
    })
    mocker.patch.object(check_role, '_generate_report', return_value={
        'text': 'test report',
        'image': 'test_screenshot',
        'error_report': {'summary': 'test errors'}
    })

    result = await check_role.check_errors()

    # Verify return value
    assert result['text'] == 'test report'
    assert result['image'] == 'test_screenshot'
    assert result['error_report']['summary'] == 'test errors'


@pytest.mark.asyncio
async def test_clear_logs(create_workspace):
    """Test clear_logs function"""
    workspace = await create_workspace
    check_role = CheckRole(workspace)

    # Setup terminal logs with data
    check_role.terminal_log_dict['terminal1'] = ObserverLog('terminal')
    check_role.terminal_log_dict['terminal1'].lines.push('line 1')
    check_role.terminal_log_dict['terminal1'].lines.push('line 2')

    check_role.terminal_log_dict['terminal2'] = ObserverLog('terminal')
    check_role.terminal_log_dict['terminal2'].lines.push('line 3')
    check_role.terminal_log_dict['terminal2'].lines.push('line 4')

    # Verify logs have data before clearing
    assert len(check_role.terminal_log_dict['terminal1'].lines.items) == 2
    assert len(check_role.terminal_log_dict['terminal2'].lines.items) == 2

    # Clear logs
    await check_role.clear_logs()

    # Verify all logs are cleared
    assert len(check_role.terminal_log_dict['terminal1'].lines.items) == 0
    assert len(check_role.terminal_log_dict['terminal2'].lines.items) == 0
