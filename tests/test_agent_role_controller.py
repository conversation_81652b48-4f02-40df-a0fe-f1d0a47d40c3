import pytest
import json
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from heracles.core.exceptions import Agent<PERSON><PERSON>Exception
from heracles.agent_roles.role_action import Agent<PERSON><PERSON><PERSON>reateSpecAction, AgentRoleCreateStepAction


class TestAgentRoleController:
    @pytest.fixture
    async def controller(self, create_blank_playground):
        """创建AgentRoleController实例"""
        playground = await create_blank_playground('test-playground-id')
        return playground.agent_controller

    @pytest.mark.asyncio
    async def test_task_property(self, controller):
        """测试task属性getter - 覆盖行60-61"""
        controller = await controller
        # 测试当workspace.task为None时
        controller.workspace.task = None
        assert controller.task is None

        # 测试当workspace.task有值时
        mock_task = Mock()
        controller.workspace.task = mock_task
        assert controller.task == mock_task

    @pytest.mark.asyncio
    async def test_task_state_setter(self, controller):
        """测试task_state setter - 覆盖行80"""
        controller = await controller

        # 使用patch来mock set_state方法
        with patch.object(controller.task_role.task_state, 'set_state') as mock_set_state:
            test_state = 'working'  # 使用有效的状态值
            controller.task_state = test_state
            mock_set_state.assert_called_once_with(test_state)

    @pytest.mark.asyncio
    async def test_make_spec_with_payment_auth_key(self, controller):
        """测试make_spec方法在有payment_auth_key时的逻辑 - 覆盖行87-91"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.payment_auth_key', 'test_key'), patch(
            'heracles.agent_controller.agent_role_controller.async_encrypt_content', new_callable=AsyncMock
        ) as mock_encrypt, patch('heracles.agent_controller.agent_role_controller.uuid.uuid4') as mock_uuid, patch(
            'heracles.agent_controller.agent_role_controller.time.time', return_value=1234567890
        ), patch.object(controller.spec_role, 'run', new_callable=AsyncMock) as mock_spec_run, patch.object(
            controller.workspace, 'trigger', new_callable=AsyncMock
        ) as mock_trigger:
            mock_uuid.return_value = 'test-uuid'
            mock_encrypt.return_value = 'encrypted_data'

            mock_spec = Mock()
            mock_spec.dict.return_value = {'test': 'data'}
            mock_spec.task_scale = 'medium'
            mock_spec_run.return_value = mock_spec

            await controller.make_spec('test_goal', 'test_detail', from_chat=False)

            mock_encrypt.assert_called_once()
            mock_trigger.assert_called_once()

    @pytest.mark.asyncio
    async def test_make_spec_from_chat(self, controller):
        """测试make_spec方法从chat调用时的逻辑 - 覆盖行95-99"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.payment_auth_key', None), patch.object(
            controller.spec_role, 'run', new_callable=AsyncMock
        ) as mock_spec_run, patch.object(controller.chat_role.memory, 'transform_last_message_with_result') as mock_transform:
            mock_spec = Mock()
            mock_spec.dict.return_value = {'test': 'data'}
            mock_spec.model_dump_json.return_value = '{"test": "data"}'
            mock_spec_run.return_value = mock_spec

            await controller.make_spec('test_goal', 'test_detail', from_chat=True)

            mock_transform.assert_called_once()

    @pytest.mark.asyncio
    async def test_make_project_draft(self, controller):
        """测试make_project_draft方法 - 覆盖行103-114"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.find_best_match_env') as mock_find_env, patch(
            'heracles.agent_controller.agent_role_controller.find_match_middlewares'
        ) as mock_find_middlewares, patch.object(
            controller.analyze_role, 'create_project_draft', new_callable=AsyncMock
        ) as mock_create_draft, patch.object(controller.workspace, 'trigger', new_callable=AsyncMock) as mock_trigger:
            mock_find_env.return_value = {'name': 'test_env'}
            mock_find_middlewares.return_value = [{'name': 'middleware1'}, {'name': 'middleware2'}]

            mock_create_draft.return_value = {'environments': {'test': 'env'}, 'middlewares': ['test', 'middleware']}

            result = await controller.make_project_draft('test_goal', 'test_detail')

            assert result['goal'] == 'test_goal'
            assert 'test_env' in result['goal_detail']
            assert 'middleware1' in result['goal_detail']
            mock_trigger.assert_called_once_with('project_draft_updated', mock_create_draft.return_value)

    @pytest.mark.asyncio
    async def test_make_additional_step_with_non_null_action(self, controller):
        """测试make_additional_step方法返回非空action - 覆盖行132-139"""
        controller = await controller

        with patch.object(controller.plan_role, 'make_additional_step', new_callable=AsyncMock) as mock_make_step, patch.object(
            controller.workspace, 'trigger', new_callable=AsyncMock
        ) as mock_trigger, patch.object(controller.chat_role.memory, 'transform_last_message_with_result') as mock_transform:
            mock_action = Mock()
            mock_action.to_dict.return_value = {'action': 'test'}
            mock_action.model_dump_json.return_value = '{"action": "test"}'
            mock_make_step.return_value = mock_action

            await controller.make_additional_step({'test': 'intent'})

            mock_trigger.assert_called_once_with('add_task_step_confirmation', {'action': 'test'})
            mock_transform.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_task_with_existing_task(self, controller):
        """测试run_task方法当存在task时 - 覆盖行168"""
        controller = await controller
        # 设置task状态为working
        controller.task_role.task_state.set_state('working')
        with patch.object(controller.task_role, 'run', new_callable=AsyncMock) as mock_task_run, \
             patch.object(controller, 'start_auto_fix', new_callable=AsyncMock):
            mock_task = Mock()
            mock_task.run_turn = 1
            controller.workspace.task = mock_task
            await controller.run_task(0)

            assert mock_task.run_turn == 2
            mock_task_run.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_message_create_spec_action_with_project_id_none(self, controller):
        """测试run_message处理CreateSpecAction且project_id为None - 覆盖行186-193"""
        controller = await controller

        with patch.object(controller.chat_role, 'run', new_callable=AsyncMock) as mock_chat_run, patch.object(
            controller, 'make_project_draft', new_callable=AsyncMock
        ) as mock_draft:
            action = AgentRoleCreateSpecAction(goal='test_goal', goal_detail='test_detail')
            mock_chat_run.return_value = action
            controller.playground.project_id = None

            mock_draft.return_value = {'goal': 'updated_goal', 'goal_detail': 'updated_detail'}

            await controller.run_message('test_message', False, False)

            mock_draft.assert_called_once_with('test_goal', 'test_detail')

    @pytest.mark.asyncio
    async def test_run_message_create_step_action(self, controller):
        """测试run_message处理CreateStepAction - 覆盖行198-206"""
        controller = await controller

        with patch.object(controller.chat_role, 'run', new_callable=AsyncMock) as mock_chat_run, patch.object(
            controller, '_add_asyncio_task'
        ) as mock_add_task:
            action = AgentRoleCreateStepAction(goal='test_goal', plan_draft='test_plan', references=['ref1', 'ref2'])
            mock_chat_run.return_value = action

            await controller.run_message('test_message', False, False)

            mock_add_task.assert_called_once()

    @pytest.mark.asyncio
    async def test_run_message_unknown_action(self, controller):
        """测试run_message处理未知action类型 - 覆盖行213-214"""
        controller = await controller

        with patch.object(controller.chat_role, 'run', new_callable=AsyncMock) as mock_chat_run:
            unknown_action = Mock()
            mock_chat_run.return_value = unknown_action

            with pytest.raises(AgentRunException):
                await controller.run_message('test_message', False, False)

    @pytest.mark.asyncio
    async def test_cancel_asyncio_message(self, controller):
        """测试_cancel_asyncio_message方法 - 覆盖行259"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.cancel_asyncio_handler') as mock_cancel:
            controller._cancel_asyncio_message()
            mock_cancel.assert_called_once_with(controller, '_asyncio_message')

    @pytest.mark.asyncio
    async def test_wait_tasks_with_all_tasks(self, controller):
        """测试wait_tasks方法当所有任务都存在时 - 覆盖行263"""
        controller = await controller
        mock_task1 = AsyncMock()
        mock_task2 = AsyncMock()
        mock_task3 = AsyncMock()
        mock_task4 = AsyncMock()

        controller._asyncio_task = mock_task1
        controller._asyncio_message = mock_task2
        controller._asyncio_analyze_role_task = mock_task3
        controller._asyncio_summary_role_task = mock_task4

        with patch('asyncio.gather', new_callable=AsyncMock) as mock_gather:
            await controller.wait_tasks()
            mock_gather.assert_called_once_with(mock_task1, mock_task2, mock_task3, mock_task4)

    @pytest.mark.asyncio
    async def test_expand_task_turn_with_sign_data_and_payment_auth_key(self, controller):
        """测试expand_task_turn方法在有sign_data和payment_auth_key时 - 覆盖行275-282"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.payment_auth_key', 'test_key'), patch(
            'heracles.agent_controller.agent_role_controller.async_decrypt_content', new_callable=AsyncMock
        ) as mock_decrypt, patch('heracles.agent_controller.agent_role_controller.time.time', return_value=1000):
            mock_decrypt.return_value = json.dumps({'timestamp': 995000})  # 在有效期内

            mock_task = Mock()
            controller.workspace.task = mock_task

            # 记录调用前的值
            initial_count = controller.task_role.task_state.appended_count
            initial_turn = controller.task_role.task_state.appended_expansion_turn

            await controller.expand_task_turn('encrypted_data')

            mock_decrypt.assert_called_once_with('encrypted_data', 'test_key')
            assert controller.task_role.task_state.appended_count == initial_count
            assert controller.task_role.task_state.appended_expansion_turn == initial_turn + 1

    @pytest.mark.asyncio
    async def test_expand_task_turn_with_invalid_sign_data(self, controller):
        """测试expand_task_turn方法在sign_data无效时抛出异常 - 覆盖行284"""
        controller = await controller
        with patch('heracles.agent_controller.agent_role_controller.payment_auth_key', 'test_key'), patch(
            'heracles.agent_controller.agent_role_controller.async_decrypt_content', new_callable=AsyncMock
        ) as mock_decrypt:
            mock_decrypt.side_effect = Exception('Decryption failed')

            with pytest.raises(AgentRunException, match='Data integrity check failed'):
                await controller.expand_task_turn('invalid_data')

    @pytest.mark.asyncio
    async def test_expand_task_turn_without_task(self, controller):
        """测试expand_task_turn方法当没有task时直接返回"""
        controller = await controller
        controller.workspace.task = None

        result = await controller.expand_task_turn()

        assert result is None

    @pytest.mark.asyncio
    async def test_start_auto_fix_without_task(self, controller):
        """测试start_auto_fix方法当没有task时直接返回"""
        controller = await controller
        controller.workspace.task = None

        result = await controller.start_auto_fix()

        assert result is None

    @pytest.mark.asyncio
    async def test_start_auto_fix_with_fix_completed_status(self, controller):
        """测试start_auto_fix方法当check_errors返回fix_completed状态"""
        controller = await controller

        # Setup task
        mock_task = Mock()
        controller.workspace.task = mock_task
        controller.workspace.auto_fix.max_fix_turn = 3

        with patch.object(controller.workspace.auto_fix, 'update_status_and_event', new_callable=AsyncMock) as mock_update_status, \
             patch.object(controller, 'check_errors', new_callable=AsyncMock) as mock_check_errors:

            # Mock check_errors to set status to fix_completed
            def mock_check_side_effect():
                controller.workspace.auto_fix.status = 'fix_completed'
                return {'text': '', 'error_report': {'summary': ''}}

            mock_check_errors.side_effect = mock_check_side_effect

            await controller.start_auto_fix()

            # Verify start status was set
            mock_update_status.assert_any_call('start')
            # Verify check_errors was called
            mock_check_errors.assert_called()

    @pytest.mark.asyncio
    async def test_start_auto_fix_max_turns_reached(self, controller):
        """测试start_auto_fix方法达到最大修复轮次"""
        controller = await controller

        # Setup task
        mock_task = Mock()
        controller.workspace.task = mock_task
        controller.workspace.auto_fix.max_fix_turn = 2

        with patch.object(controller.workspace.auto_fix, 'update_status_and_event', new_callable=AsyncMock) as mock_update_status, \
             patch.object(controller, 'check_errors', new_callable=AsyncMock) as mock_check_errors, \
             patch.object(controller, 'make_additional_step', new_callable=AsyncMock), \
             patch.object(controller.task_role, 'run', new_callable=AsyncMock):

            # Mock check_errors to always return error text
            mock_check_errors.return_value = {
                'text': 'persistent error',
                'error_report': {'summary': 'error summary'}
            }

            # Mock task_state methods
            controller.task_state.start_task = Mock()

            await controller.start_auto_fix()

            # Verify fix_turn was incremented
            assert controller.workspace.auto_fix.fix_turn == 2

            # Verify final check_errors was called after max turns
            assert mock_check_errors.call_count == 3  # 2 turns + 1 final check

            # Verify final status update with error message
            mock_update_status.assert_any_call('fix_stopped', 'error summary\nui summary')

    @pytest.mark.asyncio
    async def test_make_additional_step_with_action_auto_fix(self, controller):
        """测试make_additional_step方法返回有效action且auto_fix模式"""
        controller = await controller

        # Setup task
        mock_task = Mock()
        controller.workspace.task = mock_task

        with patch.object(controller.plan_role, 'make_additional_step', new_callable=AsyncMock) as mock_make_step, \
             patch.object(controller.workspace.auto_fix, 'update_status_and_event', new_callable=AsyncMock), \
             patch.object(controller.task_role, 'trigger_task_updated', new_callable=AsyncMock):

            mock_action = Mock()
            mock_action.to_dict.return_value = {'action': 'test_action'}
            mock_make_step.return_value = mock_action

            await controller.make_additional_step({'test': 'intent'}, is_auto_fix=True)
