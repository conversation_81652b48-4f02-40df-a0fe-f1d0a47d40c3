SHELL=/bin/bash
# Makefile for Heracles project

# Variables
PYTHON_VERSION = 3.11
# Accept port and host from command line with defaults
PORT ?= 3020
HOST ?= "0.0.0.0"

# ANSI color codes
GREEN=$(shell tput -Txterm setaf 2)
YELLOW=$(shell tput -Txterm setaf 3)
RED=$(shell tput -Txterm setaf 1)
BLUE=$(shell tput -Txterm setaf 6)
RESET=$(shell tput -Txterm sgr0)

install:
	@echo "$(GREEN)Installing project dependencies...$(RESET)"
	@$(MAKE) -s install-python-dependencies
	@$(MAKE) -s install-precommit-hooks
	@echo "$(GREEN)Dependencies installed successfully.$(RESET)"

install-python-dependencies:
	@echo "$(GREEN)Installing Python dependencies...$(RESET)"
	poetry env use python$(PYTHON_VERSION)
	@poetry install
	@echo "$(GREEN)Python dependencies installed successfully.$(RESET)"

install-precommit-hooks:
	@echo "$(YELLOW)Installing pre-commit hooks...$(RESET)"
	@git config --unset-all core.hooksPath || true
	@poetry run pre-commit install
	@echo "$(GREEN)Pre-commit hooks installed successfully.$(RESET)"

# Start local dev server, can specify HOST and PORT: make dev HOST=0.0.0.0 PORT=3000
dev:
	@echo "$(YELLOW)Starting server...$(RESET)"
	@poetry run uvicorn heracles.server.main:app --port $(PORT) --host $(HOST) --reload --timeout-graceful-shutdown 1

# Start production server, can specify HOST and PORT
serve:
	@echo "$(YELLOW)Starting production server...$(RESET)"
	@poetry run uvicorn heracles.server.main:app --port $(PORT) --host $(HOST) --no-access-log

lint:
	@echo "$(YELLOW)Running linters...$(RESET)"
	@echo "$(YELLOW)Running lint formatter...$(RESET)"
	@poetry run ruff check --fix
	@poetry run ruff format
	@poetry run pre-commit run --files heracles/**/* --show-diff-on-failure

test:
	@echo "$(YELLOW)Running tests...$(RESET)"
	@poetry run pytest -s --durations=10 --cov-report term-missing --cov heracles ./tests

llm_test:
	@echo "$(YELLOW)Running llm tests...$(RESET)"
	@poetry run pytest -s ./llm_tests -n 3

basic_test:
	@echo "$(YELLOW)Running llm tests...$(RESET)"
	@poetry run pytest -s ./llm_tests/basic

unstable_basic_test:
	@echo "$(YELLOW)Running llm tests...$(RESET)"
	@poetry run pytest -s ./llm_tests/basic -m unstable --repeat 2 -n 3

smoke_test:
	@echo "$(YELLOW)Running llm tests...$(RESET)"
	@poetry run pytest -s ./llm_tests/smoke -n 3

rag_test:
	@echo "$(YELLOW)Running llm tests...$(RESET)"
	@poetry run pytest -s ./llm_tests/rag -n 3

install-admin:
	@echo "$(GREEN)Installing admin frontend dependencies...$(RESET)"
	@cd ./heracles/server/admin/frontend/ && npm install -g pnpm
	@cd ./heracles/server/admin/frontend/ && pnpm install

dev-admin:
	@echo "$(YELLOW)Starting admin frontend...$(RESET)"
	@cd ./heracles/server/admin/frontend/ && pnpm run dev

clean-db:
	@echo "$(YELLOW)Cleaning llm test db...$(RESET)"
	@if [ -f ./llm_tests/llm_metrics.db ]; then rm ./llm_tests/llm_metrics.db; fi
