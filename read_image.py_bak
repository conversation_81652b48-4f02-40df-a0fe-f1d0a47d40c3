    async def read_image(self, url: str) -> str:
        """Read an image
        :param url: Image url
        :return: data uri of base64 encoded image
        """
        image_base64 = ""
        if not get_env_var('ENABLE_IMAGE_REFERENCE_OPTIONAL'):
            return image_base64
        url_parsed = urlparse(url)
        if url_parsed.scheme not in ['http', 'https']:
            self.logger.warning(f"Unsupported image scheme: {url}")
            return image_base64
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        self.logger.warning(f"Failed to fetch image: {url} , response status: {response.status}")
                        return image_base64
                    else:
                        image_bytes = await response.read()
                        mime = magic.Magic(mime=True)
                        mime_type = mime.from_buffer(image_bytes)
                        if mime_type not in ['image/jpeg', 'image/png', 'image/webp', 'image/gif']:
                            self.logger.warning(f"Unsupported image type: {mime_type}, url: {url}")
                            return image_base64
                        img = Image.open(io.BytesIO(image_bytes))
                        buffer = io.BytesIO()
                        # 设置WebP参数，quality控制有损压缩质量，lossless=False表示使用有损压缩
                        # method控制压缩算法，值越高压缩比越高但速度越慢，范围0-6
                        img.save(buffer, format='WEBP', save_all=True, quality=80, method=6, lossless=False)
                        compressed_image_bytes = buffer.getvalue()
                        image_media_type = "image/webp"
                        image_data = base64.b64encode(compressed_image_bytes).decode('utf-8')
                        image_base64 = f"data:{image_media_type};base64,{image_data}"
        except Exception as e:
            self.logger.warning(f"Failed to read image: {url}, error: {str(e)}")
            return image_base64
        return image_base64