{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import litellm\n", "from qdrant_client import models\n", "from heracles.core.schema import AdminPlaybookModel\n", "from heracles.server.admin.config import get_settings, get_sparse_model\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'[]'\n"]}], "source": ["import requests\n", "res = requests.post(\"http://localhost:8888/rag_query\",json={\"words\": [\"d\"]})\n", "print(res.content)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["text-embedding-3-small\n"]}], "source": ["settings = get_settings()\n", "print(settings.dense_embedding_model)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["LLM_MODEL=\"anthropic/claude-3.5-sonnet\"\n", "LLM_API_KEY=\"sk-szzEcOLRy1WGfMzRg_z_SA\"\n", "LLM_BASE_URL=\"https://proxy.clackyai.com\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["ModelResponse(id='gen-**********-bXnGnv5DuEF6ckooatNh', created=**********, model='openrouter/openrouter/anthropic/claude-3.5-sonnet', object='chat.completion', system_fingerprint=None, choices=[Choices(finish_reason='stop', index=0, message=Message(content=\"Hi! I'm doing well, thank you for asking. How are you today?\", role='assistant', tool_calls=None, function_call=None))], usage=Usage(completion_tokens=21, prompt_tokens=13, total_tokens=34, completion_tokens_details=None, prompt_tokens_details=None), service_tier=None, provider='Google')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import litellm\n", "litellm.completion(model=\"openrouter/anthropic/claude-3.5-sonnet\",api_key=LLM_API_KEY,base_url=LLM_BASE_URL,messages=[{ \"content\": \"Hello, how are you?\",\"role\": \"user\"}])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1;31mProvider List: https://docs.litellm.ai/docs/providers\u001b[0m\n", "\n", "\n", "\u001b[1;31mProvider List: https://docs.litellm.ai/docs/providers\u001b[0m\n", "\n", "\n", "\u001b[1;31mProvider List: https://docs.litellm.ai/docs/providers\u001b[0m\n", "\n"]}, {"ename": "BadRequestError", "evalue": "litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=\"text-embedding-3-small\"\n Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mBadRequestError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m res \u001b[38;5;241m=\u001b[39m litellm\u001b[38;5;241m.\u001b[39membedding(\n\u001b[1;32m      2\u001b[0m         model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext-embedding-3-small\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124md\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m      4\u001b[0m         api_base\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://proxy.clackyai.com\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m      5\u001b[0m         api_key\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msk-szzEcOLRy1WGfMzRg_z_SA\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m      6\u001b[0m     )\n", "File \u001b[0;32m~/miniconda3/envs/clackyai/lib/python3.11/site-packages/litellm/utils.py:1013\u001b[0m, in \u001b[0;36mclient.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m   1009\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m logging_obj:\n\u001b[1;32m   1010\u001b[0m     logging_obj\u001b[38;5;241m.\u001b[39mfailure_handler(\n\u001b[1;32m   1011\u001b[0m         e, traceback_exception, start_time, end_time\n\u001b[1;32m   1012\u001b[0m     )  \u001b[38;5;66;03m# DO NOT MAKE THREADED - router retry fallback relies on this!\u001b[39;00m\n\u001b[0;32m-> 1013\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[0;32m~/miniconda3/envs/clackyai/lib/python3.11/site-packages/litellm/utils.py:903\u001b[0m, in \u001b[0;36mclient.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    901\u001b[0m         print_verbose(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mError while checking max token limit: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    902\u001b[0m \u001b[38;5;66;03m# MODEL CALL\u001b[39;00m\n\u001b[0;32m--> 903\u001b[0m result \u001b[38;5;241m=\u001b[39m original_function(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    904\u001b[0m end_time \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow()\n\u001b[1;32m    905\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m kwargs \u001b[38;5;129;01mand\u001b[39;00m kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/clackyai/lib/python3.11/site-packages/litellm/main.py:3247\u001b[0m, in \u001b[0;36membedding\u001b[0;34m(model, input, dimensions, encoding_format, timeout, api_base, api_version, api_key, api_type, caching, user, custom_llm_provider, litellm_call_id, logger_fn, **kwargs)\u001b[0m\n\u001b[1;32m   3242\u001b[0m default_params \u001b[38;5;241m=\u001b[39m openai_params \u001b[38;5;241m+\u001b[39m litellm_params\n\u001b[1;32m   3243\u001b[0m non_default_params \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m   3244\u001b[0m     k: v \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m kwargs\u001b[38;5;241m.\u001b[39mitems() \u001b[38;5;28;01mif\u001b[39;00m k \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m default_params\n\u001b[1;32m   3245\u001b[0m }  \u001b[38;5;66;03m# model-specific params - pass them straight to the model/provider\u001b[39;00m\n\u001b[0;32m-> 3247\u001b[0m model, custom_llm_provider, dynamic_api_key, api_base \u001b[38;5;241m=\u001b[39m get_llm_provider(\n\u001b[1;32m   3248\u001b[0m     model\u001b[38;5;241m=\u001b[39mmodel,\n\u001b[1;32m   3249\u001b[0m     custom_llm_provider\u001b[38;5;241m=\u001b[39mcustom_llm_provider,\n\u001b[1;32m   3250\u001b[0m     api_base\u001b[38;5;241m=\u001b[39mapi_base,\n\u001b[1;32m   3251\u001b[0m     api_key\u001b[38;5;241m=\u001b[39mapi_key,\n\u001b[1;32m   3252\u001b[0m )\n\u001b[1;32m   3253\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m dynamic_api_key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   3254\u001b[0m     api_key \u001b[38;5;241m=\u001b[39m dynamic_api_key\n", "File \u001b[0;32m~/miniconda3/envs/clackyai/lib/python3.11/site-packages/litellm/litellm_core_utils/get_llm_provider_logic.py:313\u001b[0m, in \u001b[0;36mget_llm_provider\u001b[0;34m(model, custom_llm_provider, api_base, api_key, litellm_params)\u001b[0m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    312\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e, litellm\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mBadRequestError):\n\u001b[0;32m--> 313\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    314\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    315\u001b[0m         error_str \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    316\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGetLLMProvider Exception - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124moriginal model: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    317\u001b[0m         )\n", "File \u001b[0;32m~/miniconda3/envs/clackyai/lib/python3.11/site-packages/litellm/litellm_core_utils/get_llm_provider_logic.py:290\u001b[0m, in \u001b[0;36mget_llm_provider\u001b[0;34m(model, custom_llm_provider, api_base, api_key, litellm_params)\u001b[0m\n\u001b[1;32m    288\u001b[0m     error_str \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m Pass model as E.g. For \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mHuggingface\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m inference endpoints pass in `completion(model=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhuggingface/starcoder\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m,..)` Learn more: https://docs.litellm.ai/docs/providers\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    289\u001b[0m     \u001b[38;5;66;03m# maps to openai.NotFoundError, this is raised when openai does not recognize the llm\u001b[39;00m\n\u001b[0;32m--> 290\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m litellm\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mBadRequestError(  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m    291\u001b[0m         message\u001b[38;5;241m=\u001b[39merror_str,\n\u001b[1;32m    292\u001b[0m         model\u001b[38;5;241m=\u001b[39mmodel,\n\u001b[1;32m    293\u001b[0m         response\u001b[38;5;241m=\u001b[39mhttpx\u001b[38;5;241m.\u001b[39mResponse(\n\u001b[1;32m    294\u001b[0m             status_code\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m400\u001b[39m,\n\u001b[1;32m    295\u001b[0m             content\u001b[38;5;241m=\u001b[39merror_str,\n\u001b[1;32m    296\u001b[0m             request\u001b[38;5;241m=\u001b[39mhttpx\u001b[38;5;241m.\u001b[39mRequest(method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcompletion\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://github.com/BerriAI/litellm\u001b[39m\u001b[38;5;124m\"\u001b[39m),  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m    297\u001b[0m         ),\n\u001b[1;32m    298\u001b[0m         llm_provider\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    299\u001b[0m     )\n\u001b[1;32m    300\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m api_base \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(api_base, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    301\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\n\u001b[1;32m    302\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mapi base needs to be a string. api_base=\u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(api_base)\n\u001b[1;32m    303\u001b[0m     )\n", "\u001b[0;31mBadRequestError\u001b[0m: litellm.BadRequestError: LLM Provider NOT provided. Pass in the LLM provider you are trying to call. You passed model=\"text-embedding-3-small\"\n Pass model as E.g. For 'Huggingface' inference endpoints pass in `completion(model='huggingface/starcoder',..)` Learn more: https://docs.litellm.ai/docs/providers"]}], "source": ["res = litellm.embedding(\n", "        model=\"text-embedding-3-small\",\n", "        input=[\"d\"],\n", "        api_base='https://proxy.clackyai.com',\n", "        api_key='sk-szzEcOLRy1WGfMzRg_z_SA',\n", "    )"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Response [401]>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["anthropic/claude-3.5-sonnet\n", "anthropic/claude-3.5-sonnet"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["EmbeddingResponse(model='test', data=[{'embedding': [0, 0, 1]}], object='list', usage=Usage(completion_tokens=0, prompt_tokens=0, total_tokens=0, completion_tokens_details=None, prompt_tokens_details=None))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["litellm.EmbeddingResponse.model_validate({'model': 'test', 'data': [{'embedding': [0,0,1]}]})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'embedding': [-0.009876498021185398,\n", "   0.0015304419212043285,\n", "   0.015627983957529068,\n", "   -0.05478791892528534,\n", "   -0.00641245674341917,\n", "   -0.012935652397572994,\n", "   0.009648099541664124,\n", "   -0.013551635667681694,\n", "   0.02862592600286007,\n", "   0.007862440310418606,\n", "   0.03180966153740883,\n", "   -0.006592406891286373,\n", "   0.0038308631628751755,\n", "   0.010132580995559692,\n", "   0.014077643863856792,\n", "   0.044683024287223816,\n", "   -0.059909578412771225,\n", "   -0.0024206764064729214,\n", "   -0.051188915967941284,\n", "   0.0364883653819561,\n", "   0.036100782454013824,\n", "   0.024334806948900223,\n", "   0.03992126137018204,\n", "   -0.0436033196747303,\n", "   0.034439701586961746,\n", "   -0.019573045894503593,\n", "   -0.013973826542496681,\n", "   0.010617062449455261,\n", "   0.031200598925352097,\n", "   -0.041582342237234116,\n", "   0.0587468259036541,\n", "   -0.028875088319182396,\n", "   -0.0013963445089757442,\n", "   -0.03881387785077095,\n", "   0.055203188210725784,\n", "   0.004097328055649996,\n", "   0.023615004494786263,\n", "   0.018922457471489906,\n", "   -0.0056684319861233234,\n", "   -0.003938141278922558,\n", "   -0.04149928689002991,\n", "   -0.04498755559325218,\n", "   0.011364548467099667,\n", "   0.02722785249352455,\n", "   0.030010158196091652,\n", "   -0.01872866414487362,\n", "   -0.034412018954753876,\n", "   -0.03862008452415466,\n", "   0.04842045158147812,\n", "   0.027836913242936134,\n", "   -0.03178197517991066,\n", "   -0.002778846537694335,\n", "   0.04822665825486183,\n", "   0.07020826637744904,\n", "   0.01933772675693035,\n", "   0.023933378979563713,\n", "   -0.03515950217843056,\n", "   0.019932946190238,\n", "   0.018548713997006416,\n", "   0.0053327553905546665,\n", "   -0.002318589249625802,\n", "   -0.018770191818475723,\n", "   0.038398608565330505,\n", "   0.005495402496308088,\n", "   -0.03197576850652695,\n", "   -0.060020316392183304,\n", "   0.01223661471158266,\n", "   0.03150513023138046,\n", "   0.00046631330042146146,\n", "   0.021469444036483765,\n", "   0.04592883214354515,\n", "   0.011842108331620693,\n", "   -0.012859519571065903,\n", "   0.01672152802348137,\n", "   0.00872066430747509,\n", "   0.01996063068509102,\n", "   0.0027061745058745146,\n", "   0.01818881370127201,\n", "   0.014548283070325851,\n", "   -0.008035468868911266,\n", "   0.020237477496266365,\n", "   0.011011568829417229,\n", "   -0.019656101241707802,\n", "   0.009606572799384594,\n", "   0.031172914430499077,\n", "   -0.012845677323639393,\n", "   -0.09047342836856842,\n", "   0.006242888048291206,\n", "   0.006869253236800432,\n", "   -0.03651605173945427,\n", "   -0.011177676729857922,\n", "   -0.0035540168173611164,\n", "   0.015295768156647682,\n", "   0.05171492323279381,\n", "   0.02332431636750698,\n", "   -0.027518540620803833,\n", "   -0.0025469877291470766,\n", "   -0.018036548048257828,\n", "   0.002434518886730075,\n", "   -0.007225693203508854,\n", "   0.01036790106445551,\n", "   0.009717311710119247,\n", "   -0.0013331888476386666,\n", "   0.017911966890096664,\n", "   0.022452250123023987,\n", "   0.01864561066031456,\n", "   -0.03169892355799675,\n", "   0.023545794188976288,\n", "   -0.012499618344008923,\n", "   0.03330463171005249,\n", "   -0.0057860915549099445,\n", "   -0.020638905465602875,\n", "   -0.01755206659436226,\n", "   0.029484150931239128,\n", "   0.044738393276929855,\n", "   0.01511581800878048,\n", "   0.10996342450380325,\n", "   -0.1035405844449997,\n", "   0.03394138067960739,\n", "   -0.07131565362215042,\n", "   0.027352431789040565,\n", "   0.030508482828736305,\n", "   0.012506539933383465,\n", "   -0.02289520390331745,\n", "   0.03164355456829071,\n", "   -0.021012648940086365,\n", "   -0.00611830735579133,\n", "   -0.009440464898943901,\n", "   -0.02289520390331745,\n", "   6.450739601859823e-05,\n", "   -0.03167123720049858,\n", "   0.004934788681566715,\n", "   0.03302778676152229,\n", "   0.009606572799384594,\n", "   -0.010790091939270496,\n", "   -0.008340000174939632,\n", "   -0.025151502341032028,\n", "   0.004512597806751728,\n", "   -0.05863608419895172,\n", "   0.041610024869441986,\n", "   0.011323020793497562,\n", "   0.024044116958975792,\n", "   0.00636746920645237,\n", "   0.0005147614283487201,\n", "   -0.016223203390836716,\n", "   -0.028266025707125664,\n", "   -0.016832266002893448,\n", "   0.04028116539120674,\n", "   0.022770622745156288,\n", "   0.028155287727713585,\n", "   -0.011537577025592327,\n", "   0.0056338259018957615,\n", "   -0.008035468868911266,\n", "   0.0037028216756880283,\n", "   -0.053680531680583954,\n", "   -0.037734176963567734,\n", "   -0.015794092789292336,\n", "   -0.019628414884209633,\n", "   -0.031339023262262344,\n", "   -0.001717313309200108,\n", "   0.02418254129588604,\n", "   -0.004972855094820261,\n", "   0.011309178546071053,\n", "   -0.02538682334125042,\n", "   -0.025525245815515518,\n", "   0.008630689233541489,\n", "   -0.007841676473617554,\n", "   -0.01757975108921528,\n", "   -0.052739255130290985,\n", "   0.019254673272371292,\n", "   0.02667415887117386,\n", "   0.015544930472970009,\n", "   0.013648531399667263,\n", "   -0.012395801022648811,\n", "   -0.02062506228685379,\n", "   0.02407180145382881,\n", "   -0.022438406944274902,\n", "   0.01036790106445551,\n", "   -0.022341512143611908,\n", "   0.002041742904111743,\n", "   -0.020099055022001266,\n", "   0.05074596032500267,\n", "   -0.025594457983970642,\n", "   0.018825560808181763,\n", "   0.0151435025036335,\n", "   -0.059134408831596375,\n", "   0.024196382611989975,\n", "   0.005353518761694431,\n", "   0.0029587969183921814,\n", "   -0.013793876394629478,\n", "   -0.0060871620662510395,\n", "   -0.009419701993465424,\n", "   0.029179619625210762,\n", "   0.03814944624900818,\n", "   0.027670806273818016,\n", "   -0.0773509070277214,\n", "   0.01847950369119644,\n", "   0.03092375211417675,\n", "   -0.013544714078307152,\n", "   0.040751803666353226,\n", "   -0.03510413318872452,\n", "   -0.0029414938762784004,\n", "   0.010305610485374928,\n", "   -0.011468365788459778,\n", "   0.01510197576135397,\n", "   0.013565477915108204,\n", "   -0.02394722029566765,\n", "   0.04700853303074837,\n", "   -0.017399800941348076,\n", "   -0.02501307986676693,\n", "   0.06976531445980072,\n", "   -0.001362603739835322,\n", "   -0.0019361950689926744,\n", "   0.0029484149999916553,\n", "   -0.005488481372594833,\n", "   0.007550987880676985,\n", "   0.05088438466191292,\n", "   0.012631121091544628,\n", "   -0.008769112639129162,\n", "   0.06107233464717865,\n", "   -0.0081185232847929,\n", "   0.0100564481690526,\n", "   0.08764959871768951,\n", "   0.012312747538089752,\n", "   -0.010236398316919804,\n", "   0.08349689841270447,\n", "   0.011198440566658974,\n", "   -0.014188382774591446,\n", "   0.001392018748447299,\n", "   -0.05650436878204346,\n", "   -0.04291120544075966,\n", "   0.01795349456369877,\n", "   0.007654805202037096,\n", "   -0.004218448419123888,\n", "   -0.02383648231625557,\n", "   0.02610662393271923,\n", "   -0.03352610766887665,\n", "   -0.004190763458609581,\n", "   -0.03496571257710457,\n", "   -0.05160418525338173,\n", "   -0.037651121616363525,\n", "   0.009516597725450993,\n", "   -0.029594888910651207,\n", "   -0.039229147136211395,\n", "   -0.02073580212891102,\n", "   -0.013074074871838093,\n", "   0.024750076234340668,\n", "   -0.009620415046811104,\n", "   0.025635983794927597,\n", "   0.015171186998486519,\n", "   0.005228938069194555,\n", "   -0.03748501464724541,\n", "   -0.03748501464724541,\n", "   -0.06882403790950775,\n", "   0.02733859047293663,\n", "   -0.01049940288066864,\n", "   0.025483718141913414,\n", "   0.0024812365882098675,\n", "   0.02086038328707218,\n", "   -0.008125443942844868,\n", "   -0.051105860620737076,\n", "   -0.0036336102057248354,\n", "   -0.042495936155319214,\n", "   -0.04986005276441574,\n", "   -0.06611093878746033,\n", "   0.0051908716559410095,\n", "   0.02931804209947586,\n", "   -0.014257594011723995,\n", "   0.013406290672719479,\n", "   -0.04437849298119545,\n", "   -0.0066650789231061935,\n", "   0.01512966025620699,\n", "   0.018548713997006416,\n", "   -0.020929593592882156,\n", "   -0.031228283420205116,\n", "   0.04133317992091179,\n", "   -0.0012440788559615612,\n", "   0.018922457471489906,\n", "   0.025954358279705048,\n", "   0.026771055534482002,\n", "   -0.042468249797821045,\n", "   -0.013558556325733662,\n", "   -0.004893261473625898,\n", "   -0.02848750352859497,\n", "   -0.011323020793497562,\n", "   0.02415485493838787,\n", "   0.01092851534485817,\n", "   -0.04590114578604698,\n", "   0.007184166461229324,\n", "   0.06090622767806053,\n", "   -0.012776465155184269,\n", "   -0.010783170349895954,\n", "   0.001306369318626821,\n", "   0.032944731414318085,\n", "   0.01618167757987976,\n", "   0.02292288839817047,\n", "   0.020292846485972404,\n", "   -0.040142741054296494,\n", "   0.0005610467051155865,\n", "   -0.05725185200572014,\n", "   0.03164355456829071,\n", "   0.03012089803814888,\n", "   0.005298149771988392,\n", "   0.028376765549182892,\n", "   -0.00936433207243681,\n", "   0.04529208317399025,\n", "   0.013738506473600864,\n", "   -0.008028548210859299,\n", "   0.003630149643868208,\n", "   -0.01666615903377533,\n", "   0.008360764011740685,\n", "   0.01968378573656082,\n", "   -0.0008365954854525626,\n", "   0.007461012806743383,\n", "   0.014437544159591198,\n", "   -0.05093975365161896,\n", "   -0.013288631103932858,\n", "   0.05725185200572014,\n", "   0.035990044474601746,\n", "   -0.02274293825030327,\n", "   0.012554988265037537,\n", "   -0.022646041586995125,\n", "   0.03413517028093338,\n", "   0.03241872414946556,\n", "   -0.015531088225543499,\n", "   -0.0026577261742204428,\n", "   0.0014768029795959592,\n", "   -0.026798740029335022,\n", "   0.014271436259150505,\n", "   0.03313852474093437,\n", "   -0.09540130198001862,\n", "   -0.027906125411391258,\n", "   -0.0035713196266442537,\n", "   -0.008644531480967999,\n", "   0.035325612872838974,\n", "   0.03225261718034744,\n", "   0.009260514751076698,\n", "   0.05542466789484024,\n", "   -0.005886448547244072,\n", "   0.041693080216646194,\n", "   -0.06694147735834122,\n", "   0.011329942382872105,\n", "   -0.0028411371167749166,\n", "   -0.012070506811141968,\n", "   0.04603957012295723,\n", "   0.02260451577603817,\n", "   0.0451536625623703,\n", "   -0.024113329127430916,\n", "   0.03081301413476467,\n", "   0.014229909516870975,\n", "   0.04307731240987778,\n", "   0.010644746944308281,\n", "   -0.0022130415309220552,\n", "   0.02148328721523285,\n", "   0.021469444036483765,\n", "   -0.05091206729412079,\n", "   -0.01674921251833439,\n", "   0.014963552355766296,\n", "   0.03252946212887764,\n", "   -0.0017752781277522445,\n", "   0.014112249948084354,\n", "   0.04307731240987778,\n", "   0.04507060721516609,\n", "   0.010284846648573875,\n", "   0.04684242606163025,\n", "   0.01996063068509102,\n", "   -0.026577262207865715,\n", "   -0.011399153620004654,\n", "   -0.014589809812605381,\n", "   0.0009706930140964687,\n", "   0.010374821722507477,\n", "   0.03474423289299011,\n", "   0.045624300837516785,\n", "   0.017219850793480873,\n", "   -0.03471655026078224,\n", "   0.030563851818442345,\n", "   0.04277278110384941,\n", "   0.06948846578598022,\n", "   0.02308899722993374,\n", "   0.04576272517442703,\n", "   0.028349079191684723,\n", "   0.009689627215266228,\n", "   0.035270243883132935,\n", "   -0.011011568829417229,\n", "   0.028266025707125664,\n", "   0.0025002697948366404,\n", "   -0.015655668452382088,\n", "   0.027034059166908264,\n", "   -0.0035003777593374252,\n", "   -0.01982220821082592,\n", "   0.05143807828426361,\n", "   -0.020375901833176613,\n", "   -0.01709526963531971,\n", "   -0.07640963047742844,\n", "   0.06323173642158508,\n", "   0.02512381784617901,\n", "   0.018880929797887802,\n", "   0.07962104678153992,\n", "   0.018174972385168076,\n", "   -0.0026715686544775963,\n", "   0.022452250123023987,\n", "   -0.03936757147312164,\n", "   -0.041748449206352234,\n", "   0.04041958600282669,\n", "   -0.003072995925322175,\n", "   0.009184381924569607,\n", "   -0.03225261718034744,\n", "   -0.013828481547534466,\n", "   0.03255714848637581,\n", "   -0.0025954358279705048,\n", "   -0.026258889585733414,\n", "   0.03349842503666878,\n", "   0.032861676067113876,\n", "   -0.0020469336304813623,\n", "   -0.012603436596691608,\n", "   -0.02268756926059723,\n", "   0.030591536313295364,\n", "   -0.0036959005519747734,\n", "   0.03180966153740883,\n", "   0.01095619983971119,\n", "   -0.0331108383834362,\n", "   -0.03820481523871422,\n", "   -0.011724448762834072,\n", "   0.015392664819955826,\n", "   -0.022327668964862823,\n", "   0.00153303740080446,\n", "   0.04507060721516609,\n", "   0.018894772976636887,\n", "   0.03513181954622269,\n", "   -0.023047469556331635,\n", "   -0.0022978258784860373,\n", "   -0.019946789368987083,\n", "   -0.035934675484895706,\n", "   0.03704205900430679,\n", "   -0.03419053927063942,\n", "   -0.01629241555929184,\n", "   0.0073018260300159454,\n", "   0.003796257311478257,\n", "   -0.005183950532227755,\n", "   0.013696979731321335,\n", "   -0.02412717044353485,\n", "   -0.005104356911033392,\n", "   0.015212714672088623,\n", "   -0.02174629084765911,\n", "   -0.041582342237234116,\n", "   0.018576398491859436,\n", "   -0.0349380262196064,\n", "   -0.04227445647120476,\n", "   0.008582240901887417,\n", "   -0.019614573568105698,\n", "   0.01051324512809515,\n", "   -0.03554708883166313,\n", "   -0.01349626574665308,\n", "   -0.059854209423065186,\n", "   0.01578024961054325,\n", "   -0.05384664237499237,\n", "   0.012077427469193935,\n", "   -0.0033273487351834774,\n", "   0.06489281356334686,\n", "   0.018147287890315056,\n", "   0.03659910708665848,\n", "   0.04612262547016144,\n", "   0.015711037442088127,\n", "   0.0482543408870697,\n", "   0.05096743628382683,\n", "   -0.006007568910717964,\n", "   0.026203520596027374,\n", "   0.03463349491357803,\n", "   0.04263436049222946,\n", "   -0.040170423686504364,\n", "   -0.00874834880232811,\n", "   -0.01996063068509102,\n", "   0.04617799445986748,\n", "   0.0049832365475595,\n", "   -0.0352148711681366,\n", "   -0.014894341118633747,\n", "   0.024362491443753242,\n", "   -0.0035297926515340805,\n", "   0.00069298135349527,\n", "   -0.07856903225183487,\n", "   -0.032003454864025116,\n", "   -0.03324926272034645,\n", "   -0.03095143660902977,\n", "   -0.016167834401130676,\n", "   0.011980531737208366,\n", "   0.11450370401144028,\n", "   -0.0383155532181263,\n", "   0.001189574715681374,\n", "   -0.01950383558869362,\n", "   -0.015171186998486519,\n", "   0.03319389373064041,\n", "   0.0008781224605627358,\n", "   0.023338159546256065,\n", "   -0.015558772720396519,\n", "   0.006080240942537785,\n", "   0.03399674966931343,\n", "   0.02750469744205475,\n", "   0.020099055022001266,\n", "   -0.0009897261625155807,\n", "   0.007897046394646168,\n", "   -0.0724230408668518,\n", "   -0.02386416681110859,\n", "   -0.035325612872838974,\n", "   0.06699685007333755,\n", "   0.025539088994264603,\n", "   0.020375901833176613,\n", "   -0.010873145423829556,\n", "   -0.018631769344210625,\n", "   0.050441429018974304,\n", "   -0.003116253297775984,\n", "   -0.017635121941566467,\n", "   0.003446738701313734,\n", "   -0.00045593155664391816,\n", "   -0.0023583860602229834,\n", "   -0.03095143660902977,\n", "   -0.013696979731321335,\n", "   -0.021649396046996117,\n", "   0.029511835426092148,\n", "   -0.0081185232847929,\n", "   -0.011440681293606758,\n", "   -0.014119170606136322,\n", "   0.021012648940086365,\n", "   -0.024763917550444603,\n", "   -0.017316747456789017,\n", "   -0.020126739516854286,\n", "   -0.02369805984199047,\n", "   -0.00755790900439024,\n", "   -0.10237783193588257,\n", "   -0.013004863634705544,\n", "   -0.06201361119747162,\n", "   -0.03477191925048828,\n", "   -0.032058823853731155,\n", "   0.018451817333698273,\n", "   -0.02538682334125042,\n", "   -0.002451821696013212,\n", "   -0.004768680781126022,\n", "   -0.010153344832360744,\n", "   0.010900829918682575,\n", "   -0.02940109744668007,\n", "   -0.014409859664738178,\n", "   -0.0015339025994762778,\n", "   0.017856597900390625,\n", "   -0.004166539758443832,\n", "   -0.0031249045860022306,\n", "   -0.008976747281849384,\n", "   0.040696434676647186,\n", "   0.0008262137416750193,\n", "   -0.021178755909204483,\n", "   -0.013558556325733662,\n", "   -0.022452250123023987,\n", "   0.012554988265037537,\n", "   -0.04266204312443733,\n", "   0.03870313987135887,\n", "   0.00376511225476861,\n", "   0.04894645884633064,\n", "   -0.001030388055369258,\n", "   0.011433759704232216,\n", "   -0.015711037442088127,\n", "   0.0176074355840683,\n", "   -0.008769112639129162,\n", "   0.004003892187029123,\n", "   0.005893369670957327,\n", "   0.01933772675693035,\n", "   -0.04368637502193451,\n", "   -0.012984099797904491,\n", "   0.012465013191103935,\n", "   0.004678705707192421,\n", "   -0.020417427644133568,\n", "   0.023822639137506485,\n", "   0.003405211726203561,\n", "   -0.01211895514279604,\n", "   0.038287870585918427,\n", "   -0.03175429254770279,\n", "   -0.01950383558869362,\n", "   -0.011814423836767673,\n", "   -0.007793228607624769,\n", "   -6.726504216203466e-05,\n", "   -0.02254914678633213,\n", "   0.006543958559632301,\n", "   -0.003280630800873041,\n", "   0.003003784455358982,\n", "   -0.011807502247393131,\n", "   -0.017662806436419487,\n", "   0.021621709689497948,\n", "   -0.02231382578611374,\n", "   0.0029466848354786634,\n", "   0.03914609178900719,\n", "   -0.03801102191209793,\n", "   -0.04061337932944298,\n", "   -0.019462307915091515,\n", "   0.027158640325069427,\n", "   0.0362115204334259,\n", "   0.022618357092142105,\n", "   -0.010644746944308281,\n", "   0.058414608240127563,\n", "   0.005689195357263088,\n", "   0.01663847453892231,\n", "   -0.009032116271555424,\n", "   -0.02564982697367668,\n", "   -0.014492913149297237,\n", "   0.0070180585607886314,\n", "   -0.02137254923582077,\n", "   0.05622752010822296,\n", "   0.019669942557811737,\n", "   0.00732258940115571,\n", "   0.013080996461212635,\n", "   -0.03012089803814888,\n", "   0.004259975161403418,\n", "   -0.01944846473634243,\n", "   0.012070506811141968,\n", "   0.02033437415957451,\n", "   -0.0029034274630248547,\n", "   -0.010201793164014816,\n", "   -0.03355379402637482,\n", "   0.007959336042404175,\n", "   0.015766406431794167,\n", "   0.01982220821082592,\n", "   -0.01404303777962923,\n", "   0.015544930472970009,\n", "   -0.017026059329509735,\n", "   -0.0701529011130333,\n", "   -0.0023220498114824295,\n", "   0.003747809212654829,\n", "   0.016846109181642532,\n", "   0.00830539409071207,\n", "   0.022770622745156288,\n", "   0.009966473095119,\n", "   0.006308639422059059,\n", "   0.00733643164858222,\n", "   0.013724664226174355,\n", "   -0.040862541645765305,\n", "   0.010707037523388863,\n", "   0.011288415640592575,\n", "   -0.03549171984195709,\n", "   -0.018825560808181763,\n", "   -0.048032864928245544,\n", "   -0.006066398695111275,\n", "   0.03003784269094467,\n", "   -0.02859824150800705,\n", "   -0.01701221615076065,\n", "   0.03643299639225006,\n", "   0.027809228748083115,\n", "   0.01510197576135397,\n", "   0.007108033634722233,\n", "   0.034495070576667786,\n", "   -0.010769328102469444,\n", "   -0.005045527126640081,\n", "   -0.024334806948900223,\n", "   -0.00925359409302473,\n", "   0.0018842864083126187,\n", "   -0.029179619625210762,\n", "   -0.012181245721876621,\n", "   -0.02062506228685379,\n", "   -0.0035159504041075706,\n", "   0.033609163016080856,\n", "   -0.041665397584438324,\n", "   -0.03969978541135788,\n", "   -0.03626688942313194,\n", "   -0.00672044837847352,\n", "   -0.010070291347801685,\n", "   0.029760997742414474,\n", "   0.006031792610883713,\n", "   0.019005510956048965,\n", "   -0.03006552904844284,\n", "   0.0010087593691423535,\n", "   0.04357563704252243,\n", "   -0.028902772814035416,\n", "   -0.017593594267964363,\n", "   -0.001318481401540339,\n", "   -0.011759054847061634,\n", "   -0.006111386232078075,\n", "   0.007433328311890364,\n", "   0.03632225841283798,\n", "   -0.034578125923871994,\n", "   0.04097327962517738,\n", "   0.00028484908398240805,\n", "   -0.017372116446495056,\n", "   -0.04501523822546005,\n", "   -0.042329829186201096,\n", "   0.02736627496778965,\n", "   -0.002993402536958456,\n", "   -0.011537577025592327,\n", "   -0.030010158196091652,\n", "   -0.005138962995260954,\n", "   -0.021926240995526314,\n", "   0.017482856288552284,\n", "   -0.0013954793103039265,\n", "   0.00036098185228183866,\n", "   0.01346858125180006,\n", "   -0.003844705643132329,\n", "   -0.02765696309506893,\n", "   0.005993726197630167,\n", "   0.008457659743726254,\n", "   0.015434191562235355,\n", "   -0.015987884253263474,\n", "   -0.01111538615077734,\n", "   0.003207958536222577,\n", "   -0.009357411414384842,\n", "   -0.01575256511569023,\n", "   -0.005395045969635248,\n", "   0.014562125317752361,\n", "   -0.0035176805686205626,\n", "   -0.010630904696881771,\n", "   -0.03983820974826813,\n", "   -0.02249377779662609,\n", "   -0.014686706475913525,\n", "   0.005367361009120941,\n", "   0.00937817431986332,\n", "   0.010014921426773071,\n", "   0.007848598062992096,\n", "   -0.011669079773128033,\n", "   9.311125904787332e-05,\n", "   -0.03230798617005348,\n", "   0.02816912904381752,\n", "   -0.0006146857049316168,\n", "   0.01465902104973793,\n", "   -0.011800581589341164,\n", "   -0.015821777284145355,\n", "   0.018078075721859932,\n", "   0.019614573568105698,\n", "   0.022023137658834457,\n", "   -0.002415485680103302,\n", "   0.0007466203533113003,\n", "   -0.009786522947251797,\n", "   0.01330939494073391,\n", "   -0.020292846485972404,\n", "   -0.021303337067365646,\n", "   0.010229477658867836,\n", "   -0.02868129499256611,\n", "   -0.03394138067960739,\n", "   -0.0045610456727445126,\n", "   -0.003379257395863533,\n", "   0.00433956878259778,\n", "   0.0009109979728236794,\n", "   0.0315604992210865,\n", "   -0.011454523541033268,\n", "   -0.02386416681110859,\n", "   0.000956850650254637,\n", "   0.02131718024611473,\n", "   -0.051327336579561234,\n", "   0.002090191002935171,\n", "   0.009530439972877502,\n", "   0.013613926246762276,\n", "   -0.013696979731321335,\n", "   0.029650257900357246,\n", "   0.021164914593100548,\n", "   -0.01583561860024929,\n", "   0.007274141535162926,\n", "   0.0025106514804065228,\n", "   -0.03543635085225105,\n", "   0.00018643880321178585,\n", "   0.009994158521294594,\n", "   -0.023628847673535347,\n", "   0.021151071414351463,\n", "   -0.03646068274974823,\n", "   -0.0473407506942749,\n", "   -0.015337294898927212,\n", "   -0.026438839733600616,\n", "   -0.0042841993272304535,\n", "   0.03084069862961769,\n", "   0.017455171793699265,\n", "   0.014991236850619316,\n", "   0.03347073867917061,\n", "   0.017593594267964363,\n", "   -0.008914456702768803,\n", "   -0.026258889585733414,\n", "   -0.01510197576135397,\n", "   0.034522756934165955,\n", "   0.02054200880229473,\n", "   0.02182934619486332,\n", "   0.019351569935679436,\n", "   -0.011226125061511993,\n", "   0.021649396046996117,\n", "   -0.015281925909221172,\n", "   0.008367684669792652,\n", "   0.043271105736494064,\n", "   -0.044046275317668915,\n", "   0.017538225278258324,\n", "   0.015198872424662113,\n", "   0.01987757720053196,\n", "   -0.018368763849139214,\n", "   -0.013122523203492165,\n", "   -0.00733643164858222,\n", "   0.0016636743675917387,\n", "   -0.008900614455342293,\n", "   0.024113329127430916,\n", "   0.01150297187268734,\n", "   0.021677080541849136,\n", "   -0.017538225278258324,\n", "   -0.014949710108339787,\n", "   -0.002140369499102235,\n", "   0.015572614967823029,\n", "   -0.020791171118617058,\n", "   0.02843213453888893,\n", "   0.03742964565753937,\n", "   -0.03416285663843155,\n", "   -0.009205145761370659,\n", "   -0.027297062799334526,\n", "   0.02463933639228344,\n", "   -0.03662678971886635,\n", "   -0.012845677323639393,\n", "   -0.0104440338909626,\n", "   0.01795349456369877,\n", "   0.0012553257402032614,\n", "   0.016361627727746964,\n", "   -0.024376332759857178,\n", "   0.013745428062975407,\n", "   0.0389523021876812,\n", "   -0.020375901833176613,\n", "   -0.03718048334121704,\n", "   -0.022825993597507477,\n", "   0.025857461616396904,\n", "   -0.00439493777230382,\n", "   0.029982473701238632,\n", "   -0.002476045861840248,\n", "   -0.022161561995744705,\n", "   -0.011475286446511745,\n", "   0.0018237262265756726,\n", "   0.0057860915549099445,\n", "   -0.015655668452382088,\n", "   0.00366821582429111,\n", "   -0.03413517028093338,\n", "   0.028210656717419624,\n", "   -0.039229147136211395,\n", "   -0.01273493841290474,\n", "   -0.014368332922458649,\n", "   0.014188382774591446,\n", "   0.009129012934863567,\n", "   -0.008623767644166946,\n", "   0.024777760729193687,\n", "   0.00468908715993166,\n", "   -0.020320532843470573,\n", "   0.003316967049613595,\n", "   -0.04194224253296852,\n", "   0.021953925490379333,\n", "   0.02645268104970455,\n", "   0.007820913568139076,\n", "   -0.014769759960472584,\n", "   -0.004246132913976908,\n", "   -0.018742507323622704,\n", "   0.027587752789258957,\n", "   0.021261809393763542,\n", "   -0.001171406707726419,\n", "   -0.018285710364580154,\n", "   0.03097912110388279,\n", "   0.0012008215999230742,\n", "   -0.000981939840130508,\n", "   0.0013972095912322402,\n", "   -0.02587130479514599,\n", "   -0.001048556063324213,\n", "   0.02452859841287136,\n", "   -0.004481452517211437,\n", "   0.0292349886149168,\n", "   0.033802956342697144,\n", "   0.0030089751817286015,\n", "   -0.008942141197621822,\n", "   -0.003377527231350541,\n", "   -0.009232830256223679,\n", "   -0.0018289170693606138,\n", "   -0.027324747294187546,\n", "   -0.037678807973861694,\n", "   0.02389185130596161,\n", "   -0.01629241555929184,\n", "   -0.014119170606136322,\n", "   0.04208066686987877,\n", "   0.013461660593748093,\n", "   -0.007056124974042177,\n", "   0.021275652572512627,\n", "   0.019974473863840103,\n", "   0.006817344576120377,\n", "   0.005346597637981176,\n", "   -0.021524814888834953,\n", "   -0.019143935292959213,\n", "   -0.024376332759857178,\n", "   0.0019085104577243328,\n", "   0.00914977677166462,\n", "   0.01286644022911787,\n", "   0.0032339130993932486,\n", "   0.027407802641391754,\n", "   0.03953367844223976,\n", "   0.039312202483415604,\n", "   0.0020192491356283426,\n", "   0.012312747538089752,\n", "   0.036100782454013824,\n", "   0.01393922045826912,\n", "   0.04293888807296753,\n", "   -0.040004316717386246,\n", "   0.025137661024928093,\n", "   0.009807286784052849,\n", "   0.022646041586995125,\n", "   0.034412018954753876,\n", "   0.01575256511569023,\n", "   -0.02065274864435196,\n", "   -0.0732535794377327,\n", "   -0.021234124898910522,\n", "   0.0057860915549099445,\n", "   0.04282815009355545,\n", "   -0.010305610485374928,\n", "   8.786631951807067e-05,\n", "   0.005889908876270056,\n", "   -0.02030668966472149,\n", "   -0.0017077967058867216,\n", "   0.05287767946720123,\n", "   0.01859024167060852,\n", "   -0.029899420216679573,\n", "   -0.007163402624428272,\n", "   -0.03504876419901848,\n", "   0.006592406891286373,\n", "   -0.01741364412009716,\n", "   0.023933378979563713,\n", "   -0.015420349314808846,\n", "   -0.03479960188269615,\n", "   0.019697627052664757,\n", "   0.0100564481690526,\n", "   0.014534440822899342,\n", "   0.02105417475104332,\n", "   -0.01847950369119644,\n", "   0.01824418269097805,\n", "   0.04969394579529762,\n", "   -0.014575967565178871,\n", "   -0.008983668871223927,\n", "   -0.04210834950208664,\n", "   0.01984989270567894,\n", "   -0.019005510956048965,\n", "   0.017565909773111343,\n", "   -0.010900829918682575,\n", "   0.034467387944459915,\n", "   0.008907536044716835,\n", "   0.049666259437799454,\n", "   -0.03388600796461105,\n", "   -0.029179619625210762,\n", "   0.026729527860879898,\n", "   -0.027186324819922447,\n", "   0.004315344616770744,\n", "   0.03089606761932373,\n", "   0.053597480058670044,\n", "   0.01388385146856308,\n", "   0.004076564684510231,\n", "   -0.0017147179460152984,\n", "   0.00435341103002429,\n", "   -0.01672152802348137,\n", "   -0.02343505434691906,\n", "   -0.041610024869441986,\n", "   0.030287005007267,\n", "   -0.018604082986712456,\n", "   0.011433759704232216,\n", "   0.003292742883786559,\n", "   0.037651121616363525,\n", "   -0.0059452783316373825,\n", "   -0.011579103767871857,\n", "   0.01052708737552166,\n", "   -0.04675937071442604,\n", "   0.02848750352859497,\n", "   -0.01829955354332924,\n", "   -0.01629241555929184,\n", "   -0.003201037412509322,\n", "   0.0007137448410503566,\n", "   -0.01512966025620699,\n", "   0.04263436049222946,\n", "   0.02180165983736515,\n", "   0.0012466743355616927,\n", "   0.0023687677457928658,\n", "   0.014825128950178623,\n", "   -0.028819719329476357,\n", "   0.02707558684051037,\n", "   0.03230798617005348,\n", "   0.011011568829417229,\n", "   -0.005273925606161356,\n", "   -0.0023480041418224573,\n", "   0.024376332759857178,\n", "   -0.014797444455325603,\n", "   0.013226340524852276,\n", "   0.032778624445199966,\n", "   0.018119601532816887,\n", "   0.022756781429052353,\n", "   -0.02177397534251213,\n", "   0.03089606761932373,\n", "   0.015489560551941395,\n", "   0.017109112814068794,\n", "   -0.0041250125505030155,\n", "   0.007350274361670017,\n", "   0.07236766815185547,\n", "   0.010146423242986202,\n", "   0.02722785249352455,\n", "   0.014935867860913277,\n", "   0.040115054696798325,\n", "   0.028708981350064278,\n", "   -0.003304854966700077,\n", "   -0.006422838661819696,\n", "   0.009613494388759136,\n", "   0.022867519408464432,\n", "   -0.015489560551941395,\n", "   -0.0017631660448387265,\n", "   0.0352148711681366,\n", "   0.0007976639317348599,\n", "   0.009302041493356228,\n", "   -0.022770622745156288,\n", "   -0.06417301297187805,\n", "   -0.002195738721638918,\n", "   0.017206009477376938,\n", "   ...],\n", "  'index': 0,\n", "  'object': 'embedding'}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["res.model_dump()['data']"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from heracles.agent_workspace.playbook_db_tool import PlaybookDatabaseTool"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/clackyai/lib/python3.11/site-packages/qdrant_client/async_qdrant_remote.py:117: UserWarning: Api key is used with an insecure connection.\n", "  warnings.warn(\"Api key is used with an insecure connection.\")\n"]}], "source": ["from fastapi import FastAPI\n", "from qdrant_client import models\n", "from heracles.core.schema import AdminPlaybookModel, AdminPlaybookQuery, AdminDataResponse\n", "from heracles.core.utils.admin_utils import (\n", "    record_to_playbook,\n", "    playbook_to_point,\n", "    create_dense_embedding,\n", "    create_sparse_embedding,\n", ")\n", "from heracles.server.admin.config import get_settings, get_vector_db_client, get_sparse_model\n", "\n", "settings = get_settings()\n", "vector_db_client = get_vector_db_client()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["records, _ = await vector_db_client.scroll(\n", "        collection_name=\"playbooks\", limit=50, with_payload=True, with_vectors=False\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["playbooks = list(map(record_to_playbook, records))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"id\":\"212714c8-f707-441f-b227-603b0478b0af\",\"title\":\"Empty data elements with value representation SQ are set to None\",\"description\":\"Empty data elements with value representation SQ are set to None **Describe the bug** In the current `master`, empty data elements are not read correctly from files. The attribute value is set to `None` instead of `[]`.  **Expected behavior** Create empty list `[]` for empty sequence, i.e., a sequence with zero items.  **Steps To Reproduce** ```python import pydicom ds = pydicom.Dataset() ds.AcquisitionContextSequence = [] print(ds) ds.is_little_endian = True ds.is_implicit_VR = True ds.save_as('/tmp/test.dcm')  reloaded_ds = pydicom.dcmread('/tmp/test.dcm', force=True) print(reloaded_ds) ``` This prints: ``` (0040, 0555)  Acquisition Context Sequence   0 item(s) ---- ... TypeError: With tag (0040, 0555) got exception: object of type 'NoneType' has no len() Traceback (most recent call last):   File \\\"/private/tmp/pydicom/pydicom/tag.py\\\", line 30, in tag_in_exception     yield   File \\\"/private/tmp/pydicom/pydicom/dataset.py\\\", line 1599, in _pretty_str     len(data_element.value))) TypeError: object of type 'NoneType' has no len() ```  **Your environment** ``` Darwin-18.6.0-x86_64-i386-64bit Python  3.7.3 (default, Mar 27 2019, 09:23:15) [Clang 10.0.1 (clang-1001.0.46.3)] pydicom  1.4.0.dev0 ```\",\"tags\":[],\"original_content\":\"## Overview\\r\\nFix handling of empty data elements for VR 'SQ' in pydicom\\r\\nThis update addresses the issue where empty data elements with a value representation (VR) of 'SQ' in DICOM files were incorrectly set to `None` instead of an empty list `[]` upon reading. The solution modifies the behavior to ensure empty sequences are correctly represented as empty lists.\\r\\n\\r\\n## Procedure\\r\\n1. Update configuration explanation in config.py:\\r\\n   - Modify the documentation for `use_none_as_empty_text_VR_value` in `pydicom/config.py` to clarify behavior for various VRs, emphasizing that VR 'SQ' should always result in an empty list.\\r\\n\\r\\n2. Adjust the empty value logic in dataelem.py:\\r\\n   - In `pydicom/dataelem.py`, update the function `empty_value_for_VR` to return `[]` specifically for VR 'SQ', ensuring that empty sequences are represented correctly.\\r\\n\\r\\n3. Retain existing settings for other VRs:\\r\\n   - Ensure that the function `empty_value_for_VR` continues to respect the current setting of `config.use_none_as_empty_text_VR_value` for text VRs, and default behavior for other VRs remains unchanged, except for the specific handling of VR 'SQ'.\\r\\n\\r\\n## Git Diff Messages\\r\\n```\\r\\ndiff --git a/pydicom/config.py b/pydicom/config.py\\r\\n--- a/pydicom/config.py\\r\\n+++ b/pydicom/config.py\\r\\n@@ -87,9 +87,10 @@ def DS_decimal(use_Decimal_boolean=True):\\r\\n \\\"\\\"\\\"\\r\\n \\r\\n use_none_as_empty_text_VR_value = False\\r\\n-\\\"\\\"\\\" If ``True``, the value of decoded empty data element is always ``None``.\\r\\n-If ``False`` (the default), the value of an empty data element with\\r\\n-a text VR is an empty string, for all other VRs it is also ``None``.\\r\\n+\\\"\\\"\\\" If ``True``, the value of a decoded empty data element with\\r\\n+a text VR is ``None``, otherwise (the default), it is is an empty string.\\r\\n+For all other VRs the behavior does not change - the value is en empty\\r\\n+list for VR 'SQ' and ``None`` for all other VRs.\\r\\n Note that the default of this value will change to ``True`` in version 2.0.\\r\\n \\\"\\\"\\\"\\r\\n \\r\\ndiff --git a/pydicom/dataelem.py b/pydicom/dataelem.py\\r\\n--- a/pydicom/dataelem.py\\r\\n+++ b/pydicom/dataelem.py\\r\\n@@ -48,10 +48,12 @@ def empty_value_for_VR(VR, raw=False):\\r\\n \\r\\n     The behavior of this property depends on the setting of\\r\\n     :attr:`config.use_none_as_empty_value`. If that is set to ``True``,\\r\\n-    an empty value is always represented by ``None``, otherwise it depends\\r\\n-    on `VR`. For text VRs (this includes 'AE', 'AS', 'CS', 'DA', 'DT', 'LO',\\r\\n-    'LT', 'PN', 'SH', 'ST', 'TM', 'UC', 'UI', 'UR' and 'UT') an empty string\\r\\n-    is used as empty value representation, for all other VRs, ``None``.\\r\\n+    an empty value is represented by ``None`` (except for VR 'SQ'), otherwise\\r\\n+    it depends on `VR`. For text VRs (this includes 'AE', 'AS', 'CS', 'DA',\\r\\n+    'DT', 'LO', 'LT', 'PN', 'SH', 'ST', 'TM', 'UC', 'UI', 'UR' and 'UT') an\\r\\n+    empty string is used as empty value representation, for all other VRs\\r\\n+    except 'SQ', ``None``. For empty sequence values (VR 'SQ') an empty list\\r\\n+    is used in all cases.\\r\\n     Note that this is used only if decoding the element - it is always\\r\\n     possible to set the value to another empty value representation,\\r\\n     which will be preserved during the element object lifetime.\\r\\n@@ -67,10 +69,12 @@ def empty_value_for_VR(VR, raw=False):\\r\\n \\r\\n     Returns\\r\\n     -------\\r\\n-    str or bytes or None\\r\\n+    str or bytes or None or list\\r\\n         The value a data element with `VR` is assigned on decoding\\r\\n         if it is empty.\\r\\n     \\\"\\\"\\\"\\r\\n+    if VR == 'SQ':\\r\\n+        return []\\r\\n     if config.use_none_as_empty_text_VR_value:\\r\\n         return None\\r\\n     if VR in ('AE', 'AS', 'CS', 'DA', 'DT', 'LO', 'LT',\\r\\n```\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"3de75ba9-3219-479a-872b-95b0673ec61c\",\"title\":\"Find Minimum Diameter After Merging Two Trees\",\"description\":\"Solution: Find Minimum Diameter After Merging Two Trees\",\"tags\":[],\"original_content\":\"# Find Minimum Diameter After Merging Two Trees\\r\\n\\r\\n## Solution\\r\\n\\r\\n## Overview\\r\\n\\r\\nWe are given two trees: one with n nodes and the other with m nodes. Our goal is to add an edge between a node from the first tree and a node from the second tree, in such a way that the diameter of the resulting tree is minimized.\\r\\n\\r\\n> The diameter of a tree is the longest path between any two nodes in the tree.\\r\\n\\r\\nLet us consider the two ways that the longest path can be formed:\\r\\n\\r\\n1 The path starts and ends at nodes within the same tree.\\r\\n\\r\\nIn this case, the problem reduces to finding the maximum diameter of the two original trees.\\r\\n\\r\\n2 The path starts at a node in the first tree and ends at a node in the second.\\r\\n\\r\\nIn this case, the selection of the nodes to connect is crucial for minimizing the overall diameter. Intuitively, we aim to select these nodes so that, if chosen as roots, the heights of their respective trees are minimized. In practice, this often involves selecting nodes near the \\\"center\\\" of each tree, ensuring their subtrees are as balanced as possible. This property is associated with the centroid(s) of a tree:\\r\\n\\r\\n> Centroid of a tree is a node which if removed from the tree would split it into a ‘forest’, such that any tree in the forest would have at most half the number of vertices in the original tree.\\r\\n\\r\\nBy adding an edge between two centroids of the trees, the maximum distance between each of them and a node within the same tree is at most \\r\\nfloor(diameter/2). Thus, the combined diameter of the tree is the sum of the halves of the original diameters plus one for the extra edge:\\r\\n\\r\\nfloor(diameter_1 / 2) + floor(diameter _2 / 2) + 1\\r\\n\\r\\nTherefore, the problem simplifies to returning the maximum among the diameter of each tree and the above value.\\r\\n\\r\\nFeel free to try solving these problems first as great prerequisites to this one:\\r\\n1. Minimum Height Trees.\\r\\n2. Tree Diameter\\r\\n\\r\\n## Approach 1: Farthest of Farthest (BFS)\\r\\n\\r\\n### Intuition\\r\\n\\r\\nLet's break down the problem of calculating the diameter of a tree. First of all, we observe that any tree can be seen as:\\r\\n\\r\\n- The sequence of nodes on the diameter itself, plus\\r\\n- Additional subtrees branching out from nodes along the diameter.\\r\\n\\r\\nFor any node in the tree, its minimum distance to one of the diameter's endpoints (say a and b) is always less than or equal to the diameter. This can be proven via contradiction. If one endpoint of the diameter (a) is known, the other endpoint (b) is simply the farthest node from a.\\r\\n\\r\\nBased on that, one naive way to find the diameter is:\\r\\n\\r\\n1 Assume each node is one endpoint of the diameter.\\r\\n2 Calculate the farthest node from it.\\r\\n3 Record the longest path found.\\r\\nHowever, this approach involves computing the farthest node for all nodes, leading to a time complexity of O(n^2)\\r\\n, which will result in a TLE (Time Limit Exceeded) for the given constraints.\\r\\n\\r\\nFor the optimized approach, we observe that we only need to find the farthest node of a single arbitrary node u and that node would be one of the endpoints of the diameter. Why does this work? Let's consider the following cases:\\r\\n\\r\\nCase 1: u lies on the diameter\\r\\nRunning a BFS for the longest path from u will find an endpoint of the diameter.\\r\\n\\r\\nCase 2: u does not lie on the diameter\\r\\nThe path from u to the farthest node passes through the diameter so the problem reduces to Case 1.\\r\\n\\r\\nTherefore, to calculate the diameter of a tree, only two BFS calls are needed:\\r\\n\\r\\n1 First BFS starting from any arbitrary node to find the farthest node from it, which is also an endpoint of the diameter.\\r\\n2 Second BFS starting from this farthest node to find the farthest node from it, which is equal to the second endpoint of the diameter.\\r\\n> Breadth-First Search (BFS): For a more comprehensive understanding of breadth-first search, check out the BFS Explore Card. This resource provides an in-depth look at BFS, explaining its key concepts and applications with a variety of problems to solidify understanding of the pattern.\\r\\n\\r\\n### Algorithm\\r\\n\\r\\nMain Function: minimumDiameterAfterMerge\\r\\n- Calculate the number of nodes for each tree:\\r\\n\\r\\n\\t- n is the number of nodes in Tree 1.\\r\\n\\t- m is the number of nodes in Tree 2.\\r\\n- Build adjacency lists for both trees:\\r\\n\\r\\n\\t- Call buildAdjList(n, edges1) to construct the adjacency list for the first tree.\\r\\n\\t- Call buildAdjList(m, edges2) to construct the adjacency list for the second tree.\\r\\n- Calculate the diameters of both trees:\\r\\n\\r\\n\\t- Call findDiameter(n, adjList1) to find the diameter of the first tree.\\r\\n\\t- Call findDiameter(m, adjList2) to find the diameter of the second tree.\\r\\n- Calculate the longest path that spans across both trees:\\r\\n\\r\\n\\t- Calculate combinedDiameter as the sum of half the diameters of both trees, plus 1 (rounded up).\\r\\n- Return the maximum of the three possibilities:\\r\\n\\r\\nReturn the maximum of diameter1, diameter2, and combinedDiameter.\\r\\n\\r\\nbuildAdjList function:\\r\\n- Create an adjacency list of size size.\\r\\n- For each edge in edges, add the nodes to each other's adjacency list.\\r\\n\\r\\nfindDiameter function:\\r\\n- Call findFarthestNode(n, adjList, 0) to find the farthest node from an arbitrary starting node (e.g., node 0).\\r\\n- Call findFarthestNode(n, adjList, farthestNode) from the previously found farthest node to determine the tree diameter.\\r\\n\\r\\nfindFarthestNode function:\\r\\n- Initialize a queue and a visited array to perform BFS starting from sourceNode.\\r\\n- Traverse the graph, updating the farthest node each time a node is dequeued.\\r\\n- Return the farthest node and the distance (diameter).\\r\\n\\r\\n### Implementation\\r\\n\\r\\n```\\r\\nclass Solution:\\r\\n    def minimumDiameterAfterMerge(self, edges1, edges2):\\r\\n        # Calculate the number of nodes for each tree\\r\\n        n = len(edges1) + 1\\r\\n        m = len(edges2) + 1\\r\\n\\r\\n        # Build adjacency lists for both trees\\r\\n        adj_list1 = self.build_adj_list(n, edges1)\\r\\n        adj_list2 = self.build_adj_list(m, edges2)\\r\\n\\r\\n        # Calculate the diameters of both trees\\r\\n        diameter1 = self.find_diameter(n, adj_list1)\\r\\n        diameter2 = self.find_diameter(m, adj_list2)\\r\\n\\r\\n        # Calculate the longest path that spans across both trees\\r\\n        combined_diameter = ceil(diameter1 / 2) + ceil(diameter2 / 2) + 1\\r\\n\\r\\n        # Return the maximum of the three possibilities\\r\\n        return max(diameter1, diameter2, combined_diameter)\\r\\n\\r\\n    def build_adj_list(self, size, edges):\\r\\n        adj_list = [[] for _ in range(size)]\\r\\n        for edge in edges:\\r\\n            adj_list[edge[0]].append(edge[1])\\r\\n            adj_list[edge[1]].append(edge[0])\\r\\n        return adj_list\\r\\n\\r\\n    def find_diameter(self, n, adj_list):\\r\\n        # First BFS to find the farthest node from an arbitrary node (e.g., 0)\\r\\n        farthest_node, _ = self.find_farthest_node(n, adj_list, 0)\\r\\n\\r\\n        # Second BFS to find the diameter starting from the farthest node\\r\\n        _, diameter = self.find_farthest_node(n, adj_list, farthest_node)\\r\\n        return diameter\\r\\n\\r\\n    def find_farthest_node(self, n, adj_list, source_node):\\r\\n        queue = deque([source_node])\\r\\n        visited = [False] * n\\r\\n        visited[source_node] = True\\r\\n\\r\\n        maximum_distance = 0\\r\\n        farthest_node = source_node\\r\\n\\r\\n        while queue:\\r\\n            for _ in range(len(queue)):\\r\\n                current_node = queue.popleft()\\r\\n                farthest_node = current_node\\r\\n\\r\\n                for neighbor in adj_list[current_node]:\\r\\n                    if not visited[neighbor]:\\r\\n                        visited[neighbor] = True\\r\\n                        queue.append(neighbor)\\r\\n\\r\\n            if queue:\\r\\n                maximum_distance += 1\\r\\n\\r\\n        return farthest_node, maximum_distance\\r\\n```\\r\\n\\r\\n### Complexity Analysis\\r\\n\\r\\nLet n be the number of nodes in the first tree and m the number of nodes in the second tree.\\r\\n\\r\\nTime complexity: O(n+m)\\r\\n\\r\\nTo calculate the diameter of a tree, we perform two BFS calls using the findFarthestNode function. Each BFS visits every node and edge exactly once, and since the number of edges is k−1=O(k) for a tree of size k, the time complexity of one BFS is O(k). Thus, finding the diameter of the first tree takes O(n), and for the second tree, it takes O(m), as each involves two BFS calls.\\r\\n\\r\\nThe combined diameter of the tree is calculated using constant-time operations like addition and comparison, contributing O(1) to the overall time complexity of O(n+m).\\r\\n\\r\\nSpace complexity: O(n+m)\\r\\n\\r\\nAll the data structures used in the algorithm, including the adjacency lists, the visited array, and the nodesQueue, have linear space complexity in terms of the size of the tree being processed. Therefore, the total space complexity is O(n+m).\\r\\n\\r\\n## Approach 2: Depth First Search\\r\\n\\r\\n### Intuition\\r\\n\\r\\nLet’s start with a simple observation based on the definition of the diameter:\\r\\n\\r\\n- For each node in the tree, we calculate the length of the longest path passing through it. The longest of these paths represents the diameter of the tree.\\r\\nTo determine the longest path that passes through a node u, we perform a DFS to calculate the two longest distances from u to any leaf nodes in the tree. The sum of these two distances gives the length of the longest path through u.\\r\\n\\r\\nDuring the recursive calls, each node returns two values:\\r\\n\\r\\n1 The diameter of its subtree.\\r\\n2 The longest path to a leaf in its subtree, or its depth. This avoids redundant calculations, reusing previously computed values.\\r\\n> Depth-First Search (DFS): For a more comprehensive understanding of depth-first search, check out the DFS Explore Card. This resource provides an in-depth look at DFS, explaining its key concepts and applications with a variety of problems to solidify understanding of the pattern.\\r\\n\\r\\nAlgorithm\\r\\nMain Function: minimumDiameterAfterMerge\\r\\n- Calculate the number of nodes for each tree:\\r\\n\\t- n is the number of nodes in Tree 1.\\r\\n\\t- m is the number of nodes in Tree 2.\\r\\n- Build adjacency lists for both trees:\\r\\n\\t- Use the buildAdjList function to construct the adjacency list for both trees (adjList1 and adjList2).\\r\\n- Find the diameter of Tree 1:\\r\\n\\t- Call findDiameter(adjList1, 0, -1) to start a DFS from node 0 in Tree 1.\\r\\n\\t- Store the diameter of Tree 1 in diameter1.\\r\\n- Find the diameter of Tree 2:\\r\\n\\t- Call findDiameter(adjList2, 0, -1) to start a DFS from node 0 in Tree 2.\\r\\n\\t- Store the diameter of Tree 2 in diameter2.\\r\\n- Calculate the diameter of the combined tree:\\r\\n\\t- The combined diameter accounts for the longest path spanning both trees.\\r\\n\\t- It is calculated as ceil(diameter1 / 2.0) + ceil(diameter2 / 2.0) + 1.\\r\\n- Return the maximum diameter:\\r\\n\\t- Return the maximum of the three values: diameter1, diameter2, and combinedDiameter.\\r\\nHelper Function: buildAdjList\\r\\n- Given the number of nodes size and an edge list edges, build an adjacency list (adjList):\\r\\n\\t- Iterate through each edge and add the corresponding nodes to the adjacency list.\\r\\nHelper Function: findDiameter\\r\\n- Given the adjacency list adjList, the current node, and its parent, calculate the diameter of the tree:\\r\\n\\t- Initialize two variables maxDepth1 and maxDepth2 to track the two largest depths from the current node.\\r\\n\\t- Initialize diameter to track the diameter of the subtree.\\r\\nFor each neighbor of the current node:\\r\\n\\r\\n\\t- Skip the parent node to avoid cycles.\\r\\n\\t- Recursively calculate the diameter and depth of the neighbor’s subtree.\\r\\n\\t- Update diameter with the maximum of the current diameter and the child’s diameter.\\r\\n\\t- Increment the depth and update the two largest depths (maxDepth1 and maxDepth2).\\r\\n- The diameter of the current node is updated as maxDepth1 + maxDepth2.\\r\\n\\r\\n- Return the diameter and maxDepth1 (to be used by the parent).\\r\\n\\r\\n### Implementation\\r\\n\\r\\n```\\r\\nclass Solution:\\r\\n    def minimumDiameterAfterMerge(\\r\\n        self, edges1: list[list[int]], edges2: list[list[int]]\\r\\n    ) -> int:\\r\\n        # Calculate the number of nodes for each tree (number of edges + 1)\\r\\n        n = len(edges1) + 1\\r\\n        m = len(edges2) + 1\\r\\n\\r\\n        # Build adjacency lists for both trees\\r\\n        adj_list1 = self.build_adj_list(n, edges1)\\r\\n        adj_list2 = self.build_adj_list(m, edges2)\\r\\n\\r\\n        # Calculate the diameter of both trees\\r\\n        diameter1, _ = self.find_diameter(\\r\\n            adj_list1, 0, -1\\r\\n        )  # Start DFS for Tree 1\\r\\n        diameter2, _ = self.find_diameter(\\r\\n            adj_list2, 0, -1\\r\\n        )  # Start DFS for Tree 2\\r\\n\\r\\n        # Calculate the diameter of the combined tree\\r\\n        # This accounts for the longest path spanning both trees\\r\\n        combined_diameter = ceil(diameter1 / 2) + ceil(diameter2 / 2) + 1\\r\\n\\r\\n        # Return the maximum diameter among the two trees and the combined tree\\r\\n        return max(diameter1, diameter2, combined_diameter)\\r\\n\\r\\n    # Helper function to build an adjacency list from an edge list\\r\\n    def build_adj_list(\\r\\n        self, size: int, edges: list[list[int]]\\r\\n    ) -> list[list[int]]:\\r\\n        adj_list = [[] for _ in range(size)]\\r\\n        for edge in edges:\\r\\n            adj_list[edge[0]].append(edge[1])\\r\\n            adj_list[edge[1]].append(edge[0])\\r\\n        return adj_list\\r\\n\\r\\n    # Helper function to find the diameter of a tree\\r\\n    # Returns the diameter and the depth of the node's subtree\\r\\n    def find_diameter(\\r\\n        self, adj_list: list[list[int]], node: int, parent: int\\r\\n    ) -> tuple[int, int]:\\r\\n        max_depth1 = max_depth2 = (\\r\\n            0  # Tracks the two largest depths from the current node\\r\\n        )\\r\\n        diameter = 0  # Tracks the maximum diameter of the subtree\\r\\n\\r\\n        for neighbor in adj_list[node]:\\r\\n            if neighbor == parent:\\r\\n                continue  # Skip the parent to avoid cycles\\r\\n\\r\\n            # Recursively calculate the diameter and depth of the neighbor's subtree\\r\\n            child_diameter, depth = self.find_diameter(adj_list, neighbor, node)\\r\\n            depth += 1  # Increment depth to include edge to neighbor\\r\\n\\r\\n            # Update the maximum diameter of the subtree\\r\\n            diameter = max(diameter, child_diameter)\\r\\n\\r\\n            # Update the two largest depths from the current node\\r\\n            if depth > max_depth1:\\r\\n                max_depth2 = max_depth1\\r\\n                max_depth1 = depth\\r\\n            elif depth > max_depth2:\\r\\n                max_depth2 = depth\\r\\n\\r\\n        # Update the diameter to include the path through the current node\\r\\n        diameter = max(diameter, max_depth1 + max_depth2)\\r\\n\\r\\n        # Return the diameter and the longest depth\\r\\n        return diameter, max_depth1\\r\\n```\\r\\n\\r\\n### Complexity Analysis\\r\\n\\r\\nLet n be the number of nodes in the first tree and m the number of nodes in the second tree.\\r\\n\\r\\nTime complexity: O(n+m)\\r\\n\\r\\nThe findDiameter function uses Depth-First Search (DFS) on the tree, with a time complexity of O(k), where k is the tree's size. The diameter calculation itself takes O(n+m) time. Since combining the diameters involves only constant-time operations, the overall time complexity is O(n+m).\\r\\n\\r\\nSpace complexity: O(n+m)\\r\\n\\r\\nThe space complexity depends on the size of the data structures and the recursion depth. Using an adjacency list representation of the trees requires O(n+m) space. Additionally, the recursion depth can reach O(k), where k is the number of nodes in the processed tree. Thus, the total space complexity is O(n+m).\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"4d7a7a41-3737-46a1-ac9f-7ba07c579188\",\"title\":\"How to implement a rate limiter in python\",\"description\":\"4 methods to implement a rate limiter in python in plain code\",\"tags\":[],\"original_content\":\"# Implement a rate limiter in python\\r\\n\\r\\n## Overview\\r\\n\\r\\nAPI rate limiting is a technique used to control the number of requests that a client can make to an API within a specified period. It is an essential aspect of API management, ensuring the stability and availability of services by preventing excessive use that could lead to server overloads or abuse.\\r\\n\\r\\n## Common Rate Limiting Strategies\\r\\nToken Bucket Algorithm:\\r\\n— This algorithm allows a fixed number of tokens to be issued at a regular interval.\\r\\n— Each request consumes a token. When tokens are exhausted, requests are denied until more tokens are added.\\r\\n— It allows for a burst of traffic as long as tokens are available.\\r\\n\\r\\nLeaky Bucket Algorithm:\\r\\n— Requests are processed at a constant rate. Excess requests are queued and handled in order.\\r\\n— This approach smooths out traffic spikes but can introduce delays for queued requests.\\r\\n\\r\\nFixed Window:\\r\\n— Limits the number of requests in a fixed time window (e.g., 100 requests per minute).\\r\\n— Simple to implement but can allow bursts at the edge of windows.\\r\\n\\r\\nSliding Window:\\r\\n— Similar to the fixed window but tracks requests in a rolling window to prevent bursts at the edges.\\r\\n— More complex but provides smoother enforcement.\\r\\n\\r\\n## Sample Codes\\r\\n```\\r\\nimport time\\r\\nimport queue\\r\\n\\r\\nclass LeakyBucketLimitter:\\r\\n    def __init__(self) -> None:\\r\\n        # after reaching max_size, calls limit is limit_rate (unit: second)\\r\\n        self.max_size = 1\\r\\n        self.limit_rate = 1\\r\\n        self.cur_size = 0\\r\\n        self.last_time = time.time()\\r\\n\\r\\n    def can_pass(self):\\r\\n        self.cur_size = max(0, self.cur_size - (time.time() - self.last_time) * self.limit_rate)\\r\\n        self.last_time = time.time()\\r\\n        if self.cur_size + 1 <= self.max_size:\\r\\n            self.cur_size += 1\\r\\n            return True\\r\\n        else:\\r\\n            return False\\r\\n        \\r\\n\\r\\nclass TokenBucketLimitter:\\r\\n    def __init__(self) -> None:\\r\\n        # after reaching max_size, calls limit is limit_rate (unit: second)\\r\\n        self.max_size = 1\\r\\n        self.limit_rate = 1\\r\\n        self.cur_size = 0\\r\\n        self.last_time = time.time()\\r\\n\\r\\n    def can_pass(self):\\r\\n        self.cur_size = min(self.max_size, self.cur_size + (time.time() - self.last_time) * self.limit_rate)\\r\\n        self.last_time = time.time()\\r\\n        if self.cur_size > 0:\\r\\n            self.cur_size -= 1\\r\\n            return True\\r\\n        else:\\r\\n            return False\\r\\n        \\r\\n\\r\\nclass FixedWindowLimitter:\\r\\n    def __init__(self) -> None:\\r\\n        # in window_size, calls limit is limit_rate (unit: second)\\r\\n        self.window_size = 1\\r\\n        self.rate = 1\\r\\n        self.counter = 0\\r\\n        self.last_time = time.time()\\r\\n\\r\\n    def can_pass(self):\\r\\n        if time.time() - self.last_time > self.window_size:\\r\\n            self.last_time = time.time()\\r\\n            self.counter = 1\\r\\n        else:\\r\\n            self.counter += 1\\r\\n        return self.counter <= self.rate\\r\\n    \\r\\nclass SlidingWindowLimitter:\\r\\n    def __init__(self) -> None:\\r\\n        # in window_size, calls limit is limit_rate (unit: second)\\r\\n        self.window_size = 1\\r\\n        self.rate = 1\\r\\n        self.q = queue.Queue()\\r\\n\\r\\n    def can_pass(self):\\r\\n        cur_time = time.time()\\r\\n        while not self.q.empty() and (cur_time - self.q.queue[0]) > self.window_size:\\r\\n            self.q.get()\\r\\n        if self.q.qsize() < self.rate:\\r\\n            self.q.put(cur_time)\\r\\n            return True\\r\\n        else:\\r\\n            return False\\r\\n\\r\\nAbove classes has method can_pass which return True if calls rate under limit, otherwise return False if exceed limit\\r\\n```\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"59c8f16f-0438-4a02-9e47-e70797c70426\",\"title\":\"Error : a bytes-like object is required, not 'MultiValue'\",\"description\":\"Error : a bytes-like object is required, not 'MultiValue' Hello,  I am getting following error while updating the tag LongTrianglePointIndexList (0066,0040), **TypeError: a bytes-like object is required, not 'MultiValue'**  I noticed that the error  gets produced only when the VR is given as \\\"OL\\\" , works fine with \\\"OB\\\", \\\"OF\\\" etc.  sample code (assume 'lineSeq' is the dicom dataset sequence): ```python import pydicom import array data=list(range(1,10)) data=array.array('H', indexData).tostring()  # to convert to unsigned short lineSeq.add_new(0x00660040, 'OL', data)    ds.save_as(\\\"mydicom\\\") ``` outcome: **TypeError: a bytes-like object is required, not 'MultiValue'**  using version - *******  Any help is appreciated.  Thank you\",\"tags\":[],\"original_content\":\"## Overview\\r\\nFix TypeError: a bytes-like object is required, not 'MultiValue'\\r\\nThis update addresses a TypeError encountered when updating a DICOM tag with the Value Representation (VR) of \\\"OL\\\" (Other Long). The solution ensures that backslash characters in byte and certain string VRs are properly handled during value assignment in DICOM datasets.\\r\\n\\r\\n## Procedure\\r\\n1. Identify VR Exclusions for Backslash Characters:\\r\\n   - Define a list of VRs where backslash characters should be ignored during value assignment. This includes 'LT', 'OB', 'OD', 'OF', 'OL', 'OV', 'OW', 'ST', 'UN', 'UT', and binary representations like 'OB/OW', 'OW/OB'.\\r\\n\\r\\n2. Update Value Assignment Logic in dataelem.py:\\r\\n   - Modify the value assignment logic to exclude the defined VRs from splitting values on backslashes. This prevents misinterpretation of binary data as multi-value strings for these VR types.\\r\\n\\r\\n3. Modify Value Setter Method:\\r\\n   - In `dataelem.py`, update the `value` setter method to incorporate the VR exclusions list. Ensure that the logic only splits strings on backslash characters for VRs not included in the exclusions.\\r\\n\\r\\nBy following these steps, the error related to byte-like objects during DICOM tag value assignment can be resolved, allowing the dataset to accommodate the \\\"OL\\\" VR properly.\\r\\n\\r\\n## Git Diff Messages\\r\\n```\\r\\ndiff --git a/pydicom/dataelem.py b/pydicom/dataelem.py\\r\\n--- a/pydicom/dataelem.py\\r\\n+++ b/pydicom/dataelem.py\\r\\n@@ -433,13 +433,24 @@ def value(self) -> Any:\\r\\n     @value.setter\\r\\n     def value(self, val: Any) -> None:\\r\\n         \\\"\\\"\\\"Convert (if necessary) and set the value of the element.\\\"\\\"\\\"\\r\\n+        # Ignore backslash characters in these VRs, based on:\\r\\n+        # * Which str VRs can have backslashes in Part 5, Section 6.2\\r\\n+        # * All byte VRs\\r\\n+        exclusions = [\\r\\n+            'LT', 'OB', 'OD', 'OF', 'OL', 'OV', 'OW', 'ST', 'UN', 'UT',\\r\\n+            'OB/OW', 'OW/OB', 'OB or OW', 'OW or OB',\\r\\n+            # Probably not needed\\r\\n+            'AT', 'FD', 'FL', 'SQ', 'SS', 'SL', 'UL',\\r\\n+        ]\\r\\n+\\r\\n         # Check if is a string with multiple values separated by '\\\\'\\r\\n         # If so, turn them into a list of separate strings\\r\\n         #  Last condition covers 'US or SS' etc\\r\\n-        if isinstance(val, (str, bytes)) and self.VR not in \\\\\\r\\n-                ['UT', 'ST', 'LT', 'FL', 'FD', 'AT', 'OB', 'OW', 'OF', 'SL',\\r\\n-                 'SQ', 'SS', 'UL', 'OB/OW', 'OW/OB', 'OB or OW',\\r\\n-                 'OW or OB', 'UN'] and 'US' not in self.VR:\\r\\n+        if (\\r\\n+            isinstance(val, (str, bytes))\\r\\n+            and self.VR not in exclusions\\r\\n+            and 'US' not in self.VR\\r\\n+        ):\\r\\n             try:\\r\\n                 if _backslash_str in val:\\r\\n                     val = cast(str, val).split(_backslash_str)\\r\\n```\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"7282a1d4-9bac-478b-9186-c2bc384a1e8e\",\"title\":\"Implement /car GET endpoint in FastAPI application\",\"description\":\"This update introduces a new GET endpoint '/car' to the FastAPI application, which returns a list of cars read from a JSON file. The corresponding read function is also implemented in the API module, along with a new Pydantic model for type validation.\",\"tags\":[\"python\",\"fastapi\"],\"original_content\":\"## Overview\\r\\nImplement /car GET endpoint in FastAPI application\\r\\nThis update introduces a new GET endpoint '/car' to the FastAPI application, which returns a list of cars read from a JSON file. The corresponding read function is also implemented in the API module, along with a new Pydantic model for type validation.\\r\\n\\r\\n## Procedure\\r\\n1. Add GET endpoint for car data in main.py: Modify 'app/main.py' to include a new route definition for the GET endpoint at '/car'. This endpoint will call the 'read_car' function from the API to fetch the car data.\\r\\n2. Implement read_car function in api.py: In 'app/api/api.py', add the 'read_car' function. This function will open 'data/cars.json', load the data using 'json.load', and return the loaded list of cars.\\r\\n3. Define Car model in models.py: Create a new class in 'app/db/models.py' using Pydantic's BaseModel to define the data structure for each car in the JSON. Include attributes: id, name, fuel, price, category, and link.\\r\\n\\r\\n\\r\\n\\r\\n## Important information\\r\\n\\r\\n### Project Knowledge\\r\\n\\r\\n```json\\r\\n{\\\"language\\\":\\\"javascript\\\",\\\"language_version\\\":\\\"1.0.0\\\",\\\"framework\\\":\\\"react\\\",\\\"framework_version\\\":\\\"1.0.0\\\",\\\"packages_install_command\\\":\\\"npm install\\\",\\\"run_command\\\":\\\"npm run dev\\\",\\\"middlewares\\\":[\\\"redis\\\"]}\\r\\n```\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n### Git Diff Messages\\r\\n\\r\\n```\\r\\n\\r\\ndiff --git a/app/api/api.py b/app/api/api.py\\r\\nindex b0d7c36..af12301 100644\\r\\n--- a/app/api/api.py\\r\\n+++ b/app/api/api.py\\r\\n@@ -7,6 +7,11 @@ def read_user():\\r\\n\\r\\n     return users\\r\\n\\r\\n+def read_car():\\r\\n+    with open('data/cars.json') as stream:\\r\\n+        cars = json.load(stream)\\r\\n+\\r\\n+    return cars\\r\\n\\r\\n def read_questions(position: int):\\r\\n     with open('data/questions.json') as stream:\\r\\ndiff --git a/app/main.py b/app/main.py\\r\\nindex 09ca0ff..2c9dff5 100644\\r\\n--- a/app/main.py\\r\\n+++ b/app/main.py\\r\\n@@ -16,6 +16,9 @@ def root():\\r\\n def read_user():\\r\\n     return api.read_user()\\r\\n\\r\\<EMAIL>(\\\"/car\\\")\\r\\n+def read_car():\\r\\n+    return api.read_car()\\r\\n\\r\\n @app.get(\\\"/question/{position}\\\", status_code=200)\\r\\n def read_questions(position: int, response: Response):\\r\\n\\r\\n```\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"8bbad832-c30d-497f-8c3c-a2815c19c110\",\"title\":\"Add read_cars API to FastAPI application\",\"description\":\"Implement a new /car GET endpoint to return all car data and update car data model as necessary.\",\"tags\":[\"FastAPI\",\"Python\"],\"original_content\":\"\\n\\n## Overview\\nAdd read_cars API to FastAPI application\\nImplement a new /car GET endpoint to return all car data and update car data model as necessary.\\n\\n## Procedure\\n1. Add GET endpoint for car data: Implement a new route in main.py that handles GET requests to '/car' and routes them to a handler function that calls 'api.read_cars()'.\\n2. Implement read_cars function: Create a new function 'read_cars()' in api.py that reads data from 'data/cars.json' using 'json.load' and returns the loaded car data.\\n3. Define Car model: Update models.py to create a new Pydantic BaseModel class 'Car' that matches the structure of the JSON data in 'cars.json' with the appropriate attributes.\\n\\n\\n## Project Basic Info\\nLanguage: typescript \\nFramework: nextjs 14.0.1\\nMiddlewares: ['mongodb']\\n\\n\\n\\n# File Tree\\nBelow are all files of the project, read files already exist:, excluding hidden files, excluding ignored files:\\n```\\n.1024\\n.1024nix\\n.env.example\\n.eslintrc.json\\n.gitignore\\nREADME.md\\napp/api/chat/route.ts\\napp/favicon.ico\\napp/globals.css\\napp/hooks/useConfiguration.ts\\napp/layout.tsx\\napp/page.tsx\\ncomponents/Bubble.tsx\\ncomponents/Configure.tsx\\ncomponents/Dropdown.tsx\\ncomponents/Footer.tsx\\ncomponents/PromptSuggestions/PromptSuggestionButton.tsx\\ncomponents/PromptSuggestions/PromptSuggestionsRow.tsx\\ncomponents/ThemeButton.tsx\\ncomponents/Toggle.tsx\\nnext-env.d.ts\\nnext.config.js\\npackage-lock.json\\npackage.json\\npostcss.config.js\\nscripts/populateDb.ts\\nscripts/sample_data.json\\ntailwind.config.js\\ntsconfig.json\\n```\\n\\n\\n\",\"source\":\"AI\",\"status\":\"pending\"}\n", "{\"id\":\"b2abfc69-12ad-4e0d-a96d-2a308e05e2c2\",\"title\":\"Find Building Where <PERSON> and <PERSON> Can Meet\",\"description\":\"Tutorial: Find Building Where <PERSON> and <PERSON> Can Meet\",\"tags\":[],\"original_content\":\"# Solution: Find Building Where <PERSON> and <PERSON> Can Meet\\r\\n\\r\\n## Solution\\r\\n\\r\\n## Overview\\r\\nWe are given an integer array heights and an array of pairs queries, where each pair is of the form [a \\r\\ni,b i], representing the positions of <PERSON> and <PERSON> at indices i and j, respectively. For each query, the task is to find the closest value to the right in the heights array that is greater than the heights at both the given positions.\\r\\n\\r\\nIn other words, given indices i and j, we need to find the first value in the heights array that is greater than the values at heights[i] and heights[j]. If no such value exists, return -1.\\r\\n\\r\\n## Approach 1: Monotonic Stack\\r\\n\\r\\n### Intuition\\r\\nLet’s start by breaking down the problem into simpler terms. Suppose queries only contained single integer indices. The goal would then be to find, for each index, the first building to the right in the heights array that is taller than the building at that index. Instead of scanning the array repeatedly for each query, we can preprocess the heights array to store this \\\"next taller building\\\" information in advance.\\r\\n\\r\\nThe key insight here is that for each building, the next taller building to its right depends only on the heights of the buildings that come after it. Using a monotonic stack, we can compute this efficiently. By traversing the heights array from right to left, we maintain a stack of indices in decreasing order of heights. For the current building, any shorter or equal buildings already in the stack cannot be the answer, so we remove them. If the stack is not empty, the top element gives the position of the next taller building. If the stack is empty, it means no taller building exists to the right, so we store -1. This preprocessing step allows us to handle single queries in constant time. For a better understanding of this idea, you can refer to Next Greater Element - II, which applies a similar technique.\\r\\n\\r\\nNow, let’s extend this idea to handle queries that are pairs of values. In this scenario, the task is to find the first height to the right in the heights array that is greater than both values in each pair. Here the key realization is that the answer for a pair depends on the larger of the two values since a building must be taller than both. This simplifies the problem by reducing it to a comparison with a single threshold for each query.\\r\\n\\r\\nWhile traversing the heights array, we use a monotonic stack to maintain all elements greater than the current height, with the nearest greater height at the top of the stack. When processing a query, the stack already contains all elements greater than the current height.\\r\\n\\r\\nFor each query pair, we use binary search on the stack to quickly find the first element greater than the larger value in the pair. This ensures that each query is processed in O(logn) time.\\r\\n\\r\\n### Algorithm\\r\\n\\r\\nMain function - leftmostBuildingQueries(heights, queries)\\r\\n\\r\\n1 Create a list newQueries where each index stores the list of queries that require this index as the maximum index of the query pair. Each query is stored as a pair containing the required height (heights[a]) and the query index.\\r\\n2 Initialize a monotonic stack monoStack to keep track of building heights and their indices in decreasing order of height while iterating from right to left in the heights array.\\r\\n3 Initialize an array result to store the answers for each query, with all elements initially set to -1.\\r\\n4 Iterate over the queries:\\r\\n- For each query, extract the two indices a and b.\\r\\n- If a > b, swap the indices to ensure a <= b.\\r\\n- If heights[b] > heights[a] or a == b, set result[currQuery] = b.\\r\\n- Otherwise, add the query to newQueries[b] with its required height (heights[a]) and the query index.\\r\\n5 Iterate over the indices of the heights array from right to left:\\r\\n- For each query stored at the current index in newQueries, use binary search on the monoStack to find the first building with a height greater than the query's required height. If such a building exists, set the result for the query to the index of this building.\\r\\n- Remove all elements from the top of the monoStack where the height is less than or equal to the current height, as they are no longer relevant.\\r\\n- Push the current height and index onto the monoStack.\\r\\n6 Return the result array.\\r\\n\\r\\nHelper Binary Search function - search(height, monoStack)\\r\\n\\r\\n1 Initialize two pointers left = 0 and right = size of monoStack - 1. Set a variable ans = -1 to store the search result.\\r\\n2 Perform a binary search:\\r\\n- Calculate mid = (left + right) / 2.\\r\\n- If the height at monoStack[mid] is greater than the required height:\\r\\n\\t- Update ans = max(ans, mid) and set left = mid + 1.\\r\\n3 Otherwise, set right = mid - 1.\\r\\nReturn ans, which will be the index of the first building with a height greater than the required height. If no such building exists, ans remains -1.\\r\\n\\r\\n### Implementation\\r\\n\\r\\n```\\r\\nclass Solution:\\r\\n    def leftmostBuildingQueries(self, heights, queries):\\r\\n        mono_stack = []\\r\\n        result = [-1 for _ in range(len(queries))]\\r\\n        new_queries = [[] for _ in range(len(heights))]\\r\\n        for i in range(len(queries)):\\r\\n            a = queries[i][0]\\r\\n            b = queries[i][1]\\r\\n            if a > b:\\r\\n                a, b = b, a\\r\\n            if heights[b] > heights[a] or a == b:\\r\\n                result[i] = b\\r\\n            else:\\r\\n                new_queries[b].append((heights[a], i))\\r\\n\\r\\n        for i in range(len(heights) - 1, -1, -1):\\r\\n            mono_stack_size = len(mono_stack)\\r\\n            for a, b in new_queries[i]:\\r\\n                position = self.search(a, mono_stack)\\r\\n                if position < mono_stack_size and position >= 0:\\r\\n                    result[b] = mono_stack[position][1]\\r\\n            while mono_stack and mono_stack[-1][0] <= heights[i]:\\r\\n                mono_stack.pop()\\r\\n            mono_stack.append((heights[i], i))\\r\\n        return result\\r\\n\\r\\n    def search(self, height, mono_stack):\\r\\n        left = 0\\r\\n        right = len(mono_stack) - 1\\r\\n        ans = -1\\r\\n        while left <= right:\\r\\n            mid = (left + right) // 2\\r\\n            if mono_stack[mid][0] > height:\\r\\n                ans = max(ans, mid)\\r\\n                left = mid + 1\\r\\n            else:\\r\\n                right = mid - 1\\r\\n        return ans\\r\\n```\\r\\n\\r\\n### Complexity Analysis\\r\\n\\r\\nLet n be the size of the array heights and q be the number of queries in the queries array.\\r\\n\\r\\nTime Complexity: O(q⋅logn+n)\\r\\n\\r\\nThe algorithm processes each query using binary search on the monotonic stack, which takes O(logn) per query. With q queries, the total query processing time is O(q⋅logn). Apart from this, we also iterate through the heights and queries arrays, that takes O(n) and O(q) time, respectively.\\r\\nTherefore, the overall time complexity is O(q⋅logn+n).\\r\\n\\r\\nSpace Complexity: O(n+q)\\r\\n\\r\\nThe algorithm uses a monotonic stack to store building indices, requiring O(n) space. It also stores queries in the newQueries array and results in the result array, each taking O(q) space.\\r\\nTherefore, the total space complexity is O(n+q).\\r\\n\\r\\n\\r\\n## Approach 2: Priority Queue\\r\\n\\r\\n### Intuition\\r\\n\\r\\nIn the previous approach, we calculated the answer using a monotonic stack. Each query asks for the closest index to the right with a value greater than both elements in the query pair. Instead of processing each query one at a time, we can optimize by checking, for each index in the heights array, if it can serve as the answer for any query.\\r\\n\\r\\nTo do this efficiently, we can iterate through the heights array from left to right. For each index, we look for query pairs where both indices are smaller than the current index, and both values in the pair are smaller than the value at the current index. To make this process faster, we prioritize assigning answers to the smallest query pairs first.\\r\\n\\r\\nBy maintaining the query pairs sorted based on their maximum value and index up to the current position, we can process them more efficiently.\\r\\n\\r\\nTo implement this idea, we process the heights array while managing the queries by storing them in a 2D array of arrays, where each subarray holds the queries for the corresponding building.\\r\\n\\r\\nWe begin by sorting and mapping the queries to track the index and values that we need. Using a priority queue, we store queries based on their maximum value and index. This helps us quickly retrieve the smallest index for processing.\\r\\n\\r\\nAs we move through the heights array, we pop the queries from the queue. For each query, if the current index is greater than both indices of the query, we assign the current index as the answer and store it. We also check if new queries, whose maximum index matches the current one, should be added to the queue for future processing.\\r\\n\\r\\nThis allows us to handle queries without reprocessing them repeatedly.\\r\\n\\r\\n### Algorithm\\r\\n\\r\\n- Initialize storeQueries as a 2D array of arrays to store queries for each building.\\r\\n\\r\\n- Initialize maxIndex as a priority queue to track the queries that need to be answered based on building heights.\\r\\n\\r\\n- Initialize result as an array of -1 to store the answers for each query.\\r\\n\\r\\n- Loop through each query:\\r\\n\\r\\n\\t- For each query (a, b):\\r\\n\\t\\t- If the height of building a is less than building b and a is smaller than b, set result[currQuery] to b (building b is the answer).\\r\\n\\t\\t- If the height of building a is greater than building b and a is greater than b, set result[currQuery] to a (building a is the answer).\\r\\n\\t\\t- If a is equal to b, set result[currQuery] to a (both are the same building).\\r\\n\\t\\t- Otherwise, store the query in storeQueries[max(a, b)] for future processing.\\r\\n- Loop through each building index index:\\r\\n\\r\\n\\t- While the priority queue maxIndex has elements and the minimum value in maxIndex is smaller than the current building height:\\r\\n\\t\\t- Set the corresponding query's result in result and pop the element from maxIndex (this query is answered).\\r\\n\\t- Push new queries from storeQueries[index] into maxIndex, sorting them by height.\\r\\n- Return the result array containing the answers to all queries.\\r\\n\\r\\n### Implementation\\r\\n\\r\\n```\\r\\nclass Solution:\\r\\n    def leftmostBuildingQueries(self, heights, queries):\\r\\n        max_idx = []  # Min-heap to simulate priority queue\\r\\n        results = [-1] * len(queries)\\r\\n        store_queries = [[] for _ in heights]\\r\\n\\r\\n        # Store the mappings for all queries in store_queries.\\r\\n        for idx, query in enumerate(queries):\\r\\n            a, b = query\\r\\n            if a < b and heights[a] < heights[b]:\\r\\n                results[idx] = b\\r\\n            elif a > b and heights[a] > heights[b]:\\r\\n                results[idx] = a\\r\\n            elif a == b:\\r\\n                results[idx] = a\\r\\n            else:\\r\\n                store_queries[max(a, b)].append(\\r\\n                    (max(heights[a], heights[b]), idx)\\r\\n                )\\r\\n\\r\\n        for idx, height in enumerate(heights):\\r\\n            # If the heap's smallest value is less than the current height, it is an answer to the query.\\r\\n            while max_idx and max_idx[0][0] < height:\\r\\n                _, q_idx = heapq.heappop(max_idx)\\r\\n                results[q_idx] = idx\\r\\n            # Push the queries with their maximum index as the current index into the heap.\\r\\n            for element in store_queries[idx]:\\r\\n                heapq.heappush(max_idx, element)\\r\\n\\r\\n        return results\\r\\n```\\r\\n\\r\\n### Complexity Analysis\\r\\n\\r\\nLet n be the size of the array heights and q be the number of queries in the queries array.\\r\\n\\r\\nTime Complexity: O(q⋅logq+n)\\r\\n\\r\\nThe algorithm first iterates over the queries array to map the maximum indices and heights in storeQueries, taking O(q) time. It then processes each index in the heights array, updating results via a priority queue. Insertion and deletion operations in the priority queue take O(logq) each, with at most q queries processed. For each index, the algorithm checks and pushes relevant queries from storeQueries, resulting in an overall O(n) time for all iterations.\\r\\n\\r\\nThus, the overall time complexity is O(q⋅logq+n).\\r\\n\\r\\nSpace Complexity: O(n+q)\\r\\n\\r\\nThe algorithm uses a array storeQueries to store query mappings, which requires O(n) space, as each element corresponds to an index in heights. Additionally, a priority queue maxIndex is used to handle queries, which at most can store O(q) elements. The result array also requires O(q) space to store the answers.\\r\\n\\r\\nTherefore, the total space complexity is O(n+q).\",\"source\":\"Human\",\"status\":\"ready\"}\n", "{\"id\":\"c0efeb37-c1b8-4729-93ac-4ff07912aefe\",\"title\":\"test\",\"description\":\"test\",\"tags\":[\"test\"],\"original_content\":\"test\",\"source\":\"Human\",\"status\":\"pending\"}\n", "{\"id\":\"cd3870d8-a3d4-4e61-beb6-60878e06f3fb\",\"title\":\"The function generate_uid() generates non-conforming “2.25 .” DICOM UIDs\",\"description\":\"The function generate_uid() generates non-conforming “2.25 .” DICOM UIDs <!-- Instructions For Filing a Bug: https://github.com/pydicom/pydicom/blob/master/CONTRIBUTING.md#filing-bugs -->  #### Description It seems there was already a discussion about this function in the past (#125), but the current implementation generates non-conforming DICOM UIDs when called with prefix ‘none’ to trigger that the function generate_uid() should generate a UUID derived UID.  The DICOM Standard requires (see DICOM PS 3.5, B.2 that when a UUID derived UID is constructed it should be in the format “2.25.” + uuid(in its decimal representation string representation) For example a UUID of f81d4fae-7dec-11d0-a765-00a0c91e6bf6 should become 2.25.329800735698586629295641978511506172918  The current implementation extends the uuid part to the remaining 59 characters. By not following the DICOM formatting rule, receiving systems that are processing DICOM instances created with this library are not capable of converting the generated “2.25” UID back to a UUID. Due to the extra sha512 operation on the UUID, the variant and version info of the UUID are also lost.  #### Steps/Code to Reproduce - call generate_uid() to generate a \\\"2.25.\\\" DICOM UID  #### Expected Results A conforming unique DICOM UID is returned.  #### Actual Results Non conforming UID is returned.\",\"tags\":[],\"original_content\":\"## Overview\\r\\nThis update resolves the issue of generating non-conforming DICOM UIDs in `pydicom` when using the `generate_uid()` function with a `None` prefix. The update ensures compliance with the DICOM Standard by using the UUID4 algorithm to generate a correctly formatted \\\"2.25\\\" UID, which conforms to the \\\"2.25.\\\" + integer form of UUID as required by the standard. \\r\\n\\r\\n## Procedure\\r\\n1. **Update `generate_uid` function signature:** \\r\\n   - Adjust the documentation in `pydicom/uid.py` to clarify that when `prefix` is `None`, a UUID4-derived integer will be used with a \\\"2.25.\\\" prefix.\\r\\n\\r\\n2. **Implement UUID4-based UID generation:**\\r\\n   - Modify the `generate_uid` function in `pydicom/uid.py` to return a UID by formatting it as \\\"2.25.\\\" followed by the integer representation of a newly generated UUID v4 using `uuid4().int`.\\r\\n\\r\\n3. **Remove incorrect UID length handling:**\\r\\n   - Remove any logic that incorrectly adjusts the length of the UUID-derived UIDs to the remaining 59 characters, ensuring compliance with the standard.\\r\\n\\r\\n4. **Retain the existing logic for non-None prefixes:**\\r\\n   - Preserve the SHA512-based UID generation process for cases where the `prefix` is not `None`. This ensures deterministic UID generation for custom prefixes.\\r\\n\\r\\n5. **Update tests and documentation:**\\r\\n   - Verify that updated examples in the documentation reflect the correct usage of `generate_uid()` with the expected results.\\r\\n   - Ensure any relevant tests check for compliance with DICOM's UID formatting rules.\\r\\n\\r\\n## Git Diff Messages\\r\\n```\\r\\ndiff --git a/pydicom/uid.py b/pydicom/uid.py\\r\\n--- a/pydicom/uid.py\\r\\n+++ b/pydicom/uid.py\\r\\n@@ -250,19 +250,19 @@ def generate_uid(prefix=PYDICOM_ROOT_UID, entropy_srcs=None):\\r\\n     ----------\\r\\n     prefix : str or None\\r\\n         The UID prefix to use when creating the UID. Default is the pydicom\\r\\n-        root UID '1.2.826.0.1.3680043.8.498.'. If None then a value of '2.25.'\\r\\n-        will be used (as described on `David Clunie's website\\r\\n+        root UID '1.2.826.0.1.3680043.8.498.'. If None then a prefix of '2.25.'\\r\\n+        will be used with the integer form of a UUID generated using the\\r\\n+        UUID4 algorithm.\\r\\n     entropy_srcs : list of str or None\\r\\n-        If a list of str, the prefix will be appended with a SHA512 hash of the\\r\\n-        list which means the result is deterministic and should make the\\r\\n-        original data unrecoverable. If None random data will be used\\r\\n-        (default).\\r\\n+        If `prefix` is not None, then the prefix will be appended with a\\r\\n+        SHA512 hash of the list which means the result is deterministic and\\r\\n+        should make the original data unrecoverable. If None random data will\\r\\n+        be used (default).\\r\\n \\r\\n     Returns\\r\\n     -------\\r\\n     pydicom.uid.UID\\r\\n-        A 64 character DICOM UID.\\r\\n+        A DICOM UID of up to 64 characters.\\r\\n \\r\\n     Raises\\r\\n     ------\\r\\n@@ -275,17 +275,17 @@ def generate_uid(prefix=PYDICOM_ROOT_UID, entropy_srcs=None):\\r\\n     >>> generate_uid()\\r\\n     1.2.826.0.1.3680043.8.498.22463838056059845879389038257786771680\\r\\n     >>> generate_uid(prefix=None)\\r\\n-    2.25.12586835699909622925962004639368649121731805922235633382942\\r\\n+    2.25.167161297070865690102504091919570542144\\r\\n     >>> generate_uid(entropy_srcs=['lorem', 'ipsum'])\\r\\n     1.2.826.0.1.3680043.8.498.87507166259346337659265156363895084463\\r\\n     >>> generate_uid(entropy_srcs=['lorem', 'ipsum'])\\r\\n     1.2.826.0.1.3680043.8.498.87507166259346337659265156363895084463\\r\\n     \\\"\\\"\\\"\\r\\n-    max_uid_len = 64\\r\\n-\\r\\n     if prefix is None:\\r\\n-        prefix = '2.25.'\\r\\n+        # UUID -> as 128-bit int -> max 39 characters long\\r\\n+        return UID('2.25.{}'.format(uuid.uuid4().int))\\r\\n \\r\\n+    max_uid_len = 64\\r\\n     if len(prefix) > max_uid_len - 1:\\r\\n         raise ValueError(\\\"The prefix must be less than 63 chars\\\")\\r\\n     if not re.match(RE_VALID_UID_PREFIX, prefix):\\r\\n```\",\"source\":\"Human\",\"status\":\"ready\"}\n"]}], "source": ["for playbook in playbooks:\n", "    print(playbook.model_dump_json())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "os.path.exists('.sfas')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["['rate_limiter_python.json',\n", " 'programming_problem_2.json',\n", " 'fastapi_example.json',\n", " 'swe_1413.json',\n", " 'swe_965.json',\n", " 'programming_problem_1.json',\n", " 'swe_800.json']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["playbook_samples_path = 'heracles/server/admin/playbook_samples'\n", "os.listdir(playbook_samples_path)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from glob import glob"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["import litellm\n", "from qdrant_client import models\n", "from heracles.core.schema import AdminPlaybookModel, AdminPlaybookStatus\n", "from heracles.server.admin.config import get_settings, get_sparse_model, get_vector_db_client\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["# vector_db_client = get_vector_db_client()\n"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/qdrant_remote.py:130: UserWarning: Api key is used with an insecure connection.\n", "  warnings.warn(\"Api key is used with an insecure connection.\")\n"]}], "source": ["from qdrant_client import QdrantClient\n", "# vector_db_client = QdrantClient(url='http://************', port=30633, api_key='sk-hV23KnK2T5', timeout=60)\n", "vector_db_client = QdrantClient(url='http://*************', port=30633, api_key='sk-3eL0jjsMWx', timeout=60)\n", "\n"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ[\"http_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"https_proxy\"] = \"http://127.0.0.1:1080\"\n", "os.environ[\"no_proxy\"] = \"127.0.0.1,localhost\""]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"ename": "ResponseHandlingException", "evalue": "timed out", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mReadTimeout\u001b[0m                               Trace<PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_transports/default.py:72\u001b[0m, in \u001b[0;36mmap_httpcore_exceptions\u001b[0;34m()\u001b[0m\n\u001b[1;32m     71\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 72\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m     73\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_transports/default.py:236\u001b[0m, in \u001b[0;36mHTTPTransport.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    235\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[0;32m--> 236\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    238\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mIterable)\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:216\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    215\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_connections(closing)\n\u001b[0;32m--> 216\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    218\u001b[0m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[1;32m    219\u001b[0m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:196\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    194\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    195\u001b[0m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[0;32m--> 196\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    197\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\n\u001b[1;32m    198\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    199\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[1;32m    200\u001b[0m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[1;32m    201\u001b[0m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[1;32m    202\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    203\u001b[0m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/http_proxy.py:207\u001b[0m, in \u001b[0;36mForwardHTTPConnection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    200\u001b[0m proxy_request \u001b[38;5;241m=\u001b[39m Request(\n\u001b[1;32m    201\u001b[0m     method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    202\u001b[0m     url\u001b[38;5;241m=\u001b[39murl,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    205\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    206\u001b[0m )\n\u001b[0;32m--> 207\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_connection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mproxy_request\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/connection.py:101\u001b[0m, in \u001b[0;36mHTTPConnection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m     99\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[0;32m--> 101\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_connection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/http11.py:143\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    142\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response_closed()\n\u001b[0;32m--> 143\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/http11.py:113\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[1;32m    105\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreceive_response_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request, kwargs\n\u001b[1;32m    106\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[1;32m    107\u001b[0m     (\n\u001b[1;32m    108\u001b[0m         http_version,\n\u001b[1;32m    109\u001b[0m         status,\n\u001b[1;32m    110\u001b[0m         reason_phrase,\n\u001b[1;32m    111\u001b[0m         headers,\n\u001b[1;32m    112\u001b[0m         trailing_data,\n\u001b[0;32m--> 113\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_receive_response_headers\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    114\u001b[0m     trace\u001b[38;5;241m.\u001b[39mreturn_value \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    115\u001b[0m         http_version,\n\u001b[1;32m    116\u001b[0m         status,\n\u001b[1;32m    117\u001b[0m         reason_phrase,\n\u001b[1;32m    118\u001b[0m         headers,\n\u001b[1;32m    119\u001b[0m     )\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/http11.py:186\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_response_headers\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 186\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_receive_event\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    187\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11\u001b[38;5;241m.\u001b[39mResponse):\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_sync/http11.py:224\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_event\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    223\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11\u001b[38;5;241m.\u001b[39mNEED_DATA:\n\u001b[0;32m--> 224\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_network_stream\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    225\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mREAD_NUM_BYTES\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[1;32m    226\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    228\u001b[0m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[1;32m    229\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    230\u001b[0m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    234\u001b[0m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_backends/sync.py:124\u001b[0m, in \u001b[0;36mSyncStream.read\u001b[0;34m(self, max_bytes, timeout)\u001b[0m\n\u001b[1;32m    123\u001b[0m exc_map: ExceptionMapping \u001b[38;5;241m=\u001b[39m {socket\u001b[38;5;241m.\u001b[39mtimeout: ReadTimeout, \u001b[38;5;167;01mOSError\u001b[39;00m: ReadError}\n\u001b[0;32m--> 124\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmap_exceptions\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexc_map\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    125\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msettimeout\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/contextlib.py:158\u001b[0m, in \u001b[0;36m_GeneratorContextManager.__exit__\u001b[0;34m(self, typ, value, traceback)\u001b[0m\n\u001b[1;32m    157\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 158\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgen\u001b[38;5;241m.\u001b[39mthrow(typ, value, traceback)\n\u001b[1;32m    159\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    160\u001b[0m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[1;32m    161\u001b[0m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[1;32m    162\u001b[0m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpcore/_exceptions.py:14\u001b[0m, in \u001b[0;36mmap_exceptions\u001b[0;34m(map)\u001b[0m\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(exc, from_exc):\n\u001b[0;32m---> 14\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m to_exc(exc) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mexc\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[0;31mReadTimeout\u001b[0m: timed out", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mReadTimeout\u001b[0m                               Trace<PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api_client.py:106\u001b[0m, in \u001b[0;36mApiClient.send_inner\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 106\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    107\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_client.py:926\u001b[0m, in \u001b[0;36mClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m    924\u001b[0m auth \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_build_request_auth(request, auth)\n\u001b[0;32m--> 926\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    927\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    928\u001b[0m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    929\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    930\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    931\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    932\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_client.py:954\u001b[0m, in \u001b[0;36mClient._send_handling_auth\u001b[0;34m(self, request, auth, follow_redirects, history)\u001b[0m\n\u001b[1;32m    953\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 954\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    955\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    956\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    957\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    958\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    959\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_client.py:991\u001b[0m, in \u001b[0;36mClient._send_handling_redirects\u001b[0;34m(self, request, follow_redirects, history)\u001b[0m\n\u001b[1;32m    989\u001b[0m     hook(request)\n\u001b[0;32m--> 991\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    992\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_client.py:1027\u001b[0m, in \u001b[0;36mClient._send_single_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m   1026\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39mrequest):\n\u001b[0;32m-> 1027\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mtransport\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1029\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, SyncByteStream)\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_transports/default.py:235\u001b[0m, in \u001b[0;36mHTTPTransport.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    223\u001b[0m req \u001b[38;5;241m=\u001b[39m httpcore\u001b[38;5;241m.\u001b[39mRequest(\n\u001b[1;32m    224\u001b[0m     method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    225\u001b[0m     url\u001b[38;5;241m=\u001b[39mhttpcore\u001b[38;5;241m.\u001b[39mURL(\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    233\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    234\u001b[0m )\n\u001b[0;32m--> 235\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmap_httpcore_exceptions\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    236\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresp\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/contextlib.py:158\u001b[0m, in \u001b[0;36m_GeneratorContextManager.__exit__\u001b[0;34m(self, typ, value, traceback)\u001b[0m\n\u001b[1;32m    157\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 158\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgen\u001b[38;5;241m.\u001b[39mthrow(typ, value, traceback)\n\u001b[1;32m    159\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    160\u001b[0m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[1;32m    161\u001b[0m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[1;32m    162\u001b[0m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/httpx/_transports/default.py:89\u001b[0m, in \u001b[0;36mmap_httpcore_exceptions\u001b[0;34m()\u001b[0m\n\u001b[1;32m     88\u001b[0m message \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(exc)\n\u001b[0;32m---> 89\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m mapped_exc(message) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mexc\u001b[39;00m\n", "\u001b[0;31mReadTimeout\u001b[0m: timed out", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mResponseHandlingException\u001b[0m                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[108], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mvector_db_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscroll\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcollection_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mplaybooks\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscroll_filter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFilter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmust\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmodels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mFieldCondition\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mstatus\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmatch\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mMatchValue\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mpending\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m        \u001b[49m\u001b[43m]\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlimit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1000\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwith_payload\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/qdrant_client.py:1365\u001b[0m, in \u001b[0;36mQdrantClient.scroll\u001b[0;34m(self, collection_name, scroll_filter, limit, order_by, offset, with_payload, with_vectors, consistency, shard_key_selector, timeout, **kwargs)\u001b[0m\n\u001b[1;32m   1323\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Scroll over all (matching) points in the collection.\u001b[39;00m\n\u001b[1;32m   1324\u001b[0m \n\u001b[1;32m   1325\u001b[0m \u001b[38;5;124;03mThis method provides a way to iterate over all stored points with some optional filtering condition.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1361\u001b[0m \u001b[38;5;124;03m    If next page offset is `None` - there is no more points in the collection to scroll.\u001b[39;00m\n\u001b[1;32m   1362\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1363\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(kwargs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnknown arguments: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlist\u001b[39m(kwargs\u001b[38;5;241m.\u001b[39mkeys())\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1365\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscroll\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1366\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcollection_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcollection_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1367\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscroll_filter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscroll_filter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1368\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlimit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlimit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1369\u001b[0m \u001b[43m    \u001b[49m\u001b[43morder_by\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morder_by\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1370\u001b[0m \u001b[43m    \u001b[49m\u001b[43moffset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffset\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1371\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwith_payload\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwith_payload\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1372\u001b[0m \u001b[43m    \u001b[49m\u001b[43mwith_vectors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwith_vectors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1373\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconsistency\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconsistency\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1374\u001b[0m \u001b[43m    \u001b[49m\u001b[43mshard_key_selector\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mshard_key_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1375\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1376\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1377\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/qdrant_remote.py:1745\u001b[0m, in \u001b[0;36mQdrantRemote.scroll\u001b[0;34m(self, collection_name, scroll_filter, limit, order_by, offset, with_payload, with_vectors, consistency, shard_key_selector, timeout, **kwargs)\u001b[0m\n\u001b[1;32m   1741\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(with_payload, grpc\u001b[38;5;241m.\u001b[39mWithPayloadSelector):\n\u001b[1;32m   1742\u001b[0m     with_payload \u001b[38;5;241m=\u001b[39m GrpcToRest\u001b[38;5;241m.\u001b[39mconvert_with_payload_selector(with_payload)\n\u001b[1;32m   1744\u001b[0m scroll_result: Optional[models\u001b[38;5;241m.\u001b[39mScrollResult] \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m-> 1745\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopenapi_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpoints_api\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscroll_points\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1746\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcollection_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcollection_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1747\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconsistency\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconsistency\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1748\u001b[0m \u001b[43m        \u001b[49m\u001b[43mscroll_request\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodels\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mScrollRequest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1749\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;28;43mfilter\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscroll_filter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1750\u001b[0m \u001b[43m            \u001b[49m\u001b[43mlimit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlimit\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1751\u001b[0m \u001b[43m            \u001b[49m\u001b[43morder_by\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morder_by\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1752\u001b[0m \u001b[43m            \u001b[49m\u001b[43moffset\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moffset\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1753\u001b[0m \u001b[43m            \u001b[49m\u001b[43mwith_payload\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwith_payload\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1754\u001b[0m \u001b[43m            \u001b[49m\u001b[43mwith_vector\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mwith_vectors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1755\u001b[0m \u001b[43m            \u001b[49m\u001b[43mshard_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mshard_key_selector\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1756\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1757\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1758\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mresult\n\u001b[1;32m   1759\u001b[0m )\n\u001b[1;32m   1760\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m scroll_result \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mScroll points API returned None result\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1762\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m scroll_result\u001b[38;5;241m.\u001b[39mpoints, scroll_result\u001b[38;5;241m.\u001b[39mnext_page_offset\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api/points_api.py:1735\u001b[0m, in \u001b[0;36mSyncPointsApi.scroll_points\u001b[0;34m(self, collection_name, consistency, timeout, scroll_request)\u001b[0m\n\u001b[1;32m   1725\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mscroll_points\u001b[39m(\n\u001b[1;32m   1726\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1727\u001b[0m     collection_name: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1730\u001b[0m     scroll_request: m\u001b[38;5;241m.\u001b[39mScrollRequest \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1731\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m m\u001b[38;5;241m.\u001b[39mInlineResponse20015:\n\u001b[1;32m   1732\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1733\u001b[0m \u001b[38;5;124;03m    Scroll request - paginate over all points which matches given filtering condition\u001b[39;00m\n\u001b[1;32m   1734\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1735\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_build_for_scroll_points\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1736\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcollection_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcollection_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1737\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconsistency\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconsistency\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1738\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1739\u001b[0m \u001b[43m        \u001b[49m\u001b[43mscroll_request\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscroll_request\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1740\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api/points_api.py:682\u001b[0m, in \u001b[0;36m_PointsApi._build_for_scroll_points\u001b[0;34m(self, collection_name, consistency, timeout, scroll_request)\u001b[0m\n\u001b[1;32m    680\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mContent-Type\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m headers:\n\u001b[1;32m    681\u001b[0m     headers[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mContent-Type\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mapplication/json\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 682\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapi_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    683\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtype_\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mm\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mInlineResponse20015\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    684\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mPOST\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    685\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/collections/\u001b[39;49m\u001b[38;5;132;43;01m{collection_name}\u001b[39;49;00m\u001b[38;5;124;43m/points/scroll\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    686\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    687\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpath_params\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpath_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    688\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparams\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquery_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    689\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcontent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    690\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api_client.py:79\u001b[0m, in \u001b[0;36mApiClient.request\u001b[0;34m(self, type_, method, url, path_params, **kwargs)\u001b[0m\n\u001b[1;32m     77\u001b[0m     kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparams\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m     78\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39mbuild_request(method, url, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m---> 79\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtype_\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api_client.py:96\u001b[0m, in \u001b[0;36mApiClient.send\u001b[0;34m(self, request, type_)\u001b[0m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msend\u001b[39m(\u001b[38;5;28mself\u001b[39m, request: Request, type_: Type[T]) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m---> 96\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmiddleware\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend_inner\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     97\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;241m200\u001b[39m, \u001b[38;5;241m201\u001b[39m, \u001b[38;5;241m202\u001b[39m]:\n\u001b[1;32m     98\u001b[0m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api_client.py:205\u001b[0m, in \u001b[0;36mBaseMiddleware.__call__\u001b[0;34m(self, request, call_next)\u001b[0m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, request: Request, call_next: Send) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Response:\n\u001b[0;32m--> 205\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mcall_next\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/exp/lib/python3.11/site-packages/qdrant_client/http/api_client.py:108\u001b[0m, in \u001b[0;36mApiClient.send_inner\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    106\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39msend(request)\n\u001b[1;32m    107\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 108\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ResponseHandlingException(e)\n\u001b[1;32m    109\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[0;31mResponseHandlingException\u001b[0m: timed out"]}], "source": ["res = vector_db_client.scroll(\n", "    collection_name='playbooks',\n", "    scroll_filter=models.Filter(\n", "        must=[\n", "            models.FieldCondition(key='status', match=models.MatchValue(value='pending')),\n", "        ]\n", "    ),\n", "    limit=1000,\n", "    with_payload=True,\n", ")"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [], "source": ["ids = []\n", "for p in res[0]:\n", "    ids.append(p.id)\n"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"data": {"text/plain": ["['43d7b4f8-95e6-4db2-abf7-1f000a0deb83',\n", " '44457c1e-9880-4606-8e06-1f40167fa5a5',\n", " '9ab279ef-ea70-42f8-a77b-ff19097a55a5']"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["ids"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Record(id='43d7b4f8-95e6-4db2-abf7-1f000a0deb83', payload={'title': 'test', 'description': 'test', 'tags': [], 'original_content': 'test', 'source': 'user', 'status': 'pending'}, vector={}, shard_key=None, order_value=None)]"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_db_client.retrieve(\n", "    collection_name=\"playbooks\",\n", "    ids=['43d7b4f8-95e6-4db2-abf7-1f000a0deb83'],\n", "    with_vectors=True\n", ")\n"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/plain": ["UpdateResult(operation_id=874, status=<UpdateStatus.COMPLETED: 'completed'>)"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_db_client.delete('playbooks', points_selector=ids)"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm_notebook as tqdm"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["update\n", "644e3f5d-a075-4eae-91f8-fa262b5beaba\n", "update\n", "cc796cfb-3892-4613-9773-e82b3bc4e2d2\n", "update\n", "None\n"]}], "source": ["offset = None\n", "start = True\n", "while start or offset:\n", "    if start:\n", "        start = False\n", "    points, next_id = vector_db_client.scroll(\n", "        collection_name='playbooks',\n", "        limit=1000,\n", "        with_payload=True,\n", "        offset=offset\n", "    )\n", "    user_points = []\n", "    # ai_points = []\n", "    for res in points:\n", "        # user_points.append(res.id)\n", "        if res.payload['source'] == \"Human\":\n", "            user_points.append(res.id)\n", "    #     elif res.payload['source'] == \"AI\":\n", "    #         ai_points.append(res.id)\n", "    if user_points:\n", "        print(\"update\")\n", "        vector_db_client.set_payload('playbooks', payload={'source': 'user'}, points=user_points)\n", "    # if ai_points:\n", "    #     vector_db_client.set_payload('playbooks', payload={'source': 'ai'}, points=user_points)\n", "\n", "    offset = next_id\n", "    print(offset)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["res = vector_db_client.scroll(\n", "        collection_name='playbooks',\n", "        limit=1000,\n", "        with_payload=True,\n", "    )"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["UpdateResult(operation_id=397, status=<UpdateStatus.COMPLETED: 'completed'>)"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_db_client.set_payload('playbooks', payload={'source': 'user'}, points=['000261b9-fb67-41f1-9d34-ab6e02038611'])"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Record(id='000261b9-fb67-41f1-9d34-ab6e02038611', payload={'title': 'python~3.13 c-api none', 'original_content': ' \\n\\nThe None Object\\n===============\\n\\nNote that the [`PyTypeObject`](type#c.PyTypeObject \"PyTypeObject\") for `None` is not directly exposed in the Python/C API. Since `None` is a singleton, testing for object identity (using `==` in C) is sufficient. There is no `PyNone_Check()` function for the same reason.\\n\\n \\n`PyObject *Py_None`  \\n\\nThe Python `None` object, denoting lack of value. This object has no methods and is [immortal](../glossary#term-immortal).\\n\\nChanged in version 3.12: [`Py_None`](#c.Py_None \"Py_None\") is [immortal](../glossary#term-immortal).\\n\\n \\n  \\n`Py_RETURN_NONE`  \\n\\nReturn [`Py_None`](#c.Py_None \"Py_None\") from a function.\\n\\n\\n \\n\\n© 2001–2024 Python Software Foundation  \\nLicensed under the PSF License.  \\n\\n<https://docs.python.org/3.13/c-api/none.html>\\n\\n\\n', 'tags': ['python~3.13', 'devdocs'], 'source': 'user', 'status': 'ready'}, vector=None, shard_key=None, order_value=None)]"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_db_client.retrieve('playbooks', ids=['000261b9-fb67-41f1-9d34-ab6e02038611'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "exp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}