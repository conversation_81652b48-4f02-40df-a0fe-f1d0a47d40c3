{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["20:27:30 heracles:INFO:logger.py:74 Heracles is starting, current log_level is INFO\n", "20:27:39 heracles:INFO:llm.py:31 langfuse is enabled, load config\n", "20:27:39 heracles:INFO:redis_cache.py:44 [cache] feature is disabled\n"]}], "source": ["from heracles.agent_workspace.paas_sdk.utils import fork_codezone, delete_codezone, get_playground_id, fetch, get_environments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'data': [{'id': '403710021818515464',\n", "   'envCode': 'code_go_1_23',\n", "   'name': 'Go 1.23',\n", "   'runtime': 'go1.23 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.23', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515463',\n", "   'envCode': 'code_go_1_22',\n", "   'name': 'Go 1.22',\n", "   'runtime': 'go1.22 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.22', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515462',\n", "   'envCode': 'code_go_1_21',\n", "   'name': 'Go 1.21',\n", "   'runtime': 'go1.21 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.21', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515461',\n", "   'envCode': 'code_go_1_20',\n", "   'name': 'Go 1.20',\n", "   'runtime': 'go1.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.20', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515459',\n", "   'envCode': 'code_go_1_20_gin',\n", "   'name': 'Gin / Go 1.20',\n", "   'runtime': 'go1.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.20', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Framework']},\n", "  {'id': '403710021818515458',\n", "   'envCode': 'code_go_1_19',\n", "   'name': 'Go 1.19',\n", "   'runtime': 'go1.19 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.19', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515457',\n", "   'envCode': 'code_go_1_18',\n", "   'name': 'Go 1.18',\n", "   'runtime': 'go1.18 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.18', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515456',\n", "   'envCode': 'code_go_1_17',\n", "   'name': 'Go 1.17',\n", "   'runtime': 'go1.17 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['go 1.17', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021818515460',\n", "   'envCode': 'code_go_gvm_1_0_22',\n", "   'name': 'Go Basic Environment ( gvm 1.0.22 )',\n", "   'runtime': 'Managed by  gvm 1.0.22 with go1.22 actived, and go1.17, go1.18, go1.19, go1.20, go1.21, go1.23, node v20.1.0  already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gvm 1.0.22', 'npm 9.6.4'],\n", "   'language': 'Go',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150291',\n", "   'envCode': 'code_ruby_3_3_5',\n", "   'name': '<PERSON> 3.3.5',\n", "   'runtime': 'ruby 3.3.5 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.5.16', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['solargraph 0.51.0',\n", "    'bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150281',\n", "   'envCode': 'code_ruby_3_2_6',\n", "   'name': '<PERSON> 3.2.6',\n", "   'runtime': 'ruby 3.2.6 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.4.19', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['solargraph 0.51.0',\n", "    'bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150272',\n", "   'envCode': 'code_ruby_3_1_2',\n", "   'name': '<PERSON> 3.1.2',\n", "   'runtime': 'ruby 3.1.2 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.3.20', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['solargraph 0.51.0',\n", "    'bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150270',\n", "   'envCode': 'code_ruby_3_0_5',\n", "   'name': '<PERSON> 3.0.5',\n", "   'runtime': 'ruby 3.0.5 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.3.20', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['solargraph 0.51.0',\n", "    'bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150271',\n", "   'envCode': 'code_ruby_3_0_1',\n", "   'name': '<PERSON> 3.0.1',\n", "   'runtime': 'ruby 3.0.1 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.2.15', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['solargraph 0.51.0',\n", "    'bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150274',\n", "   'envCode': 'code_ruby_2_7_7',\n", "   'name': '<PERSON> 2.7.7',\n", "   'runtime': 'ruby 2.7.7 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['gem 3.3.20', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710021940150275',\n", "   'envCode': 'code_ruby_rbenv_1_3_0',\n", "   'name': 'Ruby Basic Environment ( rbenv 1.3.0 )',\n", "   'runtime': 'Managed by  rbenv 1.3.0 with  ruby 2.7.8 actived, and ruby 2.7.7, ruby 3.0.1, ruby 3.0.5, ruby 3.1.2, ruby 3.2.6, ruby 3.3.5,node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['rbenv 1.3.0', 'npm 9.6.4'],\n", "   'language': '<PERSON>',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036355',\n", "   'envCode': 'code_python_3_12_8',\n", "   'name': 'Python 3.12.8',\n", "   'runtime': 'python 3.12.8 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 24.3.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036354',\n", "   'envCode': 'code_python_3_11_11',\n", "   'name': 'Python 3.11.11',\n", "   'runtime': 'python 3.11.11 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 24.0', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036353',\n", "   'envCode': 'code_python_3_10_16',\n", "   'name': 'Python 3.10.16',\n", "   'runtime': 'python 3.10.16 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 23.0.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036352',\n", "   'envCode': 'code_python_3_9_21',\n", "   'name': 'Python 3.9.21',\n", "   'runtime': 'python 3.9.21 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 23.0.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036121',\n", "   'envCode': 'code_python_3_8_20',\n", "   'name': 'Python 3.8.20',\n", "   'runtime': 'python 3.8.20 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 23.0.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036116',\n", "   'envCode': 'code_python_3_7_17',\n", "   'name': 'Python 3.7.17',\n", "   'runtime': 'python 3.7.17 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 23.0.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036115',\n", "   'envCode': 'code_python_3_6_15',\n", "   'name': 'Python 3.6.15',\n", "   'runtime': 'python 3.6.15 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 18.1', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036110',\n", "   'envCode': 'code_python_2_7_18',\n", "   'name': 'Python 2.7.18',\n", "   'runtime': 'python 2.7.18 and node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pip 19.2.3', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710022024036361',\n", "   'envCode': 'code_python_pyenv_2_5',\n", "   'name': 'Python Basic Environment ( pyenv 2.5 )',\n", "   'runtime': 'Managed by pyenv 2.5 with python 2.7.18 actived, and python 3.6.15, python 3.7.17, python 3.8.20, python 3.9.21, python 3.10.16, python 3.11.11, python 3.12.8, node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['pyenv 2.5', 'npm 9.6.4'],\n", "   'language': 'Python',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '684533895370272782',\n", "   'envCode': 'code_node_22_1_3',\n", "   'name': 'Node.js 22.1.0',\n", "   'runtime': 'node v22.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['npm 10.7.0'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '684533895370272783',\n", "   'envCode': 'code_node_20_1_0',\n", "   'name': 'Node.js 20.1.0',\n", "   'runtime': 'node v20.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['npm 9.6.4'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '684533895370272770',\n", "   'envCode': 'code_node_18_14_1',\n", "   'name': 'Node.js 18.14.1',\n", "   'runtime': 'node v18.14.1 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['npm 10.7.0'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '684533895370272780',\n", "   'envCode': 'code_node_16_19_1',\n", "   'name': 'Node.js 16.19.1',\n", "   'runtime': 'node v16.19.1 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['npm 8.19.3'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '684533895370272781',\n", "   'envCode': 'code_node_14_21_3',\n", "   'name': 'Node.js 14.21.3',\n", "   'runtime': 'node v14.21.3 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['npm 6.14.18'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710023303299089',\n", "   'envCode': 'code_node_nvm_0_41_1',\n", "   'name': 'Node.js Basic Environment ( nvm 0.41.1 )',\n", "   'runtime': 'Managed by nvm 0.41.1 with node v20.1.0 actived, and node v14.21.3, node v16.19.1, node v18.14.1, node v20.1.0, node v22.1.0 already installed, based on Ubuntu 22.04.4',\n", "   'packageManagers': ['nvm 0.41.1', 'npm 9.6.4'],\n", "   'language': 'Node.js',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']},\n", "  {'id': '403710023458488320',\n", "   'envCode': 'code_html_css_js',\n", "   'name': 'HTML/CSS/JS',\n", "   'runtime': 'browser-sync 2.27.10 and node v20.1.0 already installed,  based on Ubuntu 22.04.4',\n", "   'packageManagers': ['nvm 0.41.1', 'npm 9.6.4'],\n", "   'language': 'HTML/CSS/JS',\n", "   'runtimeInformation': ['bash 5.1',\n", "    'git 2.34.1',\n", "    'mysql-client-8.0 8.0.40',\n", "    'mongodb-mongosh 2.3.8',\n", "    'postgresql-client-14 14.15',\n", "    'redis-tools 6.0.16',\n", "    'sqlite3 3.37.2'],\n", "   'tags': ['Pure Language']}],\n", " 'error': None}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["await get_environments()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'data': [{'id': '674735781877088256',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '674738647387832320',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '685670567776600064',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '685891705400180736',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '686031639419273216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '690319751829999616',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '690323829364576256',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '690331474121150464',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '690597301559209984',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '690631585007382528',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '690699364557369344',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '691059535397662720',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '693594297324408832',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '693595671193849856',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694650111954927616',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694652229608685568',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694660012387221504',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694662748390440960',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694663644042117120',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '694664879390474240',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '695034975216652288',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '695055511963734016',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '695075351134900224',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '695330867627069440',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '695395448911204352',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '697504302721654784',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697506124412739584',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697508517170896896',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697520178040299520',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697520373142544384',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697520489207324672',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697550400198434816',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697551512850817024',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697552432623935488',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697553605137424384',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697562284591046656',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697563066111516672',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697567347455713280',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697620469846794240',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697621799374053376',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697622907609833472',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697628784861548544',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697631063568191488',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697857591216345088',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697864297182826496',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697865948165738496',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697867238576918528',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697870298774671360',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '697873119460888576',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '697885794597244928',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698300180944117760',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '698314020779872256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '698314523123290112',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698318506223112192',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698319441817145344',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698329176587014144',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698345723745439744',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698593243117555712',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698635506719821824',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698650796383965184',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698663317421592576',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698663980004184064',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698664171461578752',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698664784706572288',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '698665840912011264',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698667728558526464',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698668060604796928',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698668223012442112',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698668727394275328',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '698721843976245248',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '699738639785168896',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '699744581847257088',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '699791893755748352',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '699792281129082880',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '699792658087960576',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700108941333520384',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '700143081906921472',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700155226170220544',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700399994259267584',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700501328690044928',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700753751367114752',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '700754475224293376',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '702220629885214720',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702220646276554752',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702228623398166528',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702231373473275904',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702233174595493888',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702233726268104704',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702234342763683840',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702235285987155968',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702276864449863680',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702590152136089600',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702634670696849408',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702645238489047040',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702670276718182400',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702670390891331584',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702670708752465920',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '702670934678650880',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702671210307338240',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702672901098389504',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702679360871809024',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702705895095877632',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '702708823974166528',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '702950284481216512',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '702992964263239680',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '703056060939919360',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '703315537249390592',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '703399661007953920',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '703400378473013248',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '703400494999166976',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '703428648446939136',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '703785073363005440',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '703786172031578112',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '703786446972399616',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '703787077304987648',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704726397436178432',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704733050176942080',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704733213524111360',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704733964677820416',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704734268060217344',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704743025368510464',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704753228512112640',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704753322246418432',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704756609364406272',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704757827943936000',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704758321986809856',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704769094402322432',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704769733924630528',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704769887247413248',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704769955551653888',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704770024141107200',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704776385537126400',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704795843848507392',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '704796385924755456',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704796506355806208',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704797060901515264',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704798987991265280',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704799893637967872',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704800221850644480',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704801119523975168',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704802947103862784',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704803032583778304',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704803135797211136',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704803271583608832',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '704803417801240576',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '704810302331621376',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '704840337889570816',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705119550316097536',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705120386006003712',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705120880170512384',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705130483335704576',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705187592987152384',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705208651887136768',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705213513655898112',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705216343787687936',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705219104965275648',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705227623366012928',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705228191077642240',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705235686479245312',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705239756493262848',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705240115194335232',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705240398616039424',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705241700033073152',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705244667964526592',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705253009143336960',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705253819856162816',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705260132845719552',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705265838210650112',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705269312570757120',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705443662519275520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705449021971566592',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705450224331722752',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705451922420862976',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705453788575764480',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705455159374974976',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705456859276054528',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705458654043901952',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705466292026675200',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705467829767606272',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705470388645367808',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705470736680325120',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705471390584901632',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705474118870601728',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705474381933154304',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705477174312013824',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705478614887665664',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705482645454426112',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705483057054060544',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705484150823686144',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705492778632843264',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705494751474061312',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705519275502440448',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705519959916384256',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705526970485805056',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705534935091535872',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705538229490835456',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705544188225282048',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705549339887558656',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705560238144774144',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705566442413776896',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705567099959009280',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705569400752857088',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705572664621817856',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705578690515460096',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705579018707165184',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705589222064676864',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705589514759987200',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705602528418619392',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705603945866878976',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705605741440094208',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705606047934664704',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705607473448570880',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705611642884542464',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705618721380896768',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705826385104867328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705830058040102912',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705831684129820672',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705832188415184896',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705840513244598272',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705856066277494784',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705857332181356544',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705857399470575616',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705857658082971648',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705864690160783360',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705889130307268608',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705891414344871936',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705896274016481280',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705899958167285760',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705900134013480960',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705900185980907520',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705901176608403456',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '705902820310003712',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705903369084350464',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705903845074939904',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705918457161854976',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705919210454659072',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705919247817519104',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705919400066560000',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705921312950202368',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705923441588842496',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705930050390077440',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705934456405766144',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705941143518851072',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705949629971574784',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705952911569858560',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705961874482933760',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705964015482298368',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705964334274568192',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705964571667980288',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705965754419765248',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '705966357338382336',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '705986560457695232',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706058484038754304',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706192408995946496',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706194611110133760',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706195278000275456',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706212435408961536',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706213865846980608',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706218629414502400',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706219790901166080',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706238112292126720',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706246055951581184',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706250893741506560',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706254316616531968',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706255124380762112',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706255291523776512',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706255341842841600',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706265422059704320',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706268804031877120',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706269307444826112',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706269470557114368',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706275283761795072',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706284204090101760',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706288386973011968',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706290419486593024',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706292912178237440',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706293333793869824',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706296925934227456',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706314611145740288',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706385992713670656',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706386054311219200',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706401817302568960',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706401855336517632',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706402036379455488',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706542216205733888',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706543280338407424',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706543393832079360',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706543492012347392',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706550779275808768',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706554684625817600',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706555032362979328',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706557899668885504',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706563201919258624',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706563591066783744',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706563592157302784',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706563592182468608',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706564235781640192',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706564260486090752',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706564665299341312',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706569451448995840',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706581393630781440',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706582242687934464',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706582632670126080',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706595953808990208',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706608462821548032',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706610714265214976',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706610911393308672',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706611023230230528',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706611135771795456',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706611241111740416',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706614136313962496',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706614958363992064',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706617381467938816',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706618226553085952',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706623054192795648',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706623211793768448',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706623293725302784',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706623477293211648',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706623617953390592',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706623715261243392',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706624154564255744',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706625079135657984',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706631264484155392',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706633126960652288',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706643236185399296',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706644024211234816',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706644102707634176',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706646550021103616',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706647057557053440',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706648054887047168',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706649490681192448',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706649536201973760',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706651049867567104',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706651794130034688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706652222381056000',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706653003536621568',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '706655793839300608',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706656386347655168',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706656465515143168',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706657306296934400',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706658066149629952',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706658220118335488',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706659661142777856',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706660854309019648',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706661132047441920',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '706661253287993344',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706661282358714368',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706661559304413184',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706661729492492288',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706666223508873216',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706667526624600064',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706667757332291584',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706669926773121024',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706670095417696256',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706671022757339136',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706671023243878400',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '706671047780556800',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '707760205202677760',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '707996344702668800',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '707996794667601920',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '707997621222313984',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708005371830538240',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708007022645678080',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708010117282861056',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708010313475624960',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708027658889793536',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708076627791646720',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708079420615102464',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708080495845904384',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708084698374934528',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708143298619314176',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708143737213489152',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708162241358663680',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708163730235920384',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708357678044745728',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708362451275943936',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708363704928243712',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708367746651852800',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708369447660228608',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708373162794356736',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708375140253503488',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708375891356880896',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708382060410458112',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '708382847014424576',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708383759023886336',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708388104486408192',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708430012621410304',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708431241221132288',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708450744298524672',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708491705338519552',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708492520862212096',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708495762253897728',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708501748280680448',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708505260062367744',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708508752504119296',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708555869067108352',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708719949983735808',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708724104202772480',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708724166093922304',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708724233441861632',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708729802680655872',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708748188974276608',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708757390799101952',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708781554985558016',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708786857705693184',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708787212032106496',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708790912221954048',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708797406699044864',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708799379666092032',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708803373436223488',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708811726153564160',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708818481667358720',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708826735239741440',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708832884362035200',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708839905605959680',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708840956472049664',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708841068380274688',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708841398723657728',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708842736903766016',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708843609558073344',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708844378806009856',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '708850633243082752',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708851522087403520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708906164980793344',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708906200116477952',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '708906234195197952',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709289293247660032',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709802814746116096',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709836719012429824',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709839754606395392',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709868375064690688',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709871309638844416',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709872458743922688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709883723327455232',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709885981817552896',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709887535278702592',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709888718357643264',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709891196725739520',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709891428540727296',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709894832956620800',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709895604591116288',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709897526521872384',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709904243846529024',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709905502716542976',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709910830501814272',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '709912036062224384',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709920611820068864',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709937501414055936',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709946670338760704',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709955663396298752',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709957142563741696',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '709973963882848256',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710161017019260928',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710170438084550656',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710170500810366976',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710184245511020544',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710185440677945344',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710188510585511936',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710190440279924736',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710248962304729088',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710275282472493056',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710284162531287040',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710287231298002944',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710294728100503552',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710296803265961984',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710306329373401088',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710347970129911808',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710530568319885312',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710543112384241664',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710546261635457024',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710546547917676544',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710547910659629056',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710549286550724608',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710563793478041600',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710564282160594944',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710598388273594368',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710607647895928832',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710607925323972608',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710608244460175360',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710611434194812928',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710612140469469184',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710612658507956224',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710612979342852096',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710617781028208640',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710620214496280576',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710628005122359296',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710633078799941632',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710637611114512384',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710638079026872320',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710642176283066368',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710643131200258048',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710644397452247040',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710646697050718208',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710647183623536640',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710647255144808448',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710652448515244032',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710652564382892032',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710652773896765440',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710654039028875264',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710654458228588544',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710658696354676736',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710673664554778624',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710674853329584128',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710677191217856512',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710678826346299392',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710678880180191232',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710679316341669888',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710679964651044864',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710689803431264256',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710892255334055936',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710923128305565696',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710925935888777216',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710956551187415040',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710969024426762240',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '710969736292425728',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710997465259794432',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '710998898147287040',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711000698480975872',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711004836774240256',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711005655099727872',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711007212377694208',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711008909086908416',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711010121232375808',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711010514305769472',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711026148506820608',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711028889547399168',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711030011712462848',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711031475688136704',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711032488612552704',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711032590752243712',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711032792208859136',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711033723856052224',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711049432027848704',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711056622428053504',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711121116206424064',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711126405374763008',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711127564508422144',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711129088601055232',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711139647094480896',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711139741646675968',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711141885976207360',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711256747414872064',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711266361913073664',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711266926600609792',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711267056254935040',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711268984225488896',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711271263460614144',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711272665918431232',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711279417615409152',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711280000850157568',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711280925803241472',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711285455785779200',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711289675951857664',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711290789178859520',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711319263650209792',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711320563922198528',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711324087418576896',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711324977349222400',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711330582361235456',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711338591841902592',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711341019731550208',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711343111611310080',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711344854445285376',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711348128753868800',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711352083961569280',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711355387001135104',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711356580976877568',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711359456507813888',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711363371852849152',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711364730115284992',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711365204851777536',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711367231707242496',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711369793802035200',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711371269857304576',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711374979756941312',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711378025387876352',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711391858953248768',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711393480609587200',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711397637806579712',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711417862153265152',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711775547033870336',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711976412248236032',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '711991147324727296',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '711991317697355776',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712001347905609728',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712001470295400448',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712002732164341760',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712005511691853824',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712007102910128128',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712008818044919808',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712042707249487872',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712047879849730048',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712052558683922432',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712059840008200192',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712059844286390272',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712078325983649792',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712083043397050368',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712084186688823296',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712115752907481088',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712116181586321408',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712117232817315840',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712124134242557952',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712135932047958016',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712342044089163776',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712343356591742976',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712354496377954304',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712356473610608640',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712356640938172416',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712364650670497792',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712373217641594880',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712412168301543424',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712420260187701248',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '712431468139741184',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712436060969730048',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712450682284113920',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '712464724029571072',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '714175748780539904',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '714179315759267840',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '714180573429710848',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '714181323748753408',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '714182656136527872',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715234094203748352',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715258198118055936',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715271980793937920',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715274917591072768',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715275576142938112',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715275890447302656',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715276724891500544',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '715277671277477888',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715278718599380992',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715279800796921856',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715304679072022528',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715332253240213504',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715357704385937408',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715371293872177152',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715379886772142080',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715381438190665728',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715383282073812992',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715385488500015104',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715386019037528064',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715600897597718528',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715601756243050496',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715602524220112896',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715614899648282624',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715632670595563520',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715633315780182016',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715637580582043648',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715640844996263936',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715642112020004864',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715672366885322752',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715677422133133312',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715678256426336256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715681908251639808',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715687623687094272',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '715688765112401920',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715753552471040000',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715755604865622016',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '715998697216974848',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '715999774150664192',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716001320741543936',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716003727382847488',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716004385423007744',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716030227222011904',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716042354682929152',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716095333171634176',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716095991064993792',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716101637160214528',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716107012680884224',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716110217397575680',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716110977069916160',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716113649567178752',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716122133100666880',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716326328458190848',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716343374805901312',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716367922553069568',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716395945968967680',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716397128691392512',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716401042815885312',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716402051512442880',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716403164882051072',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716403882493911040',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716406518458785792',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716408541203832832',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716409207200587776',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716428648613322752',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716441848742322176',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716450688699375616',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716453842518188032',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716466539242627072',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716466539297153024',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466539343290368',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716466621618757632',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716466636584034304',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466638110760960',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466646528729088',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466698894614528',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466729731137536',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716466755752599552',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466765584048128',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466833082982400',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466843564548096',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466868835229696',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716466904331624448',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716467970498535424',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468029986349056',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468442689085440',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468478814625792',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716468545080434688',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716468621165109248',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716468680271241216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716468736693018624',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468774173319168',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468809355141120',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468857795158016',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468902183477248',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716468950854180864',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469066231095296',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469242748379136',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469342472151040',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469464375402496',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469577588056064',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469707108163584',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469834199769088',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716469907109355520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716470000659111936',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716470093286121472',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470142015545344',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470310047752192',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470344071946240',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470678110511104',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470737581547520',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470775397392384',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470815562047488',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470866766110720',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470904732950528',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716470923238219776',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716471112967561216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716471226771611648',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716471343201296384',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716471612614025216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716471923114156032',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716472300429549568',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716472458588364800',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716472577605935104',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716472624519225344',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716472658778300416',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716472703472803840',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716472727929790464',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716478491125784576',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716479707054514176',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716479761987313664',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716479874860228608',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716479909823946752',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716479964723191808',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716480038173843456',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716480110978572288',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716480170608992256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480206185078784',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480249042477056',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480289735614464',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480427308785664',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480480421257216',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480546619957248',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480652740042752',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480739092373504',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716480874232848384',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716481090092703744',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716481188788871168',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716481331248406528',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716481507056852992',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716482393330065408',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716482997884461056',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483041266147328',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483168005431296',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483197344587776',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483501867835392',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483558474162176',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483590967435264',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483627797618688',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483664002850816',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483700287774720',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716483736493006848',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716484009479282688',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716484421506736128',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716484421607399424',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716484808116707328',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716484844321939456',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716485194869284864',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716485231741411328',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716485581219209216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716485617646739456',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716485652979556352',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716485689050570752',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716485967875317760',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716488084912513024',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716685746026749952',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716694117614264320',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716694504165515264',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716694942990376960',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716695327117320192',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716695690696368128',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716696723254321152',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716697060505722880',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716697395731275776',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716697744697368576',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716697854617493504',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716698286400118784',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716698447243288576',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716698942271823872',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716699391435644928',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716699572809932800',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716700623164317696',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716701242310696960',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716701715595960320',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716702167293140992',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716703103788998656',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716703173691269120',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716703726748000256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716704836804399104',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716704849940959232',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716704981461749760',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716705208361013248',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716705690286542848',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716706009301110784',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716706370904641536',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716706756407316480',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716706961160654848',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716707385271898112',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716707592017530880',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716708315350421504',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716708941622923264',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716709479953453056',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716709716210208768',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716709930002272256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716710305828687872',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716711273697890304',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716711373832704000',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716711653441785856',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716711953049309184',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716713377871155200',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716714180157624320',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716714471921799168',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716714863225196544',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716715195464404992',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716715701515571200',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716716180559613952',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716716555240984576',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716716733750562816',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716717066644082688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716717571273379840',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716718006927347712',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716720063625285632',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716721780811091968',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716722560293130240',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716722909359886336',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716724107223748608',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716724501333135360',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716724759953920000',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716726883681030144',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716727512373645312',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716757605145890816',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716758783795646464',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716760985100603392',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716761920262623232',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716765620410503168',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716766450614259712',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716766900411420672',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716766996087689216',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716767568916369408',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716767846965170176',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716768713877475328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716768713994915841',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716769101653463040',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716769487508459520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716769518479200256',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716769874038738944',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '716769874038738945',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716770260564824064',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716770260569018368',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716770260581601280',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '716770291925635072',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716770647392899072',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771034556518400',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716771034556518401',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771065674059776',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771420029833216',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771420159856640',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716771806488809472',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771806555918337',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716771837761540096',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772193052643328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772579012497408',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772579754889216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716772579889106944',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772580006547456',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772966406803456',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716772998140907520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716773352865779712',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716773383836520448',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716773739131817984',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716773739513499648',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774125926338560',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774129344696320',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716774157463310336',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774512418869248',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774544035532800',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774899033034752',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716774929836003328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716775285454262272',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716775671942598657',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716775703265660928',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776058510626816',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776449071292416',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776449071292417',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776449075486720',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776449075486721',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716776933580513280',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716777353853968384',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716777361244332032',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716777612147597312',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716777632766795776',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716777702182526976',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716777820147326976',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '716777867538767872',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716778356821106688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716778487377207296',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716778675923755008',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716778838830522368',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716778888071651328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779068380585984',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779169928880128',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779402003914752',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779438368530432',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779468462661632',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779825293074432',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716779937733976064',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716780060559974400',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716780313447145472',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716780429843275776',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716780651654848512',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716781208679391232',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716781659042783232',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716781888756424704',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716781998450057216',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716782083456016384',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716782520854814720',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716783010531418112',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716783087287181312',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716783484638765056',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716784472749678592',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716784713720832000',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716784934160867328',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716785210326425600',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716786798524801024',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716787479008681984',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716788753439563776',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716790357660180480',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716795006962278400',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716795145730826240',\n", "   'environmentVerId': '403710023458488320',\n", "   'environmentName': 'HTML/CSS/JS',\n", "   'startCmd': None},\n", "  {'id': '716798873523363840',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716800913523777536',\n", "   'environmentVerId': '403710021743017984',\n", "   'environmentName': 'Java',\n", "   'startCmd': None},\n", "  {'id': '716802285665165312',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716802608588824576',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716803096805810176',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716803594472562688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716804058895261696',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716804906828341248',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716805771291172864',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716805771387641856',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716806319440568320',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716807033894117376',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716807033894117377',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716807583482159104',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716808170852491264',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716808720620888064',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716809165930143744',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716809341046530048',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716809782710935552',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716809948767625216',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716810439308255232',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716810939747442688',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716811387699109888',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716811832563769344',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716812382973894656',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716812948751949824',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716813424742539264',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716813922367348736',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716814023936614400',\n", "   'environmentVerId': '403710021818515458',\n", "   'environmentName': 'Go ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716814269588611072',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716814756480196608',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716815155610165248',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716815560519884800',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716815777038245888',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716816394024599552',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716816621624311808',\n", "   'environmentVerId': '684533895370272770',\n", "   'environmentName': 'Node.js ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716816834745286656',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716817399231496192',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716817841445355520',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716818283004903424',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  {'id': '716818726678380544',\n", "   'environmentVerId': '403710022024036353',\n", "   'environmentName': 'Python ( bash )',\n", "   'startCmd': None},\n", "  ...],\n", " 'error': None}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import aiohttp\n", "async with aiohttp.ClientSession() as session:\n", "    res = await fetch(\n", "        session,\n", "        '/api/v1/sdk/codeZones',\n", "        method='GET'\n", "    )\n", "res"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "preserved_codezones = []\n", "with open('llm_tests/datasets/swe/codezones.json') as f:\n", "    data = json.load(f)\n", "    for k, v in data.items():\n", "        preserved_codezones.append(v['staging']['codezone_id'])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["301"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(preserved_codezones)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e4899df1649f4a9d8670516874fa0ddd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/8180 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for d in tqdm(res['data']):\n", "    if d['environmentVerId'] == '403710022024036354' and (d['id'] not in preserved_codezones):\n", "        await delete_codezone(d['id'])\n", "        # print(d['id'])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True, 'data': {'status': 'success'}, 'error': None}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["await delete_codezone('762844232569569280')\n", "# await get_playground_id('762833176354865152')"]}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}