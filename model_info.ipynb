{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "from functools import partial\n", "from typing import Union, overload, Optional, Any\n", "\n", "import litellm\n", "from litellm.utils import trim_messages\n", "import traceback\n", "\n", "from heracles.core.config import get_env_var\n", "from heracles.core.exceptions import AgentLLMCallParamsException, AgentLLMConfigException, AgentLLMCallResultException\n", "from heracles.core.logger import heracles_logger as logger\n", "from heracles.core.schema import (\n", "    LangfuseTraceOption,\n", "    LLMAbilityType,\n", "    SubBaseModelOrIterable,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from heracles.core.utils.merge_dict import merge_dict\n", "import aiohttp\n", "from urllib.parse import urljoin"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["api_key = get_env_var('LLM_API_KEY')\n", "base_url = get_env_var('LLM_BASE_URL')\n", "headers = {'Ocp-Apim-Subscription-Key': api_key}\n", "model_info = {}\n", "async with aiohttp.ClientSession() as session:\n", "    async with session.get(urljoin(base_url, '/model/info'), headers=headers) as response:\n", "        model_info = await response.json()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'data': [{'model_name': 'text-embedding-3-small',\n", "   'litellm_params': {'api_base': 'https://clacky.openai.azure.com/',\n", "    'api_version': '2023-05-15',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'azure/text-embedding-3-small'},\n", "   'model_info': {'id': 'f07b2b3045d7138c3e1418eae7131f675533ba964239168b15296378b8f48df1',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'mode': 'embedding',\n", "    'key': 'azure/text-embedding-3-small',\n", "    'max_tokens': 8191,\n", "    'max_input_tokens': 8191,\n", "    'max_output_tokens': None,\n", "    'input_cost_per_token': 2e-08,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 0.0,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'azure',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': <PERSON><PERSON><PERSON>,\n", "    'supports_tool_choice': <PERSON><PERSON><PERSON>,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['temperature',\n", "     'n',\n", "     'stream',\n", "     'stream_options',\n", "     'stop',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'tools',\n", "     'tool_choice',\n", "     'presence_penalty',\n", "     'frequency_penalty',\n", "     'logit_bias',\n", "     'user',\n", "     'function_call',\n", "     'functions',\n", "     'tools',\n", "     'tool_choice',\n", "     'top_p',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'response_format',\n", "     'seed',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'prediction',\n", "     'modalities',\n", "     'audio']}},\n", "  {'model_name': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:1',\n", "   'litellm_params': {'aws_region_name': 'us-west-2',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    'thinking': {'type': 'enabled', 'budget_tokens': 4096}},\n", "   'model_info': {'id': 'f3175849b5069a4d12e27291aa127aa4f2f2ed17b641268f1b631efe433233bd',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'supports_vision': True,\n", "    'key': 'anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 200000,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 3e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 1.5e-05,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'bedrock_converse',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': True,\n", "    'supports_prompt_caching': True,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'stream_options',\n", "     'stop',\n", "     'temperature',\n", "     'top_p',\n", "     'extra_headers',\n", "     'response_format',\n", "     'tools',\n", "     'thinking',\n", "     'reasoning_effort']}},\n", "  {'model_name': 'gemini-2.5-flash-preview',\n", "   'litellm_params': {'vertex_project': 'clacky-456208',\n", "    'vertex_location': 'us-central1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'vertex_ai/gemini-2.5-flash-preview-04-17'},\n", "   'model_info': {'id': 'eef4e364e9576680436e7c8e6a4777431bb00ed6ef1025215e818686c6dd5381',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'gemini-2.5-flash-preview-04-17',\n", "    'max_tokens': 65536,\n", "    'max_input_tokens': 1048576,\n", "    'max_output_tokens': 65536,\n", "    'input_cost_per_token': 1.5e-07,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': 1e-06,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 6e-07,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': 3.5e-06,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'vertex_ai-language-models',\n", "    'mode': 'chat',\n", "    'supports_system_messages': True,\n", "    'supports_response_schema': True,\n", "    'supports_vision': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['temperature',\n", "     'top_p',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'tools',\n", "     'functions',\n", "     'tool_choice',\n", "     'response_format',\n", "     'n',\n", "     'stop',\n", "     'frequency_penalty',\n", "     'presence_penalty',\n", "     'extra_headers',\n", "     'seed',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'modalities',\n", "     'reasoning_effort',\n", "     'thinking']}},\n", "  {'model_name': 'deepseek-v3',\n", "   'litellm_params': {'api_base': 'https://openrouter.ai/api/v1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'openrouter/deepseek/deepseek-chat-v3-0324'},\n", "   'model_info': {'id': 'f9fa0f6b861e5fe1436fc66d7f99af2d0a35137ed215fbd9df9480bbc4c5b3bd',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'openrouter/deepseek/deepseek-chat-v3-0324',\n", "    'max_tokens': None,\n", "    'max_input_tokens': None,\n", "    'max_output_tokens': None,\n", "    'input_cost_per_token': 0,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 0,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'openrouter',\n", "    'mode': None,\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': <PERSON><PERSON><PERSON>,\n", "    'supports_tool_choice': <PERSON><PERSON><PERSON>,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['frequency_penalty',\n", "     'logit_bias',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'modalities',\n", "     'prediction',\n", "     'n',\n", "     'presence_penalty',\n", "     'seed',\n", "     'stop',\n", "     'stream',\n", "     'stream_options',\n", "     'temperature',\n", "     'top_p',\n", "     'tools',\n", "     'tool_choice',\n", "     'function_call',\n", "     'functions',\n", "     'max_retries',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'audio',\n", "     'response_format']}},\n", "  {'model_name': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:2',\n", "   'litellm_params': {'aws_region_name': 'us-west-2',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    'thinking': {'type': 'enabled', 'budget_tokens': 1024}},\n", "   'model_info': {'id': 'cca597b1fa640e81c8657d69b132396aac7a272e62856ef511abfe7d98e4fa11',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'supports_vision': True,\n", "    'key': 'anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 200000,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 3e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 1.5e-05,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'bedrock_converse',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': True,\n", "    'supports_prompt_caching': True,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'stream_options',\n", "     'stop',\n", "     'temperature',\n", "     'top_p',\n", "     'extra_headers',\n", "     'response_format',\n", "     'tools',\n", "     'thinking',\n", "     'reasoning_effort']}},\n", "  {'model_name': 'claude-3.7-sonnet-think',\n", "   'litellm_params': {'api_base': 'https://openrouter.ai/api/v1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'openrouter/anthropic/claude-3.7-sonnet:thinking'},\n", "   'model_info': {'id': 'e694cfa752fea0b910db41910f710ca7c30d39302d702ac46365f580117c7221',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'openrouter/anthropic/claude-3.7-sonnet:thinking',\n", "    'max_tokens': None,\n", "    'max_input_tokens': None,\n", "    'max_output_tokens': None,\n", "    'input_cost_per_token': 0,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 0,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'openrouter',\n", "    'mode': None,\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': <PERSON><PERSON><PERSON>,\n", "    'supports_tool_choice': <PERSON><PERSON><PERSON>,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['frequency_penalty',\n", "     'logit_bias',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'modalities',\n", "     'prediction',\n", "     'n',\n", "     'presence_penalty',\n", "     'seed',\n", "     'stop',\n", "     'stream',\n", "     'stream_options',\n", "     'temperature',\n", "     'top_p',\n", "     'tools',\n", "     'tool_choice',\n", "     'function_call',\n", "     'functions',\n", "     'max_retries',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'audio',\n", "     'response_format']}},\n", "  {'model_name': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "   'litellm_params': {'aws_region_name': 'us-west-2',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0'},\n", "   'model_info': {'id': '2fd74bdb01c9bce40197145af714bbd1bf9ef031a5065f2bedd331370991a2a6',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'supports_vision': True,\n", "    'key': 'anthropic.claude-3-7-sonnet-20250219-v1:0',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 200000,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 3e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 1.5e-05,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'bedrock_converse',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': True,\n", "    'supports_prompt_caching': True,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'stream_options',\n", "     'stop',\n", "     'temperature',\n", "     'top_p',\n", "     'extra_headers',\n", "     'response_format',\n", "     'tools',\n", "     'thinking',\n", "     'reasoning_effort']}},\n", "  {'model_name': 'bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0',\n", "   'litellm_params': {'aws_region_name': 'us-west-2',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'bedrock/us.anthropic.claude-3-5-sonnet-20241022-v2:0'},\n", "   'model_info': {'id': '0156d882b084623b2a4ececf629894d1a7a14a3d856808c614d38fb87188534d',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'anthropic.claude-3-5-sonnet-20241022-v2:0',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 200000,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 3e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 1.5e-05,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'bedrock',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': True,\n", "    'supports_vision': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': True,\n", "    'supports_prompt_caching': True,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': True,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'stream_options',\n", "     'stop',\n", "     'temperature',\n", "     'top_p',\n", "     'extra_headers',\n", "     'response_format',\n", "     'tools']}},\n", "  {'model_name': 'gemini-2.0-flash-001',\n", "   'litellm_params': {'vertex_project': 'clacky-456208',\n", "    'vertex_location': 'us-central1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'vertex_ai/gemini-2.0-flash-001'},\n", "   'model_info': {'id': '19c3cd0ef4b83a42874a515edea99fd40327e319ef911b485cbb5d29d2b64e21',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'gemini-2.0-flash-001',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 1048576,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 1.5e-07,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': 1e-06,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 6e-07,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'vertex_ai-language-models',\n", "    'mode': 'chat',\n", "    'supports_system_messages': True,\n", "    'supports_response_schema': True,\n", "    'supports_vision': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': True,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['temperature',\n", "     'top_p',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'tools',\n", "     'functions',\n", "     'tool_choice',\n", "     'response_format',\n", "     'n',\n", "     'stop',\n", "     'frequency_penalty',\n", "     'presence_penalty',\n", "     'extra_headers',\n", "     'seed',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'modalities']}},\n", "  {'model_name': 'gemini-2.5-pro-preview',\n", "   'litellm_params': {'vertex_project': 'clacky-456208',\n", "    'vertex_location': 'us-central1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'vertex_ai/gemini-2.5-pro-preview-03-25'},\n", "   'model_info': {'id': 'f4ea4a73c8f2eabf8301664a0468925fc1afccd798537a82575558071c74bc45',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'gemini-2.5-pro-preview-03-25',\n", "    'max_tokens': 65536,\n", "    'max_input_tokens': 1048576,\n", "    'max_output_tokens': 65536,\n", "    'input_cost_per_token': 1.25e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': 2.5e-06,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': 1.25e-06,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 1e-05,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': 1.5e-05,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'vertex_ai-language-models',\n", "    'mode': 'chat',\n", "    'supports_system_messages': True,\n", "    'supports_response_schema': True,\n", "    'supports_vision': True,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['temperature',\n", "     'top_p',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'tools',\n", "     'functions',\n", "     'tool_choice',\n", "     'response_format',\n", "     'n',\n", "     'stop',\n", "     'frequency_penalty',\n", "     'presence_penalty',\n", "     'extra_headers',\n", "     'seed',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'modalities',\n", "     'reasoning_effort',\n", "     'thinking']}},\n", "  {'model_name': 'deepseek-r1',\n", "   'litellm_params': {'api_base': 'https://openrouter.ai/api/v1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'openrouter/deepseek/deepseek-r1'},\n", "   'model_info': {'id': 'e4f9c3c3361ec0daf1504c46d6677a4a9ef7cadaa9db9e62cd8201ea7750cd60',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'openrouter/deepseek/deepseek-r1',\n", "    'max_tokens': 8192,\n", "    'max_input_tokens': 65336,\n", "    'max_output_tokens': 8192,\n", "    'input_cost_per_token': 5.5e-07,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 2.19e-06,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'openrouter',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': True,\n", "    'supports_prompt_caching': True,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['frequency_penalty',\n", "     'logit_bias',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'modalities',\n", "     'prediction',\n", "     'n',\n", "     'presence_penalty',\n", "     'seed',\n", "     'stop',\n", "     'stream',\n", "     'stream_options',\n", "     'temperature',\n", "     'top_p',\n", "     'tools',\n", "     'tool_choice',\n", "     'function_call',\n", "     'functions',\n", "     'max_retries',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'audio',\n", "     'response_format']}},\n", "  {'model_name': 'gemini-2.0-flash-thinking-exp',\n", "   'litellm_params': {'vertex_project': 'clacky-456208',\n", "    'vertex_location': 'us-central1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'vertex_ai/gemini-2.0-flash-thinking-exp-01-21'},\n", "   'model_info': {'id': '0ae0535582113d5c7ae2079afaddb0d3151e42263efb0204d67fc18228459f49',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'gemini-2.0-flash-thinking-exp-01-21',\n", "    'max_tokens': 65536,\n", "    'max_input_tokens': 1048576,\n", "    'max_output_tokens': 65536,\n", "    'input_cost_per_token': 0,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': 0,\n", "    'input_cost_per_token_above_128k_tokens': 0,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 0,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': 0,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': 0,\n", "    'output_cost_per_character_above_128k_tokens': 0,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'vertex_ai-language-models',\n", "    'mode': 'chat',\n", "    'supports_system_messages': True,\n", "    'supports_response_schema': <PERSON><PERSON><PERSON>,\n", "    'supports_vision': True,\n", "    'supports_function_calling': <PERSON><PERSON><PERSON>,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['temperature',\n", "     'top_p',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'stream',\n", "     'tools',\n", "     'functions',\n", "     'tool_choice',\n", "     'response_format',\n", "     'n',\n", "     'stop',\n", "     'frequency_penalty',\n", "     'presence_penalty',\n", "     'extra_headers',\n", "     'seed',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'modalities']}},\n", "  {'model_name': 'gemini-2.5-pro-exp',\n", "   'litellm_params': {'api_base': 'https://openrouter.ai/api/v1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'openrouter/google/gemini-2.5-pro-exp-03-25:free'},\n", "   'model_info': {'id': '0ad2ce65f2e2ded4ad422b1d894a7efbf89772c9c66370f5e2639e1997c1e731',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'openrouter/google/gemini-2.5-pro-exp-03-25:free',\n", "    'max_tokens': None,\n", "    'max_input_tokens': None,\n", "    'max_output_tokens': None,\n", "    'input_cost_per_token': 0,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 0,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'openrouter',\n", "    'mode': None,\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': <PERSON><PERSON><PERSON>,\n", "    'supports_tool_choice': <PERSON><PERSON><PERSON>,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': <PERSON><PERSON><PERSON>,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['frequency_penalty',\n", "     'logit_bias',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'modalities',\n", "     'prediction',\n", "     'n',\n", "     'presence_penalty',\n", "     'seed',\n", "     'stop',\n", "     'stream',\n", "     'stream_options',\n", "     'temperature',\n", "     'top_p',\n", "     'tools',\n", "     'tool_choice',\n", "     'function_call',\n", "     'functions',\n", "     'max_retries',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'audio',\n", "     'response_format']}},\n", "  {'model_name': 'o3-mini',\n", "   'litellm_params': {'api_base': 'https://openrouter.ai/api/v1',\n", "    'use_in_pass_through': False,\n", "    'merge_reasoning_content_in_choices': False,\n", "    'model': 'openrouter/openai/o3-mini'},\n", "   'model_info': {'id': 'c14204d167f67e0491d5e3bd71b95dc9c7ec688ecb6063c38e1a98a538bcb378',\n", "    'db_model': <PERSON><PERSON><PERSON>,\n", "    'key': 'openrouter/openai/o3-mini',\n", "    'max_tokens': 65536,\n", "    'max_input_tokens': 128000,\n", "    'max_output_tokens': 65536,\n", "    'input_cost_per_token': 1.1e-06,\n", "    'cache_creation_input_token_cost': None,\n", "    'cache_read_input_token_cost': None,\n", "    'input_cost_per_character': None,\n", "    'input_cost_per_token_above_128k_tokens': None,\n", "    'input_cost_per_token_above_200k_tokens': None,\n", "    'input_cost_per_query': None,\n", "    'input_cost_per_second': None,\n", "    'input_cost_per_audio_token': None,\n", "    'input_cost_per_token_batches': None,\n", "    'output_cost_per_token_batches': None,\n", "    'output_cost_per_token': 4.4e-06,\n", "    'output_cost_per_audio_token': None,\n", "    'output_cost_per_character': None,\n", "    'output_cost_per_reasoning_token': None,\n", "    'output_cost_per_token_above_128k_tokens': None,\n", "    'output_cost_per_character_above_128k_tokens': None,\n", "    'output_cost_per_token_above_200k_tokens': None,\n", "    'output_cost_per_second': None,\n", "    'output_cost_per_image': None,\n", "    'output_vector_size': None,\n", "    'litellm_provider': 'openrouter',\n", "    'mode': 'chat',\n", "    'supports_system_messages': None,\n", "    'supports_response_schema': None,\n", "    'supports_vision': <PERSON><PERSON><PERSON>,\n", "    'supports_function_calling': True,\n", "    'supports_tool_choice': True,\n", "    'supports_assistant_prefill': <PERSON><PERSON><PERSON>,\n", "    'supports_prompt_caching': False,\n", "    'supports_audio_input': <PERSON><PERSON><PERSON>,\n", "    'supports_audio_output': <PERSON><PERSON><PERSON>,\n", "    'supports_pdf_input': False,\n", "    'supports_embedding_image_input': False,\n", "    'supports_native_streaming': None,\n", "    'supports_web_search': <PERSON><PERSON><PERSON>,\n", "    'supports_reasoning': True,\n", "    'search_context_cost_per_query': None,\n", "    'tpm': None,\n", "    'rpm': None,\n", "    'supported_openai_params': ['frequency_penalty',\n", "     'logit_bias',\n", "     'logprobs',\n", "     'top_logprobs',\n", "     'max_tokens',\n", "     'max_completion_tokens',\n", "     'modalities',\n", "     'prediction',\n", "     'n',\n", "     'presence_penalty',\n", "     'seed',\n", "     'stop',\n", "     'stream',\n", "     'stream_options',\n", "     'temperature',\n", "     'top_p',\n", "     'tools',\n", "     'tool_choice',\n", "     'function_call',\n", "     'functions',\n", "     'max_retries',\n", "     'extra_headers',\n", "     'parallel_tool_calls',\n", "     'audio',\n", "     'response_format']}}]}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["model_info"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}