{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23:06:07 heracles:INFO:logger.py:74 Heracles is starting, current log_level is INFO\n", "23:06:10 heracles:INFO:llm.py:31 langfuse is enabled, load config\n", "23:06:10 heracles:INFO:redis_cache.py:44 [cache] feature is disabled\n"]}], "source": ["from heracles.agent_workspace.paas_sdk.utils import get_environments, create_codezone, upload_files, import_codezone_file, get_playground_id, fork_codezone, delete_codezone\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["res = await get_environments()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["'403710022024036354'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["env_id = None\n", "for d in res['data']:\n", "    if d['name'].startswith('Python 3.11'):\n", "        env_id = d['id']\n", "        break\n", "env_id"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True, 'data': {'id': '761042689348308992'}, 'error': None}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["res = await create_codezone('403710022024036354')\n", "res"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[31m10:32:20 heracles:ERROR:utils.py:66 {'status': 'failure', 'errors': [{'field': None, 'errmsg': None, 'errcode': 'CodeZone(ID:760902970966700032)不存在'}]}\u001b[0m\n"]}, {"data": {"text/plain": ["{'success': <PERSON><PERSON><PERSON>,\n", " 'data': {'status': 'failure',\n", "  'errors': [{'field': None,\n", "    'errmsg': None,\n", "    'errcode': 'CodeZone(ID:760902970966700032)不存在'}]},\n", " 'error': None}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["res = await get_playground_id('760902970966700032')\n", "res"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True,\n", " 'data': {'id': '766537275109781504',\n", "  'environmentVerId': '403710022024036354',\n", "  'environmentName': 'Python ( bash )',\n", "  'startCmd': None},\n", " 'error': None}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["res = await fork_codezone('762106514419978240')\n", "res"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'success': True, 'data': {'id': '762106517557280768'}, 'error': None}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["res = await get_playground_id('762106514419978240')\n", "res"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["res = await delete_codezone('760916713574395904')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1738835921708\n", "qgQYtQPj\n", "LKnnKxjcw+SMemmoxKXWaAr2Z423V9P7pJqq7+TrmT0=\n"]}], "source": ["import time\n", "import random\n", "import string\n", "from Crypto.Cipher import AES\n", "import base64\n", "import aiohttp\n", "from typing import TypedDict, Any, Dict, Optional\n", "from heracles.core.config import get_env_var\n", "from heracles.core.logger import heracles_logger as logger\n", "\n", "PAAS_DOMAIN_URL = get_env_var('PAAS_DOMAIN_URL', must=True)\n", "PAAS_TENANT_CODE = get_env_var('PAAS_TENANT_CODE', must=True)\n", "\n", "\n", "def generate_token(nonce: str, timestamp: str) -> str:\n", "    \"\"\"Generate token for PAAS API authentication\n", "\n", "    Returns:\n", "        str: The generated token string\n", "    \"\"\"\n", "    # Secret key for encryption\n", "    key = \"demosecret123456\".encode('utf-8')\n", "    # Create message\n", "    msg = f\"{PAAS_TENANT_CODE}_{nonce}_{timestamp}\".encode('utf-8')\n", "\n", "    # Pad message to be multiple of 16 bytes\n", "    pad_len = 16 - (len(msg) % 16)\n", "    msg = msg + bytes([pad_len]) * pad_len\n", "\n", "    # Encrypt using AES ECB mode\n", "    cipher = AES.new(key, AES.MODE_ECB)\n", "    encrypted = cipher.encrypt(msg)\n", "    token = base64.b64encode(encrypted).decode()\n", "\n", "    return token\n", "\n", "\n", "timestamp = str(int(time.time() * 1000))\n", "# 生成 nonce\n", "nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=8))\n", "# 生成 token\n", "token = generate_token(nonce, timestamp)\n", "print(timestamp)\n", "print(nonce)\n", "print(token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import_codezone_file('759113692959969280')"]}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}