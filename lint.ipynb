{"cells": [{"cell_type": "code", "execution_count": null, "id": "bcc4980d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ad333052", "metadata": {}, "outputs": [], "source": ["import requests\n", "url = \"https://5e31bf9f027d96a7a614fd81dbd8b7df-agentserver.develop.clackypaas.com\"\n", "url += \"/agent/lint/diagnostic\"\n", "res = requests.post(url, json={\"path\": [\".keep\"]})\n"]}, {"cell_type": "code", "execution_count": 7, "id": "938e59b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"code\":400,\"msg\":\"No files provided\"}'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["res.text"]}, {"cell_type": "code", "execution_count": null, "id": "1466cbef", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}