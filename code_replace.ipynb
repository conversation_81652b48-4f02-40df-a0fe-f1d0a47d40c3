{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Python-dotenv could not parse statement starting at line 13\n", "18:54:30 heracles:INFO:logger.py:74 Heracles is starting, current log_level is DEBUG\n", "Python-dotenv could not parse statement starting at line 13\n", "Python-dotenv could not parse statement starting at line 13\n", "/Users/<USER>/miniconda3/envs/clackyai/lib/python3.11/site-packages/qdrant_client/async_qdrant_remote.py:117: UserWarning: Api key is used with an insecure connection.\n", "  warnings.warn(\"Api key is used with an insecure connection.\")\n", "18:54:38 heracles:INFO:llm.py:31 langfuse is enabled, load config\n", "18:54:38 heracles:INFO:redis_cache.py:44 [cache] feature is disabled\n"]}], "source": ["from heracles.agent_roles.code_role.utils import match_best_code_segment, generate_file_diff, apply_edited_snippets\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["file_content=\"\"\"\n", "package com.company.project.service.impl;\n", "\n", "import com.company.project.dao.UserMapper;\n", "import com.company.project.model.User;\n", "import com.company.project.service.UserService;\n", "import com.company.project.core.AbstractService;\n", "import org.springframework.stereotype.Service;\n", "import org.springframework.transaction.annotation.Transactional;\n", "\n", "import javax.annotation.Resource;\n", "import java.security.MessageDigest;\n", "import java.security.NoSuchAlgorithmException;\n", "import java.util.List;\n", "import java.util.Comparator;\n", "\n", "/**\n", " * Created by CodeGenerator on 2024/11/18.\n", " */\n", "@Service\n", "@Transactional\n", "public class UserServiceImpl extends AbstractService<User> implements UserService {\n", "    @Resource\n", "    private UserMapper userMapper;\n", "\n", "    public void addUser(User user) {\n", "        user.setRegisterDate(new java.util.Date());\n", "        String encryptedPassword = encryptPassword(user.getPassword());\n", "        user.setPassword(encryptedPassword);\n", "        userMapper.insert(user);\n", "    }\n", "\n", "    public boolean validateCredentials(String username, String password) {\n", "        String encryptedPassword = encryptPassword(password);\n", "        User user = userMapper.findByUsernameAndPassword(username, encryptedPassword);\n", "        boolean validCredentials = user != null;\n", "        return validCredentials;\n", "    }\n", "\n", "    public void save(User user) {\n", "        addUser(user);\n", "    }\n", "\n", "    @Override\n", "    public String generateToken() {\n", "        return java.util.UUID.randomUUID().toString().replace(\"-\", \"\");\n", "    }\n", "\n", "    @Override\n", "    public List<User> fetchSortedUsers() {\n", "        return userMapper.fetchSortedUsers();\n", "    }\n", "    \n", "    private String encryptPassword(String password) {\n", "        try {\n", "            MessageDigest md = MessageDigest.getInstance(\"MD5\");\n", "            md.update(password.getBytes());\n", "            byte[] byteData = md.digest();\n", "\n", "            // Convert byte array to hex format\n", "            StringBuilder sb = new StringBuilder();\n", "            for (byte b : byteData) {\n", "                sb.append(String.format(\"%02x\", b));\n", "            }\n", "            return sb.toString();\n", "        } catch (NoSuchAlgorithmException e) {\n", "            throw new RuntimeException(\"MD5 encryption failed\", e);\n", "        }\n", "    }\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["res = {\n", "    \"tool_calls\": [\n", "        {\n", "            \"index\": 0,\n", "            \"function\": {\n", "                \"arguments\": {\n", "                    \"tasks\": [\n", "                        {\n", "                            \"original\": \"import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;\",\n", "                            \"edited\": \"import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;\\nimport org.springframework.data.redis.core.RedisTemplate;\",\n", "                            \"thought\": \"Add the RedisTemplate import to use Redis operations.\"\n", "                        },\n", "                        {\n", "                            \"original\": \"    @Resource\\n    private UserMapper userMapper;\",\n", "                            \"edited\": \"    @Resource\\n    private UserMapper userMapper;\\n    \\n    @Resource\\n    private RedisTemplate<String, Object> redisTemplate;\",\n", "                            \"thought\": \"Inject RedisTemplate to interact with Redis.\"\n", "                        },\n", "                        {\n", "                            \"original\": \"    }\\n\",\n", "                            \"edited\": \"    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\\\"user:\\\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\\\"user:\\\" + userId);\\n    }\\n\",\n", "                            \"thought\": \"Add methods saveUserToRedis and getUserFromRedis for Redis storage and retrieval.\"\n", "                        }\n", "                    ]\n", "                },\n", "                \"name\": \"IterableEditedFileSnippet\"\n", "            },\n", "            \"id\": \"call_pnixzpABzHuWWpPBNANzajYT\",\n", "            \"type\": \"function\"\n", "        }\n", "    ],\n", "    \"function_call\": None\n", "}"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from heracles.core.schema import LLMAbilityType, FileSnippet, EditedFileSnippet\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["edited_snippets = []\n", "for data in res['tool_calls'][0]['function']['arguments']['tasks']:\n", "    edited_snippets.append(EditedFileSnippet.model_validate(data))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'original': 'import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;', 'edited': 'import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;\\nimport org.springframework.data.redis.core.RedisTemplate;', 'thought': 'Add the RedisTemplate import to use Redis operations.'}\n", "{'original': '    @Resource\\n    private UserMapper userMapper;', 'edited': '    @Resource\\n    private UserMapper userMapper;\\n    \\n    @Resource\\n    private RedisTemplate<String, Object> redisTemplate;', 'thought': 'Inject RedisTemplate to interact with Red<PERSON>.'}\n", "{'original': '    }\\n', 'edited': '    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n', 'thought': 'Add methods saveUserToRedis and getUserFromRedis for Redis storage and retrieval.'}\n"]}], "source": ["for es in edited_snippets:\n", "    print(es.model_dump())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["selected_snippets = [FileSnippet(content=snippet.original.strip('\\n')) for snippet in edited_snippets]\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[FileSnippet(path='', content='import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;', row_start=-1, row_end=-1),\n", " FileSnippet(path='', content='    @Resource\\n    private UserMapper userMapper;', row_start=-1, row_end=-1),\n", " FileSnippet(path='', content='    }', row_start=-1, row_end=-1)]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_snippets"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[FileSnippet(path='', content='import com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;', row_start=-1, row_end=-1),\n", " FileSnippet(path='', content='    @Resource\\n    private UserMapper userMapper;', row_start=-1, row_end=-1),\n", " FileSnippet(path='', content='    }', row_start=-1, row_end=-1)]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_snippets"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def match_code_snippets(original_file_content: str, selected_snippets: list) -> list[FileSnippet]:\n", "    matched_snippets: list = []\n", "    for snippet in selected_snippets:\n", "        file_snippet = match_best_code_segment(original_file_content, snippet.content)\n", "        if file_snippet.row_start == -1:\n", "            raise Exception('[AI] Code snippets to edit cannot be found, please try again.')\n", "        if file_snippet:\n", "            # 检查是否有包含关系\n", "            is_contained = False\n", "            for existing_snippet in matched_snippets:\n", "                if (file_snippet.row_start >= existing_snippet.row_start and\n", "                    file_snippet.row_end <= existing_snippet.row_end):\n", "                    is_contained = True\n", "                    break\n", "                elif (file_snippet.row_start <= existing_snippet.row_start and\n", "                      file_snippet.row_end >= existing_snippet.row_end):\n", "                    matched_snippets.remove(existing_snippet)\n", "            if not is_contained:\n", "                matched_snippets.append(file_snippet)\n", "    return matched_snippets"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["matched_snippets = match_code_snippets(file_content, selected_snippets)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def generate_edited_snippets(original_file_content: str, matched_snippets: list[FileSnippet], new_snippets: list[FileSnippet]):\n", "    if len(matched_snippets) != len(new_snippets):\n", "        raise Exception('[AI] Edited code snippets do not match the original count, please try again.')\n", "    for i, snippet in enumerate(matched_snippets):\n", "        new_snippet = new_snippets[i]\n", "        new_snippet.row_start = snippet.row_start\n", "        new_snippet.row_end = snippet.row_end\n", "    return new_snippets"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["edited_snippets = [FileSnippet(content=snippet.edited.strip('\\n')) for snippet in edited_snippets]\n", "edited_file_snippets = generate_edited_snippets(file_content, matched_snippets, edited_snippets)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'path': '',\n", " 'content': '    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }',\n", " 'row_start': 30,\n", " 'row_end': 30}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["edited_file_snippets[2].model_dump()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "package com.company.project.service.impl;\n", "\n", "import com.company.project.dao.UserMapper;\n", "import com.company.project.model.User;\n", "import com.company.project.service.UserService;\n", "import com.company.project.core.AbstractService;\n", "import org.springframework.stereotype.Service;\n", "import org.springframework.transaction.annotation.Transactional;\n", "\n", "import javax.annotation.Resource;\n", "import java.security.MessageDigest;\n", "import java.security.NoSuchAlgorithmException;\n", "import java.util.List;\n", "import java.util.Comparator;\n", "\n", "/**\n", " * Created by CodeGenerator on 2024/11/18.\n", " */\n", "@Service\n", "@Transactional\n", "public class UserServiceImpl extends AbstractService<User> implements UserService {\n", "    @Resource\n", "    private UserMapper userMapper;\n", "\n", "    public void addUser(User user) {\n", "        user.setRegisterDate(new java.util.Date());\n", "        String encryptedPassword = encryptPassword(user.getPassword());\n", "        user.setPassword(encryptedPassword);\n", "        userMapper.insert(user);\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "\n", "    public boolean validateCredentials(String username, String password) {\n", "        String encryptedPassword = encryptPassword(password);\n", "        User user = userMapper.findByUsernameAndPassword(username, encryptedPassword);\n", "        boolean validCredentials = user != null;\n", "        return validCredentials;\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "\n", "    public void save(User user) {\n", "        addUser(user);\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "\n", "    @Override\n", "    public String generateToken() {\n", "        return java.util.UUID.randomUUID().toString().replace(\"-\", \"\");\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "\n", "    @Override\n", "    public List<User> fetchSortedUsers() {\n", "        return userMapper.fetchSortedUsers();\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "    \n", "    private String encryptPassword(String password) {\n", "        try {\n", "            MessageDigest md = MessageDigest.getInstance(\"MD5\");\n", "            md.update(password.getBytes());\n", "            byte[] byteData = md.digest();\n", "\n", "            // Convert byte array to hex format\n", "            StringBuilder sb = new StringBuilder();\n", "            for (byte b : byteData) {\n", "                sb.append(String.format(\"%02x\", b));\n", "            }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "            return sb.toString();\n", "        }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    } catch (NoSuchAlgorithmException e) {\n", "            throw new RuntimeException(\"MD5 encryption failed\", e);\n", "        }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "    }\n", "    \n", "    public void saveUserToRedis(User user) {\n", "        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\n", "    }\n", "\n", "    public User getUserFromRedis(String userId) {\n", "        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\n", "    }\n", "}\n", "\n"]}], "source": ["new_file_content = apply_edited_snippets(file_content, edited_file_snippets[2:])\n", "print(new_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\npackage com.company.project.service.impl;\\n\\nimport com.company.project.dao.UserMapper;\\nimport com.company.project.model.User;\\nimport com.company.project.service.UserService;\\nimport com.company.project.core.AbstractService;\\nimport org.springframework.stereotype.Service;\\nimport org.springframework.transaction.annotation.Transactional;\\n\\nimport javax.annotation.Resource;\\nimport java.security.MessageDigest;\\nimport java.security.NoSuchAlgorithmException;\\nimport java.util.List;\\nimport java.util.Comparator;\\n\\n/**\\n * Created by CodeGenerator on 2024/11/18.\\n */\\n@Service\\n@Transactional\\npublic class UserServiceImpl extends AbstractService<User> implements UserService {\\n    @Resource\\n    private UserMapper userMapper;\\n\\n    public void addUser(User user) {\\n        user.setRegisterDate(new java.util.Date());\\n        String encryptedPassword = encryptPassword(user.getPassword());\\n        user.setPassword(encryptedPassword);\\n        userMapper.insert(user);\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n\\n    public boolean validateCredentials(String username, String password) {\\n        String encryptedPassword = encryptPassword(password);\\n        User user = userMapper.findByUsernameAndPassword(username, encryptedPassword);\\n        boolean validCredentials = user != null;\\n        return validCredentials;\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n\\n    public void save(User user) {\\n        addUser(user);\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n\\n    @Override\\n    public String generateToken() {\\n        return java.util.UUID.randomUUID().toString().replace(\"-\", \"\");\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n\\n    @Override\\n    public List<User> fetchSortedUsers() {\\n        return userMapper.fetchSortedUsers();\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n    \\n    private String encryptPassword(String password) {\\n        try {\\n            MessageDigest md = MessageDigest.getInstance(\"MD5\");\\n            md.update(password.getBytes());\\n            byte[] byteData = md.digest();\\n\\n            // Convert byte array to hex format\\n            StringBuilder sb = new StringBuilder();\\n            for (byte b : byteData) {\\n                sb.append(String.format(\"%02x\", b));\\n            }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n            return sb.toString();\\n        }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    } catch (NoSuchAlgorithmException e) {\\n            throw new RuntimeException(\"MD5 encryption failed\", e);\\n        }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n    }\\n    \\n    public void saveUserToRedis(User user) {\\n        redisTemplate.opsForValue().set(\"user:\" + user.getId(), user);\\n    }\\n\\n    public User getUserFromRedis(String userId) {\\n        return (User) redisTemplate.opsForValue().get(\"user:\" + userId);\\n    }\\n}\\n'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["new_file_content"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import re\n", "res = re.findall(r'public void saveUserToRedis(User user)', new_file_content)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["new_file_content.count('public void saveUserToRedis(User user)')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "clacky<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}